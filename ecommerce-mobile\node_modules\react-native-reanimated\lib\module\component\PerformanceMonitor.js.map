{"version": 3, "names": ["React", "useEffect", "useRef", "TextInput", "StyleSheet", "View", "useSharedValue", "useAnimatedProps", "useFrameCallback", "createAnimatedComponent", "addWhitelistedNativeProps", "jsx", "_jsx", "jsxs", "_jsxs", "createCircularDoublesBuffer", "size", "next", "buffer", "Float32Array", "count", "push", "value", "oldValue", "oldCount", "Math", "min", "front", "notEmpty", "current", "index", "back", "DEFAULT_BUFFER_SIZE", "text", "AnimatedTextInput", "loopAnimationFrame", "fn", "lastTime", "loop", "requestAnimationFrame", "time", "getFps", "renderTimeInMs", "completeBufferRoutine", "timestamp", "round", "droppedTimestamp", "measuredRangeDuration", "JsPerformance", "smoothingFrames", "jsFps", "totalRenderTime", "circular<PERSON>uffer", "_", "currentFps", "toFixed", "animatedProps", "defaultValue", "style", "styles", "container", "children", "editable", "UiPerformance", "uiFps", "PerformanceMonitor", "monitor", "create", "flexDirection", "position", "backgroundColor", "zIndex", "header", "fontSize", "color", "paddingHorizontal", "fontFamily", "alignItems", "justifyContent", "flexWrap"], "sourceRoot": "../../../src", "sources": ["component/PerformanceMonitor.tsx"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,SAAS,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAG1D,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,kBAAS;AAC5E,SAASC,uBAAuB,QAAQ,qCAA4B;AACpE,SAASC,yBAAyB,QAAQ,oBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAG5D,SAASC,2BAA2BA,CAACC,IAAY,EAAE;EACjD,SAAS;;EAET,OAAO;IACLC,IAAI,EAAE,CAAW;IACjBC,MAAM,EAAE,IAAIC,YAAY,CAACH,IAAI,CAAC;IAC9BA,IAAI;IACJI,KAAK,EAAE,CAAW;IAElBC,IAAIA,CAACC,KAAa,EAAiB;MACjC,MAAMC,QAAQ,GAAG,IAAI,CAACL,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC;MACvC,MAAMO,QAAQ,GAAG,IAAI,CAACJ,KAAK;MAC3B,IAAI,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAGK,KAAK;MAE9B,IAAI,CAACL,IAAI,GAAG,CAAC,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAACD,IAAI;MACvC,IAAI,CAACI,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,IAAI,CAACV,IAAI,EAAE,IAAI,CAACI,KAAK,GAAG,CAAC,CAAC;MAChD,OAAOI,QAAQ,KAAK,IAAI,CAACR,IAAI,GAAGO,QAAQ,GAAG,IAAI;IACjD,CAAC;IAEDI,KAAKA,CAAA,EAAkB;MACrB,MAAMC,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC;MAC/B,IAAIQ,QAAQ,EAAE;QACZ,MAAMC,OAAO,GAAG,IAAI,CAACZ,IAAI,GAAG,CAAC;QAC7B,MAAMa,KAAK,GAAGD,OAAO,GAAG,CAAC,GAAG,IAAI,CAACb,IAAI,GAAG,CAAC,GAAGa,OAAO;QACnD,OAAO,IAAI,CAACX,MAAM,CAACY,KAAK,CAAC;MAC3B;MACA,OAAO,IAAI;IACb,CAAC;IAEDC,IAAIA,CAAA,EAAkB;MACpB,MAAMH,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC;MAC/B,OAAOQ,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAG,IAAI;IACjD;EACF,CAAC;AACH;AAEA,MAAMe,mBAAmB,GAAG,EAAE;AAC9BtB,yBAAyB,CAAC;EAAEuB,IAAI,EAAE;AAAK,CAAC,CAAC;AACzC,MAAMC,iBAAiB,GAAGzB,uBAAuB,CAACN,SAAS,CAAC;AAE5D,SAASgC,kBAAkBA,CAACC,EAA4C,EAAE;EACxE,IAAIC,QAAQ,GAAG,CAAC;EAEhB,SAASC,IAAIA,CAAA,EAAG;IACdC,qBAAqB,CAAEC,IAAI,IAAK;MAC9B,IAAIH,QAAQ,GAAG,CAAC,EAAE;QAChBD,EAAE,CAACC,QAAQ,EAAEG,IAAI,CAAC;MACpB;MACAH,QAAQ,GAAGG,IAAI;MACfD,qBAAqB,CAACD,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAA,IAAI,CAAC,CAAC;AACR;AAEA,SAASG,MAAMA,CAACC,cAAsB,EAAU;EAC9C,SAAS;;EACT,OAAO,IAAI,GAAGA,cAAc;AAC9B;AAEA,SAASC,qBAAqBA,CAC5BzB,MAAsB,EACtB0B,SAAiB,EACT;EACR,SAAS;;EACTA,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACD,SAAS,CAAC;EAEjC,MAAME,gBAAgB,GAAG5B,MAAM,CAACG,IAAI,CAACuB,SAAS,CAAC,IAAIA,SAAS;EAE5D,MAAMG,qBAAqB,GAAGH,SAAS,GAAGE,gBAAgB;EAE1D,OAAOL,MAAM,CAACM,qBAAqB,GAAG7B,MAAM,CAACE,KAAK,CAAC;AACrD;AAEA,SAAS4B,aAAaA,CAAC;EAAEC;AAA6C,CAAC,EAAE;EACvE,MAAMC,KAAK,GAAG5C,cAAc,CAAgB,IAAI,CAAC;EACjD,MAAM6C,eAAe,GAAG7C,cAAc,CAAC,CAAC,CAAC;EACzC,MAAM8C,cAAc,GAAGlD,MAAM,CAC3Ba,2BAA2B,CAACkC,eAAe,CAC7C,CAAC;EAEDhD,SAAS,CAAC,MAAM;IACdkC,kBAAkB,CAAC,CAACkB,CAAC,EAAET,SAAS,KAAK;MACnCA,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACD,SAAS,CAAC;MAEjC,MAAMU,UAAU,GAAGX,qBAAqB,CACtCS,cAAc,CAACvB,OAAO,EACtBe,SACF,CAAC;;MAED;MACA;MACAM,KAAK,CAAC5B,KAAK,GAAG,CAACgC,UAAU,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,KAAK,EAAEC,eAAe,CAAC,CAAC;EAE5B,MAAMK,aAAa,GAAGjD,gBAAgB,CAAC,MAAM;IAC3C,MAAM0B,IAAI,GAAG,MAAM,IAAIiB,KAAK,CAAC5B,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;IAClD,OAAO;MAAEW,IAAI;MAAEwB,YAAY,EAAExB;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,oBACErB,IAAA,CAACP,IAAI;IAACqD,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,eAC5BjD,IAAA,CAACsB,iBAAiB;MAChBwB,KAAK,EAAEC,MAAM,CAAC1B,IAAK;MACnBuB,aAAa,EAAEA,aAAc;MAC7BM,QAAQ,EAAE;IAAM,CACjB;EAAC,CACE,CAAC;AAEX;AAEA,SAASC,aAAaA,CAAC;EAAEd;AAA6C,CAAC,EAAE;EACvE,MAAMe,KAAK,GAAG1D,cAAc,CAAgB,IAAI,CAAC;EACjD,MAAM8C,cAAc,GAAG9C,cAAc,CAAwB,IAAI,CAAC;EAElEE,gBAAgB,CAAC,CAAC;IAAEoC;EAAqB,CAAC,KAAK;IAC7C,IAAIQ,cAAc,CAAC9B,KAAK,KAAK,IAAI,EAAE;MACjC8B,cAAc,CAAC9B,KAAK,GAAGP,2BAA2B,CAACkC,eAAe,CAAC;IACrE;IAEAL,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACD,SAAS,CAAC;IAEjC,MAAMU,UAAU,GAAGX,qBAAqB,CAACS,cAAc,CAAC9B,KAAK,EAAEsB,SAAS,CAAC;IAEzEoB,KAAK,CAAC1C,KAAK,GAAGgC,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGjD,gBAAgB,CAAC,MAAM;IAC3C,MAAM0B,IAAI,GAAG,MAAM,IAAI+B,KAAK,CAAC1C,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;IAClD,OAAO;MAAEW,IAAI;MAAEwB,YAAY,EAAExB;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,oBACErB,IAAA,CAACP,IAAI;IAACqD,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,eAC5BjD,IAAA,CAACsB,iBAAiB;MAChBwB,KAAK,EAAEC,MAAM,CAAC1B,IAAK;MACnBuB,aAAa,EAAEA,aAAc;MAC7BM,QAAQ,EAAE;IAAM,CACjB;EAAC,CACE,CAAC;AAEX;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAAC;EACjChB,eAAe,GAAGjB;AACK,CAAC,EAAE;EAC1B,oBACElB,KAAA,CAACT,IAAI;IAACqD,KAAK,EAAEC,MAAM,CAACO,OAAQ;IAAAL,QAAA,gBAC1BjD,IAAA,CAACoC,aAAa;MAACC,eAAe,EAAEA;IAAgB,CAAE,CAAC,eACnDrC,IAAA,CAACmD,aAAa;MAACd,eAAe,EAAEA;IAAgB,CAAE,CAAC;EAAA,CAC/C,CAAC;AAEX;AAEA,MAAMU,MAAM,GAAGvD,UAAU,CAAC+D,MAAM,CAAC;EAC/BD,OAAO,EAAE;IACPE,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,OAAO;IACxBC,MAAM,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdC,iBAAiB,EAAE;EACrB,CAAC;EACD1C,IAAI,EAAE;IACJwC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdE,UAAU,EAAE,WAAW;IACvBD,iBAAiB,EAAE;EACrB,CAAC;EACDf,SAAS,EAAE;IACTiB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,aAAa,EAAE,KAAK;IACpBW,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}