/**
 * @license lucide-react-native v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

'use strict';

var createLucideIcon = require('../createLucideIcon.js');

const SquarePlay = createLucideIcon("SquarePlay", [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "m9 8 6 4-6 4Z", key: "f1r3lt" }]
]);

module.exports = SquarePlay;
//# sourceMappingURL=square-play.js.map
