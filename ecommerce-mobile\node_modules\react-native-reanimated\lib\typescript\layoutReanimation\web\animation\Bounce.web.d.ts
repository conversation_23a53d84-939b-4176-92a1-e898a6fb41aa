export declare const BounceInData: {
    BounceIn: {
        name: string;
        style: {
            0: {
                transform: {
                    scale: number;
                }[];
            };
            55: {
                transform: {
                    scale: number;
                }[];
            };
            70: {
                transform: {
                    scale: number;
                }[];
            };
            85: {
                transform: {
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    BounceInRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            55: {
                transform: {
                    translateX: string;
                }[];
            };
            70: {
                transform: {
                    translateX: string;
                }[];
            };
            85: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    BounceInLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            55: {
                transform: {
                    translateX: string;
                }[];
            };
            70: {
                transform: {
                    translateX: string;
                }[];
            };
            85: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    BounceInUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            55: {
                transform: {
                    translateY: string;
                }[];
            };
            70: {
                transform: {
                    translateY: string;
                }[];
            };
            85: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    BounceInDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            55: {
                transform: {
                    translateY: string;
                }[];
            };
            70: {
                transform: {
                    translateY: string;
                }[];
            };
            85: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const BounceOutData: {
    BounceOut: {
        name: string;
        style: {
            0: {
                transform: {
                    scale: number;
                }[];
            };
            15: {
                transform: {
                    scale: number;
                }[];
            };
            30: {
                transform: {
                    scale: number;
                }[];
            };
            45: {
                transform: {
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    BounceOutRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            15: {
                transform: {
                    translateX: string;
                }[];
            };
            30: {
                transform: {
                    translateX: string;
                }[];
            };
            45: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    BounceOutLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            15: {
                transform: {
                    translateX: string;
                }[];
            };
            30: {
                transform: {
                    translateX: string;
                }[];
            };
            45: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    BounceOutUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            15: {
                transform: {
                    translateY: string;
                }[];
            };
            30: {
                transform: {
                    translateY: string;
                }[];
            };
            45: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    BounceOutDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            15: {
                transform: {
                    translateY: string;
                }[];
            };
            30: {
                transform: {
                    translateY: string;
                }[];
            };
            45: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const BounceIn: {
    BounceIn: {
        style: string;
        duration: number;
    };
    BounceInRight: {
        style: string;
        duration: number;
    };
    BounceInLeft: {
        style: string;
        duration: number;
    };
    BounceInUp: {
        style: string;
        duration: number;
    };
    BounceInDown: {
        style: string;
        duration: number;
    };
};
export declare const BounceOut: {
    BounceOut: {
        style: string;
        duration: number;
    };
    BounceOutRight: {
        style: string;
        duration: number;
    };
    BounceOutLeft: {
        style: string;
        duration: number;
    };
    BounceOutUp: {
        style: string;
        duration: number;
    };
    BounceOutDown: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Bounce.web.d.ts.map