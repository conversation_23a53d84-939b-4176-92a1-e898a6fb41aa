{"version": 3, "names": ["registerReanimatedError", "reportFatalErrorOnJS", "isChromeDebugger", "isJest", "shouldBeUseWeb", "runOnJS", "setupMicrotasks", "callMicrotasks", "runOnUIImmediately", "executeOnUIRuntimeSync", "mockedRequestAnimationFrame", "DEFAULT_LOGGER_CONFIG", "logToLogBoxAndConsole", "registerLoggerConfig", "replaceLoggerImplementation", "IS_JEST", "SHOULD_BE_USE_WEB", "IS_CHROME_DEBUGGER", "overrideLogFunctionImplementation", "data", "global", "_WORKLET", "_log", "console", "log", "_getAnimationTimestamp", "performance", "now", "callGuardDEV", "fn", "args", "e", "__E<PERSON><PERSON><PERSON><PERSON>s", "reportFatalError", "setupCallGuard", "__callGuardDEV", "error", "message", "stack", "createMemorySafeCapturableConsole", "consoleCopy", "Object", "fromEntries", "entries", "map", "methodName", "method", "methodWrapper", "name", "defineProperty", "value", "writable", "capturableConsole", "setupConsole", "assert", "debug", "warn", "info", "setupRequestAnimationFrame", "nativeRequestAnimationFrame", "requestAnimationFrame", "animationFrameCallbacks", "flushRequested", "__flushAnimationFrame", "frameTimestamp", "currentCallbacks", "for<PERSON>ach", "f", "callback", "push", "timestamp", "__frameTimestamp", "undefined", "initializeUIRuntime", "globalThis"], "sourceRoot": "../../src", "sources": ["initializers.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,uBAAuB,EAAEC,oBAAoB,QAAQ,aAAU;AACxE,SAASC,gBAAgB,EAAEC,MAAM,EAAEC,cAAc,QAAQ,sBAAmB;AAC5E,SACEC,OAAO,EACPC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,sBAAsB,QACjB,cAAW;AAClB,SAASC,2BAA2B,QAAQ,kCAA+B;AAC3E,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,oBAAoB,EACpBC,2BAA2B,QACtB,mBAAU;AAEjB,MAAMC,OAAO,GAAGZ,MAAM,CAAC,CAAC;AACxB,MAAMa,iBAAiB,GAAGZ,cAAc,CAAC,CAAC;AAC1C,MAAMa,kBAAkB,GAAGf,gBAAgB,CAAC,CAAC;;AAE7C;AACA;AACA;AACA,SAASgB,iCAAiCA,CAAA,EAAG;EAC3C,SAAS;;EACTJ,2BAA2B,CAAEK,IAAI,IAAK;IACpC,SAAS;;IACTd,OAAO,CAACO,qBAAqB,CAAC,CAACO,IAAI,CAAC;EACtC,CAAC,CAAC;AACJ;;AAEA;AACA;AACAN,oBAAoB,CAACF,qBAAqB,CAAC;AAC3CO,iCAAiC,CAAC,CAAC;;AAEnC;AACA,IAAIF,iBAAiB,EAAE;EACrBI,MAAM,CAACC,QAAQ,GAAG,KAAK;EACvBD,MAAM,CAACE,IAAI,GAAGC,OAAO,CAACC,GAAG;EACzBJ,MAAM,CAACK,sBAAsB,GAAG,MAAMC,WAAW,CAACC,GAAG,CAAC,CAAC;AACzD,CAAC,MAAM;EACL;EACA;EACA;EACAlB,sBAAsB,CAACT,uBAAuB,CAAC,CAAC,CAAC;EACjDS,sBAAsB,CAACI,oBAAoB,CAAC,CAACF,qBAAqB,CAAC;EACnEF,sBAAsB,CAACS,iCAAiC,CAAC,CAAC,CAAC;AAC7D;;AAEA;AACA,OAAO,SAASU,YAAYA,CAC1BC,EAAkC,EAClC,GAAGC,IAAU,EACO;EACpB,SAAS;;EACT,IAAI;IACF,OAAOD,EAAE,CAAC,GAAGC,IAAI,CAAC;EACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAIX,MAAM,CAACY,YAAY,EAAE;MACvBZ,MAAM,CAACY,YAAY,CAACC,gBAAgB,CAACF,CAAU,CAAC;IAClD,CAAC,MAAM;MACL,MAAMA,CAAC;IACT;EACF;AACF;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAG;EAC/B,SAAS;;EACTd,MAAM,CAACe,cAAc,GAAGP,YAAY;EACpCR,MAAM,CAACY,YAAY,GAAG;IACpBC,gBAAgB,EAAGG,KAAY,IAAK;MAClC/B,OAAO,CAACJ,oBAAoB,CAAC,CAAC;QAC5BoC,OAAO,EAAED,KAAK,CAACC,OAAO;QACtBC,KAAK,EAAEF,KAAK,CAACE;MACf,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAAA,EAAmB;EAC3D,MAAMC,WAAW,GAAGC,MAAM,CAACC,WAAW,CACpCD,MAAM,CAACE,OAAO,CAACpB,OAAO,CAAC,CAACqB,GAAG,CAAC,CAAC,CAACC,UAAU,EAAEC,MAAM,CAAC,KAAK;IACpD,MAAMC,aAAa,GAAG,SAASA,aAAaA,CAAC,GAAGjB,IAAe,EAAE;MAC/D,OAAOgB,MAAM,CAAC,GAAGhB,IAAI,CAAC;IACxB,CAAC;IACD,IAAIgB,MAAM,CAACE,IAAI,EAAE;MACf;AACR;AACA;AACA;AACA;AACA;AACA;MACQP,MAAM,CAACQ,cAAc,CAACF,aAAa,EAAE,MAAM,EAAE;QAC3CG,KAAK,EAAEJ,MAAM,CAACE,IAAI;QAClBG,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,CAACN,UAAU,EAAEE,aAAa,CAAC;EACpC,CAAC,CACH,CAAC;EAED,OAAOP,WAAW;AACpB;;AAEA;AACA;AACA,MAAMY,iBAAiB,GAAGb,iCAAiC,CAAC,CAAC;AAE7D,OAAO,SAASc,YAAYA,CAAA,EAAG;EAC7B,SAAS;;EACT,IAAI,CAACpC,kBAAkB,EAAE;IACvB;IACAG,MAAM,CAACG,OAAO,GAAG;MACf;MACA+B,MAAM,EAAEjD,OAAO,CAAC+C,iBAAiB,CAACE,MAAM,CAAC;MACzCC,KAAK,EAAElD,OAAO,CAAC+C,iBAAiB,CAACG,KAAK,CAAC;MACvC/B,GAAG,EAAEnB,OAAO,CAAC+C,iBAAiB,CAAC5B,GAAG,CAAC;MACnCgC,IAAI,EAAEnD,OAAO,CAAC+C,iBAAiB,CAACI,IAAI,CAAC;MACrCpB,KAAK,EAAE/B,OAAO,CAAC+C,iBAAiB,CAAChB,KAAK,CAAC;MACvCqB,IAAI,EAAEpD,OAAO,CAAC+C,iBAAiB,CAACK,IAAI;MACpC;IACF,CAAC;EACH;AACF;AAEA,SAASC,0BAA0BA,CAAA,EAAG;EACpC,SAAS;;EAET;EACA;EACA,MAAMC,2BAA2B,GAAGvC,MAAM,CAACwC,qBAAqB;EAEhE,IAAIC,uBAA2D,GAAG,EAAE;EACpE,IAAIC,cAAc,GAAG,KAAK;EAE1B1C,MAAM,CAAC2C,qBAAqB,GAAIC,cAAsB,IAAK;IACzD,MAAMC,gBAAgB,GAAGJ,uBAAuB;IAChDA,uBAAuB,GAAG,EAAE;IAC5BI,gBAAgB,CAACC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACH,cAAc,CAAC,CAAC;IAClDzD,cAAc,CAAC,CAAC;EAClB,CAAC;EAEDa,MAAM,CAACwC,qBAAqB,GAC1BQ,QAAqC,IAC1B;IACXP,uBAAuB,CAACQ,IAAI,CAACD,QAAQ,CAAC;IACtC,IAAI,CAACN,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MACrBH,2BAA2B,CAAEW,SAAS,IAAK;QACzCR,cAAc,GAAG,KAAK;QACtB1C,MAAM,CAACmD,gBAAgB,GAAGD,SAAS;QACnClD,MAAM,CAAC2C,qBAAqB,CAACO,SAAS,CAAC;QACvClD,MAAM,CAACmD,gBAAgB,GAAGC,SAAS;MACrC,CAAC,CAAC;IACJ;IACA;IACA;IACA;IACA;IACA,OAAO,CAAC,CAAC;EACX,CAAC;AACH;AAEA,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,IAAI1D,OAAO,EAAE;IACX;IACA;IACA;IACA;IACA;IACA;IACA2D,UAAU,CAACd,qBAAqB,GAAGlD,2BAA2B;EAChE;EAEAF,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACT0B,cAAc,CAAC,CAAC;IAChBmB,YAAY,CAAC,CAAC;IACd,IAAI,CAACrC,iBAAiB,EAAE;MACtBV,eAAe,CAAC,CAAC;MACjBoD,0BAA0B,CAAC,CAAC;IAC9B;EACF,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}