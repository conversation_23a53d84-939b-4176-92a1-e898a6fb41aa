/**
 * @license lucide-react-native v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

'use strict';

var createLucideIcon = require('../createLucideIcon.js');

const Replace = createLucideIcon("Replace", [
  ["path", { d: "M14 4a2 2 0 0 1 2-2", key: "1w2hp7" }],
  ["path", { d: "M16 10a2 2 0 0 1-2-2", key: "shjach" }],
  ["path", { d: "M20 2a2 2 0 0 1 2 2", key: "188mtx" }],
  ["path", { d: "M22 8a2 2 0 0 1-2 2", key: "ddf4tu" }],
  ["path", { d: "m3 7 3 3 3-3", key: "x25e72" }],
  ["path", { d: "M6 10V5a3 3 0 0 1 3-3h1", key: "3y3t5z" }],
  ["rect", { x: "2", y: "14", width: "8", height: "8", rx: "2", key: "4rksxw" }]
]);

module.exports = Replace;
//# sourceMappingURL=replace.js.map
