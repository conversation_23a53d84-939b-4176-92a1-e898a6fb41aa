{"version": 3, "names": [], "sources": ["WebSensor.ts"], "sourcesContent": ["'use strict';\nexport declare class WebSensor {\n  start: () => void;\n  stop: () => void;\n  addEventListener: (eventType: string, eventHandler: () => void) => void;\n  quaternion: [number, number, number, number];\n  x: number;\n  y: number;\n  z: number;\n}\n\ntype configOptions =\n  | {\n      referenceFrame: string;\n      frequency?: undefined;\n    }\n  | {\n      frequency: number;\n      referenceFrame?: undefined;\n    };\n\ninterface Constructable<T> {\n  new (config: configOptions): T;\n}\n\ndeclare global {\n  interface Window {\n    Accelerometer: Constructable<WebSensor>;\n    GravitySensor: Constructable<WebSensor>;\n    Gyroscope: Constructable<WebSensor>;\n    Magnetometer: Constructable<WebSensor>;\n    AbsoluteOrientationSensor: Constructable<WebSensor>;\n    Sensor: Constructable<WebSensor>;\n    opera?: string;\n  }\n}\n"], "mappings": "AAAA,YAAY", "ignoreList": []}