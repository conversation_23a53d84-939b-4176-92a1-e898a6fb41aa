/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @noflow
 */

const CSS_UNIT_RE = /^[+-]?\d*(?:\.\d+)?(?:[Ee][+-]?\d+)?(%|\w*)/;
declare var getUnit: (str: any) => any;
declare var isNumeric: (n: any) => any;
declare var multiplyStyleLengthValue: (value: string | number, multiple: any) => any;
export default multiplyStyleLengthValue;