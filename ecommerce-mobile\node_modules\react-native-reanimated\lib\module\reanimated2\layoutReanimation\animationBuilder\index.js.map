{"version": 3, "names": ["BaseAnimationBuilder", "ComplexAnimationBuilder", "Keyframe", "LayoutAnimationType", "SharedTransitionType"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nexport { BaseAnimationBuilder } from './BaseAnimationBuilder';\nexport { ComplexAnimationBuilder } from './ComplexAnimationBuilder';\nexport { Keyframe } from './Keyframe';\nexport { LayoutAnimationType, SharedTransitionType } from './commonTypes';\nexport type {\n  LayoutAnimation,\n  AnimationFunction,\n  EntryAnimationsValues,\n  ExitAnimationsValues,\n  EntryExitAnimationFunction,\n  AnimationConfigFunction,\n  IEntryAnimationBuilder,\n  IExitAnimationBuilder,\n  LayoutAnimationsValues,\n  LayoutAnimationFunction,\n  LayoutAnimationStartFunction,\n  ILayoutAnimationBuilder,\n  BaseLayoutAnimationConfig,\n  BaseBuilderAnimationConfig,\n  LayoutAnimationAndConfig,\n  IEntryExitAnimationBuilder,\n} from './commonTypes';\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,mBAAmB,EAAEC,oBAAoB,QAAQ,eAAe", "ignoreList": []}