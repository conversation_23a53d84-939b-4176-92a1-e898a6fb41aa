{"version": 3, "names": ["isWeb", "IS_WEB", "VELOCITY_EPS", "SLOPE_FACTOR", "isValidRubberBandConfig", "config", "rubberBandEffect", "Array", "isArray", "clamp", "length"], "sources": ["utils.ts"], "sourcesContent": ["'use strict';\nimport type {\n  AnimatableValue,\n  AnimationObject,\n  Animation,\n  ReduceMotion,\n  Timestamp,\n  RequiredKeys,\n} from '../../../reanimated2/commonTypes';\nimport { isWeb } from '../../PlatformChecker';\n\nconst IS_WEB = isWeb();\nexport const VELOCITY_EPS = IS_WEB ? 1 / 20 : 1;\nexport const SLOPE_FACTOR = 0.1;\n\nexport interface DecayAnimation extends Animation<DecayAnimation> {\n  lastTimestamp: Timestamp;\n  startTimestamp: Timestamp;\n  initialVelocity: number;\n  velocity: number;\n  current: AnimatableValue;\n}\n\nexport interface InnerDecayAnimation\n  extends Omit<DecayAnimation, 'current'>,\n    AnimationObject {\n  current: number;\n  springActive?: boolean;\n}\n\n/**\n * The decay animation configuration.\n *\n * @param velocity - Initial velocity of the animation. Defaults to 0.\n * @param deceleration - The rate at which the velocity decreases over time. Defaults to 0.998.\n * @param clamp - Array of two numbers which restricts animation's range. Defaults to [].\n * @param velocityFactor - Velocity multiplier. Defaults to 1.\n * @param rubberBandEffect - Makes the animation bounce over the limit specified in `clamp`. Defaults to `false`.\n * @param rubberBandFactor - Strength of the rubber band effect. Defaults to 0.6.\n * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDecay#config\n */\nexport type DecayConfig = {\n  deceleration?: number;\n  velocityFactor?: number;\n  velocity?: number;\n  reduceMotion?: ReduceMotion;\n} & (\n  | {\n      rubberBandEffect?: false;\n      clamp?: [min: number, max: number];\n    }\n  | {\n      rubberBandEffect: true;\n      clamp: [min: number, max: number];\n      rubberBandFactor?: number;\n    }\n);\n\nexport type DefaultDecayConfig = RequiredKeys<\n  DecayConfig,\n  'deceleration' | 'velocityFactor' | 'velocity'\n> & { rubberBandFactor: number };\n\n// If user wants to use rubber band decay animation we have to make sure he has provided clamp\nexport type RubberBandDecayConfig = RequiredKeys<\n  DefaultDecayConfig,\n  'clamp'\n> & { rubberBandEffect: true };\n\nexport function isValidRubberBandConfig(\n  config: DefaultDecayConfig\n): config is RubberBandDecayConfig {\n  'worklet';\n  return (\n    !!config.rubberBandEffect &&\n    Array.isArray(config.clamp) &&\n    config.clamp.length === 2\n  );\n}\n"], "mappings": "AAAA,YAAY;;AASZ,SAASA,KAAK,QAAQ,uBAAuB;AAE7C,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC;AACtB,OAAO,MAAME,YAAY,GAAGD,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAC/C,OAAO,MAAME,YAAY,GAAG,GAAG;;AAiB/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAuBA;;AAMA,OAAO,SAASC,uBAAuBA,CACrCC,MAA0B,EACO;EACjC,SAAS;;EACT,OACE,CAAC,CAACA,MAAM,CAACC,gBAAgB,IACzBC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,KAAK,CAAC,IAC3BJ,MAAM,CAACI,KAAK,CAACC,MAAM,KAAK,CAAC;AAE7B", "ignoreList": []}