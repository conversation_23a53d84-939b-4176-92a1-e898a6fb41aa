{"version": 3, "names": ["useRef", "useSharedValue", "getShadowNodeWrapperFromRef", "makeShareableCloneRecursive", "shareableMappingCache", "Platform", "findNodeHandle", "isF<PERSON><PERSON>", "isWeb", "IS_WEB", "getComponentOrScrollable", "component", "getNativeScrollRef", "getScrollableNode", "useAnimatedRef", "tag", "viewName", "ref", "current", "fun", "getTagValueFunction", "getTagOrShadowNodeWrapper", "value", "getTag", "OS", "viewConfig", "uiViewClassName", "animatedRefShareableHandle", "__init", "f", "set"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedRef.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,cAAc,QAAQ,qBAAkB;AAGjD,SAASC,2BAA2B,QAAQ,gBAAgB;AAC5D,SAASC,2BAA2B,QAAQ,kBAAe;AAC3D,SAASC,qBAAqB,QAAQ,6BAA0B;AAChE,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,cAAc,QAAQ,qCAAqC;AAEpE,SAASC,QAAQ,EAAEC,KAAK,QAAQ,uBAAoB;AAEpD,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC;AAYtB,SAASE,wBAAwBA,CAACC,SAAmC,EAAE;EACrE,IAAIJ,QAAQ,CAAC,CAAC,IAAII,SAAS,CAACC,kBAAkB,EAAE;IAC9C,OAAOD,SAAS,CAACC,kBAAkB,CAAC,CAAC;EACvC,CAAC,MAAM,IAAI,CAACL,QAAQ,CAAC,CAAC,IAAII,SAAS,CAACE,iBAAiB,EAAE;IACrD,OAAOF,SAAS,CAACE,iBAAiB,CAAC,CAAC;EACtC;EACA,OAAOF,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAAA,EAED;EAC3B,MAAMC,GAAG,GAAGd,cAAc,CAAoC,CAAC,CAAC,CAAC;EACjE,MAAMe,QAAQ,GAAGf,cAAc,CAAgB,IAAI,CAAC;EAEpD,MAAMgB,GAAG,GAAGjB,MAAM,CAA0B,CAAC;EAE7C,IAAI,CAACiB,GAAG,CAACC,OAAO,EAAE;IAChB,MAAMC,GAA4B,GAChCR,SAAS,IACN;MACH;MACA,IAAIA,SAAS,EAAE;QACb,MAAMS,mBAAmB,GAAGb,QAAQ,CAAC,CAAC,GAClCL,2BAA2B,GAC3BI,cAAc;QAElB,MAAMe,yBAAyB,GAAGA,CAAA,KAAM;UACtC,OAAOZ,MAAM,GACTC,wBAAwB,CAACC,SAAS,CAAC,GACnCS,mBAAmB,CAACV,wBAAwB,CAACC,SAAS,CAAC,CAAC;QAC9D,CAAC;QAEDI,GAAG,CAACO,KAAK,GAAGD,yBAAyB,CAAC,CAAC;;QAEvC;QACAF,GAAG,CAACI,MAAM,GAAGhB,QAAQ,CAAC,CAAC,GACnB,MAAMD,cAAc,CAACI,wBAAwB,CAACC,SAAS,CAAC,CAAC,GACzDU,yBAAyB;QAE7BF,GAAG,CAACD,OAAO,GAAGP,SAAS;QACvB;QACA,IAAIN,QAAQ,CAACmB,EAAE,KAAK,KAAK,IAAI,CAACjB,QAAQ,CAAC,CAAC,EAAE;UACxCS,QAAQ,CAACM,KAAK,GACXX,SAAS,EAA+Bc,UAAU,EAC/CC,eAAe,IAAI,SAAS;QACpC;MACF;MACA,OAAOX,GAAG,CAACO,KAAK;IAClB,CAAE;IAEFH,GAAG,CAACD,OAAO,GAAG,IAAI;IAElB,MAAMS,0BAA0B,GAAGxB,2BAA2B,CAAC;MAC7DyB,MAAM,EAAEA,CAAA,KAAM;QACZ,SAAS;;QACT,MAAMC,CAAkB,GAAGA,CAAA,KAAMd,GAAG,CAACO,KAAK;QAC1CO,CAAC,CAACb,QAAQ,GAAGA,QAAQ;QACrB,OAAOa,CAAC;MACV;IACF,CAAC,CAAC;IACFzB,qBAAqB,CAAC0B,GAAG,CAACX,GAAG,EAAEQ,0BAA0B,CAAC;IAC1DV,GAAG,CAACC,OAAO,GAAGC,GAAG;EACnB;EAEA,OAAOF,GAAG,CAACC,OAAO;AACpB", "ignoreList": []}