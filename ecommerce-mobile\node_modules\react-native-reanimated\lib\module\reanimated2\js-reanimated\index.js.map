{"version": 3, "names": ["JSReanimated", "isWeb", "PropsAllowlists", "createReactDOMStyle", "createTransformValue", "createTextShadowValue", "require", "default", "e", "reanimatedJS", "global", "_makeShareableClone", "Error", "_scheduleOnJS", "_scheduleOnRuntime", "_updatePropsJS", "updates", "viewRef", "isAnimatedProps", "_component", "component", "rawStyles", "Object", "keys", "reduce", "acc", "key", "value", "index", "setNativeProps", "undefined", "style", "updatePropsDOM", "props", "length", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "replace", "m", "toLowerCase", "_touchableNode", "setAttribute", "componentName", "className", "console", "warn", "newProps", "_component$setNativeP2", "_component$setNativeP", "uiProps", "isNativeProp", "call", "previousStyle", "currentStyle", "domStyle", "Array", "isArray", "transform", "textShadowColor", "textShadowRadius", "textShadowOffset", "textShadow", "propName", "NATIVE_THREAD_PROPS_WHITELIST"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nimport JSR<PERSON>imated from './JSReanimated';\nimport type { StyleProps } from '../commonTypes';\nimport type { AnimatedStyle } from '../helperTypes';\nimport { isWeb } from '../PlatformChecker';\nimport { PropsAllowlists } from '../../propsAllowlists';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet createReactDOMStyle: (style: any) => any;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet createTransformValue: (transform: any) => any;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet createTextShadowValue: (style: any) => void | string;\n\nif (isWeb()) {\n  try {\n    createReactDOMStyle =\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      require('react-native-web/dist/exports/StyleSheet/compiler/createReactDOMStyle').default;\n  } catch (e) {}\n\n  try {\n    // React Native Web 0.19+\n    createTransformValue =\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      require('react-native-web/dist/exports/StyleSheet/preprocess').createTransformValue;\n  } catch (e) {}\n\n  try {\n    createTextShadowValue =\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      require('react-native-web/dist/exports/StyleSheet/preprocess').createTextShadowValue;\n  } catch (e) {}\n}\n\nconst reanimatedJS = new JSReanimated();\n\nglobal._makeShareableClone = () => {\n  throw new Error(\n    '[Reanimated] _makeShareableClone should never be called in JSReanimated.'\n  );\n};\n\nglobal._scheduleOnJS = () => {\n  throw new Error(\n    '[Reanimated] _scheduleOnJS should never be called in JSReanimated.'\n  );\n};\n\nglobal._scheduleOnRuntime = () => {\n  throw new Error(\n    '[Reanimated] _scheduleOnRuntime should never be called in JSReanimated.'\n  );\n};\n\ninterface JSReanimatedComponent {\n  previousStyle: StyleProps;\n  setNativeProps?: (style: StyleProps) => void;\n  style?: StyleProps;\n  props: Record<string, string | number>;\n  _touchableNode: {\n    setAttribute: (key: string, props: unknown) => void;\n  };\n}\n\nexport interface ReanimatedHTMLElement extends HTMLElement {\n  previousStyle: StyleProps;\n  setNativeProps?: (style: StyleProps) => void;\n  props: Record<string, string | number>;\n  _touchableNode: {\n    setAttribute: (key: string, props: unknown) => void;\n  };\n  reanimatedDummy?: boolean;\n  removedAfterAnimation?: boolean;\n}\n\nexport const _updatePropsJS = (\n  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n  updates: StyleProps | AnimatedStyle<any>,\n  viewRef: { _component?: JSReanimatedComponent | ReanimatedHTMLElement },\n  isAnimatedProps?: boolean\n): void => {\n  if (viewRef._component) {\n    const component = viewRef._component;\n    const [rawStyles] = Object.keys(updates).reduce(\n      (acc: [StyleProps, AnimatedStyle<any>], key) => {\n        const value = updates[key];\n        const index = typeof value === 'function' ? 1 : 0;\n        acc[index][key] = value;\n        return acc;\n      },\n      [{}, {}]\n    );\n\n    if (typeof component.setNativeProps === 'function') {\n      // This is the legacy way to update props on React Native Web <= 0.18.\n      // Also, some components (e.g. from react-native-svg) don't have styles\n      // and always provide setNativeProps function instead (even on React Native Web 0.19+).\n      setNativeProps(component, rawStyles, isAnimatedProps);\n    } else if (\n      createReactDOMStyle !== undefined &&\n      component.style !== undefined\n    ) {\n      // React Native Web 0.19+ no longer provides setNativeProps function,\n      // so we need to update DOM nodes directly.\n      updatePropsDOM(component, rawStyles, isAnimatedProps);\n    } else if (Object.keys(component.props).length > 0) {\n      Object.keys(component.props).forEach((key) => {\n        if (!rawStyles[key]) {\n          return;\n        }\n        const dashedKey = key.replace(/[A-Z]/g, (m) => '-' + m.toLowerCase());\n        component._touchableNode.setAttribute(dashedKey, rawStyles[key]);\n      });\n    } else {\n      const componentName =\n        'className' in component ? component?.className : '';\n      console.warn(\n        `[Reanimated] It's not possible to manipulate the component ${componentName}`\n      );\n    }\n  }\n};\n\nconst setNativeProps = (\n  component: JSReanimatedComponent | ReanimatedHTMLElement,\n  newProps: StyleProps,\n  isAnimatedProps?: boolean\n): void => {\n  if (isAnimatedProps) {\n    const uiProps: Record<string, unknown> = {};\n    for (const key in newProps) {\n      if (isNativeProp(key)) {\n        uiProps[key] = newProps[key];\n      }\n    }\n    // Only update UI props directly on the component,\n    // other props can be updated as standard style props.\n    component.setNativeProps?.(uiProps);\n  }\n\n  const previousStyle = component.previousStyle ? component.previousStyle : {};\n  const currentStyle = { ...previousStyle, ...newProps };\n  component.previousStyle = currentStyle;\n\n  component.setNativeProps?.({ style: currentStyle });\n};\n\nconst updatePropsDOM = (\n  component: JSReanimatedComponent | HTMLElement,\n  style: StyleProps,\n  isAnimatedProps?: boolean\n): void => {\n  const previousStyle = (component as JSReanimatedComponent).previousStyle\n    ? (component as JSReanimatedComponent).previousStyle\n    : {};\n  const currentStyle = { ...previousStyle, ...style };\n  (component as JSReanimatedComponent).previousStyle = currentStyle;\n\n  const domStyle = createReactDOMStyle(currentStyle);\n  if (Array.isArray(domStyle.transform) && createTransformValue !== undefined) {\n    domStyle.transform = createTransformValue(domStyle.transform);\n  }\n\n  if (\n    createTextShadowValue !== undefined &&\n    (domStyle.textShadowColor ||\n      domStyle.textShadowRadius ||\n      domStyle.textShadowOffset)\n  ) {\n    domStyle.textShadow = createTextShadowValue({\n      textShadowColor: domStyle.textShadowColor,\n      textShadowOffset: domStyle.textShadowOffset,\n      textShadowRadius: domStyle.textShadowRadius,\n    });\n  }\n\n  for (const key in domStyle) {\n    if (isAnimatedProps) {\n      (component as HTMLElement).setAttribute(key, domStyle[key]);\n    } else {\n      (component.style as StyleProps)[key] = domStyle[key];\n    }\n  }\n};\n\nfunction isNativeProp(propName: string): boolean {\n  return !!PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST[propName];\n}\n\nexport default reanimatedJS;\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,YAAY,MAAM,gBAAgB;AAGzC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA,IAAIC,mBAAwC;AAC5C;AACA,IAAIC,oBAA6C;AACjD;AACA,IAAIC,qBAAoD;AAExD,IAAIJ,KAAK,CAAC,CAAC,EAAE;EACX,IAAI;IACFE,mBAAmB;IACjB;IACAG,OAAO,CAAC,uEAAuE,CAAC,CAACC,OAAO;EAC5F,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;EAEb,IAAI;IACF;IACAJ,oBAAoB;IAClB;IACAE,OAAO,CAAC,qDAAqD,CAAC,CAACF,oBAAoB;EACvF,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;EAEb,IAAI;IACFH,qBAAqB;IACnB;IACAC,OAAO,CAAC,qDAAqD,CAAC,CAACD,qBAAqB;EACxF,CAAC,CAAC,OAAOG,CAAC,EAAE,CAAC;AACf;AAEA,MAAMC,YAAY,GAAG,IAAIT,YAAY,CAAC,CAAC;AAEvCU,MAAM,CAACC,mBAAmB,GAAG,MAAM;EACjC,MAAM,IAAIC,KAAK,CACb,0EACF,CAAC;AACH,CAAC;AAEDF,MAAM,CAACG,aAAa,GAAG,MAAM;EAC3B,MAAM,IAAID,KAAK,CACb,oEACF,CAAC;AACH,CAAC;AAEDF,MAAM,CAACI,kBAAkB,GAAG,MAAM;EAChC,MAAM,IAAIF,KAAK,CACb,yEACF,CAAC;AACH,CAAC;AAuBD,OAAO,MAAMG,cAAc,GAAGA,CAE5BC,OAAwC,EACxCC,OAAuE,EACvEC,eAAyB,KAChB;EACT,IAAID,OAAO,CAACE,UAAU,EAAE;IACtB,MAAMC,SAAS,GAAGH,OAAO,CAACE,UAAU;IACpC,MAAM,CAACE,SAAS,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAACQ,MAAM,CAC7C,CAACC,GAAqC,EAAEC,GAAG,KAAK;MAC9C,MAAMC,KAAK,GAAGX,OAAO,CAACU,GAAG,CAAC;MAC1B,MAAME,KAAK,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC;MACjDF,GAAG,CAACG,KAAK,CAAC,CAACF,GAAG,CAAC,GAAGC,KAAK;MACvB,OAAOF,GAAG;IACZ,CAAC,EACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACT,CAAC;IAED,IAAI,OAAOL,SAAS,CAACS,cAAc,KAAK,UAAU,EAAE;MAClD;MACA;MACA;MACAA,cAAc,CAACT,SAAS,EAAEC,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IACLf,mBAAmB,KAAK2B,SAAS,IACjCV,SAAS,CAACW,KAAK,KAAKD,SAAS,EAC7B;MACA;MACA;MACAE,cAAc,CAACZ,SAAS,EAAEC,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IAAII,MAAM,CAACC,IAAI,CAACH,SAAS,CAACa,KAAK,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAClDZ,MAAM,CAACC,IAAI,CAACH,SAAS,CAACa,KAAK,CAAC,CAACE,OAAO,CAAET,GAAG,IAAK;QAC5C,IAAI,CAACL,SAAS,CAACK,GAAG,CAAC,EAAE;UACnB;QACF;QACA,MAAMU,SAAS,GAAGV,GAAG,CAACW,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;QACrEnB,SAAS,CAACoB,cAAc,CAACC,YAAY,CAACL,SAAS,EAAEf,SAAS,CAACK,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMgB,aAAa,GACjB,WAAW,IAAItB,SAAS,GAAGA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,SAAS,GAAG,EAAE;MACtDC,OAAO,CAACC,IAAI,CACT,8DAA6DH,aAAc,EAC9E,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMb,cAAc,GAAGA,CACrBT,SAAwD,EACxD0B,QAAoB,EACpB5B,eAAyB,KAChB;EAAA,IAAA6B,sBAAA;EACT,IAAI7B,eAAe,EAAE;IAAA,IAAA8B,qBAAA;IACnB,MAAMC,OAAgC,GAAG,CAAC,CAAC;IAC3C,KAAK,MAAMvB,GAAG,IAAIoB,QAAQ,EAAE;MAC1B,IAAII,YAAY,CAACxB,GAAG,CAAC,EAAE;QACrBuB,OAAO,CAACvB,GAAG,CAAC,GAAGoB,QAAQ,CAACpB,GAAG,CAAC;MAC9B;IACF;IACA;IACA;IACA,CAAAsB,qBAAA,GAAA5B,SAAS,CAACS,cAAc,cAAAmB,qBAAA,eAAxBA,qBAAA,CAAAG,IAAA,CAAA/B,SAAS,EAAkB6B,OAAO,CAAC;EACrC;EAEA,MAAMG,aAAa,GAAGhC,SAAS,CAACgC,aAAa,GAAGhC,SAAS,CAACgC,aAAa,GAAG,CAAC,CAAC;EAC5E,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGN;EAAS,CAAC;EACtD1B,SAAS,CAACgC,aAAa,GAAGC,YAAY;EAEtC,CAAAN,sBAAA,GAAA3B,SAAS,CAACS,cAAc,cAAAkB,sBAAA,eAAxBA,sBAAA,CAAAI,IAAA,CAAA/B,SAAS,EAAkB;IAAEW,KAAK,EAAEsB;EAAa,CAAC,CAAC;AACrD,CAAC;AAED,MAAMrB,cAAc,GAAGA,CACrBZ,SAA8C,EAC9CW,KAAiB,EACjBb,eAAyB,KAChB;EACT,MAAMkC,aAAa,GAAIhC,SAAS,CAA2BgC,aAAa,GACnEhC,SAAS,CAA2BgC,aAAa,GAClD,CAAC,CAAC;EACN,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGrB;EAAM,CAAC;EAClDX,SAAS,CAA2BgC,aAAa,GAAGC,YAAY;EAEjE,MAAMC,QAAQ,GAAGnD,mBAAmB,CAACkD,YAAY,CAAC;EAClD,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACG,SAAS,CAAC,IAAIrD,oBAAoB,KAAK0B,SAAS,EAAE;IAC3EwB,QAAQ,CAACG,SAAS,GAAGrD,oBAAoB,CAACkD,QAAQ,CAACG,SAAS,CAAC;EAC/D;EAEA,IACEpD,qBAAqB,KAAKyB,SAAS,KAClCwB,QAAQ,CAACI,eAAe,IACvBJ,QAAQ,CAACK,gBAAgB,IACzBL,QAAQ,CAACM,gBAAgB,CAAC,EAC5B;IACAN,QAAQ,CAACO,UAAU,GAAGxD,qBAAqB,CAAC;MAC1CqD,eAAe,EAAEJ,QAAQ,CAACI,eAAe;MACzCE,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;MAC3CD,gBAAgB,EAAEL,QAAQ,CAACK;IAC7B,CAAC,CAAC;EACJ;EAEA,KAAK,MAAMjC,GAAG,IAAI4B,QAAQ,EAAE;IAC1B,IAAIpC,eAAe,EAAE;MAClBE,SAAS,CAAiBqB,YAAY,CAACf,GAAG,EAAE4B,QAAQ,CAAC5B,GAAG,CAAC,CAAC;IAC7D,CAAC,MAAM;MACJN,SAAS,CAACW,KAAK,CAAgBL,GAAG,CAAC,GAAG4B,QAAQ,CAAC5B,GAAG,CAAC;IACtD;EACF;AACF,CAAC;AAED,SAASwB,YAAYA,CAACY,QAAgB,EAAW;EAC/C,OAAO,CAAC,CAAC5D,eAAe,CAAC6D,6BAA6B,CAACD,QAAQ,CAAC;AAClE;AAEA,eAAerD,YAAY", "ignoreList": []}