{"version": 3, "names": ["useEffect", "useRef", "useCallback", "useEvent", "useSharedValue", "isWeb", "IS_WEB", "useScrollViewOffset", "useScrollViewOffsetWeb", "useScrollViewOffsetNative", "animatedRef", "providedOffset", "internalOffset", "offset", "current", "scrollRef", "<PERSON><PERSON><PERSON><PERSON>", "element", "getWebScrollableElement", "value", "scrollLeft", "scrollTop", "removeEventListener", "addEventListener", "scrollRefTag", "event", "contentOffset", "x", "y", "scrollNativeEventNames", "workletEventHandler", "unregisterFromEvents", "getTag", "console", "warn", "registerForEvents", "scrollComponent", "getScrollableNode"], "sources": ["useScrollViewOffset.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useRef, useCallback } from 'react';\nimport type { SharedValue } from '../commonTypes';\nimport type { EventHandlerInternal } from './useEvent';\nimport { useEvent } from './useEvent';\nimport { useSharedValue } from './useSharedValue';\nimport type { AnimatedScrollView } from '../component/ScrollView';\nimport type {\n  AnimatedRef,\n  RNNativeScrollEvent,\n  ReanimatedScrollEvent,\n} from './commonTypes';\nimport { isWeb } from '../PlatformChecker';\n\nconst IS_WEB = isWeb();\n\n/**\n * Lets you synchronously get the current offset of a `ScrollView`.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef) attached to an Animated.ScrollView component.\n * @returns A shared value which holds the current offset of the `ScrollView`.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/useScrollViewOffset\n */\nexport const useScrollViewOffset = IS_WEB\n  ? useScrollViewOffsetWeb\n  : useScrollViewOffsetNative;\n\nfunction useScrollViewOffsetWeb(\n  animatedRef: AnimatedRef<AnimatedScrollView>,\n  providedOffset?: SharedValue<number>\n): SharedValue<number> {\n  const internalOffset = useSharedValue(0);\n  const offset = useRef(providedOffset ?? internalOffset).current;\n  const scrollRef = useRef<AnimatedScrollView | null>(null);\n\n  const eventHandler = useCallback(() => {\n    'worklet';\n    const element = getWebScrollableElement(animatedRef.current);\n    // scrollLeft is the X axis scrolled offset, works properly also with RTL layout\n    offset.value =\n      element.scrollLeft === 0 ? element.scrollTop : element.scrollLeft;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animatedRef, animatedRef.current]);\n\n  useEffect(() => {\n    // We need to make sure that listener for old animatedRef value is removed\n    if (scrollRef.current !== null) {\n      getWebScrollableElement(scrollRef.current).removeEventListener(\n        'scroll',\n        eventHandler\n      );\n    }\n    scrollRef.current = animatedRef.current;\n\n    const element = getWebScrollableElement(animatedRef.current);\n    element.addEventListener('scroll', eventHandler);\n    return () => {\n      element.removeEventListener('scroll', eventHandler);\n    };\n    // React here has a problem with `animatedRef.current` since a Ref .current\n    // field shouldn't be used as a dependency. However, in this case we have\n    // to do it this way.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animatedRef, animatedRef.current, eventHandler]);\n\n  return offset;\n}\n\nfunction useScrollViewOffsetNative(\n  animatedRef: AnimatedRef<AnimatedScrollView>,\n  providedOffset?: SharedValue<number>\n): SharedValue<number> {\n  const internalOffset = useSharedValue(0);\n  const offset = useRef(providedOffset ?? internalOffset).current;\n  const scrollRef = useRef<AnimatedScrollView | null>(null);\n  const scrollRefTag = useRef<number | null>(null);\n\n  const eventHandler = useEvent<RNNativeScrollEvent>(\n    (event: ReanimatedScrollEvent) => {\n      'worklet';\n      offset.value =\n        event.contentOffset.x === 0\n          ? event.contentOffset.y\n          : event.contentOffset.x;\n    },\n    scrollNativeEventNames\n    // Read https://github.com/software-mansion/react-native-reanimated/pull/5056\n    // for more information about this cast.\n  ) as unknown as EventHandlerInternal<ReanimatedScrollEvent>;\n\n  useEffect(() => {\n    // We need to make sure that listener for old animatedRef value is removed\n    if (scrollRef.current !== null && scrollRefTag.current !== null) {\n      eventHandler.workletEventHandler.unregisterFromEvents(\n        scrollRefTag.current\n      );\n    }\n\n    // Store the ref and viewTag for future cleanup\n    scrollRef.current = animatedRef.current;\n    scrollRefTag.current = animatedRef.getTag();\n\n    if (scrollRefTag === null) {\n      console.warn(\n        '[Reanimated] ScrollViewOffset failed to resolve the view tag from animated ref. Did you forget to attach the ref to a component?'\n      );\n    } else {\n      eventHandler.workletEventHandler.registerForEvents(scrollRefTag.current);\n    }\n\n    return () => {\n      if (scrollRefTag.current !== null) {\n        eventHandler.workletEventHandler.unregisterFromEvents(\n          scrollRefTag.current\n        );\n      }\n    };\n    // React here has a problem with `animatedRef.current` since a Ref .current\n    // field shouldn't be used as a dependency. However, in this case we have\n    // to do it this way.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animatedRef, animatedRef.current, eventHandler]);\n\n  return offset;\n}\n\nfunction getWebScrollableElement(\n  scrollComponent: AnimatedScrollView | null\n): HTMLElement {\n  return (\n    (scrollComponent?.getScrollableNode() as unknown as HTMLElement) ??\n    scrollComponent\n  );\n}\n\nconst scrollNativeEventNames = [\n  'onScroll',\n  'onScrollBeginDrag',\n  'onScrollEndDrag',\n  'onMomentumScrollBegin',\n  'onMomentumScrollEnd',\n];\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAGtD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,cAAc,QAAQ,kBAAkB;AAOjD,SAASC,KAAK,QAAQ,oBAAoB;AAE1C,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,mBAAmB,GAAGD,MAAM,GACrCE,sBAAsB,GACtBC,yBAAyB;AAE7B,SAASD,sBAAsBA,CAC7BE,WAA4C,EAC5CC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGR,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMS,MAAM,GAAGZ,MAAM,CAACU,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAC/D,MAAMC,SAAS,GAAGd,MAAM,CAA4B,IAAI,CAAC;EAEzD,MAAMe,YAAY,GAAGd,WAAW,CAAC,MAAM;IACrC,SAAS;;IACT,MAAMe,OAAO,GAAGC,uBAAuB,CAACR,WAAW,CAACI,OAAO,CAAC;IAC5D;IACAD,MAAM,CAACM,KAAK,GACVF,OAAO,CAACG,UAAU,KAAK,CAAC,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACG,UAAU;IACnE;EACF,CAAC,EAAE,CAACV,WAAW,EAAEA,WAAW,CAACI,OAAO,CAAC,CAAC;EAEtCd,SAAS,CAAC,MAAM;IACd;IACA,IAAIe,SAAS,CAACD,OAAO,KAAK,IAAI,EAAE;MAC9BI,uBAAuB,CAACH,SAAS,CAACD,OAAO,CAAC,CAACQ,mBAAmB,CAC5D,QAAQ,EACRN,YACF,CAAC;IACH;IACAD,SAAS,CAACD,OAAO,GAAGJ,WAAW,CAACI,OAAO;IAEvC,MAAMG,OAAO,GAAGC,uBAAuB,CAACR,WAAW,CAACI,OAAO,CAAC;IAC5DG,OAAO,CAACM,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAChD,OAAO,MAAM;MACXC,OAAO,CAACK,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IACrD,CAAC;IACD;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACN,WAAW,EAAEA,WAAW,CAACI,OAAO,EAAEE,YAAY,CAAC,CAAC;EAEpD,OAAOH,MAAM;AACf;AAEA,SAASJ,yBAAyBA,CAChCC,WAA4C,EAC5CC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGR,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMS,MAAM,GAAGZ,MAAM,CAACU,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAC/D,MAAMC,SAAS,GAAGd,MAAM,CAA4B,IAAI,CAAC;EACzD,MAAMuB,YAAY,GAAGvB,MAAM,CAAgB,IAAI,CAAC;EAEhD,MAAMe,YAAY,GAAGb,QAAQ,CAC1BsB,KAA4B,IAAK;IAChC,SAAS;;IACTZ,MAAM,CAACM,KAAK,GACVM,KAAK,CAACC,aAAa,CAACC,CAAC,KAAK,CAAC,GACvBF,KAAK,CAACC,aAAa,CAACE,CAAC,GACrBH,KAAK,CAACC,aAAa,CAACC,CAAC;EAC7B,CAAC,EACDE;EACA;EACA;EACF,CAA2D;EAE3D7B,SAAS,CAAC,MAAM;IACd;IACA,IAAIe,SAAS,CAACD,OAAO,KAAK,IAAI,IAAIU,YAAY,CAACV,OAAO,KAAK,IAAI,EAAE;MAC/DE,YAAY,CAACc,mBAAmB,CAACC,oBAAoB,CACnDP,YAAY,CAACV,OACf,CAAC;IACH;;IAEA;IACAC,SAAS,CAACD,OAAO,GAAGJ,WAAW,CAACI,OAAO;IACvCU,YAAY,CAACV,OAAO,GAAGJ,WAAW,CAACsB,MAAM,CAAC,CAAC;IAE3C,IAAIR,YAAY,KAAK,IAAI,EAAE;MACzBS,OAAO,CAACC,IAAI,CACV,kIACF,CAAC;IACH,CAAC,MAAM;MACLlB,YAAY,CAACc,mBAAmB,CAACK,iBAAiB,CAACX,YAAY,CAACV,OAAO,CAAC;IAC1E;IAEA,OAAO,MAAM;MACX,IAAIU,YAAY,CAACV,OAAO,KAAK,IAAI,EAAE;QACjCE,YAAY,CAACc,mBAAmB,CAACC,oBAAoB,CACnDP,YAAY,CAACV,OACf,CAAC;MACH;IACF,CAAC;IACD;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACJ,WAAW,EAAEA,WAAW,CAACI,OAAO,EAAEE,YAAY,CAAC,CAAC;EAEpD,OAAOH,MAAM;AACf;AAEA,SAASK,uBAAuBA,CAC9BkB,eAA0C,EAC7B;EACb,OACE,CAACA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEC,iBAAiB,CAAC,CAAC,KACrCD,eAAe;AAEnB;AAEA,MAAMP,sBAAsB,GAAG,CAC7B,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,CACtB", "ignoreList": []}