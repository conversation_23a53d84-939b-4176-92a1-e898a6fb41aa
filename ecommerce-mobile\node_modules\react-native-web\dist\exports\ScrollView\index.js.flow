/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @noflow
 */

'use client';

import type { ViewProps, ViewStyle } from '../View/types';
import Dimensions from '../Dimensions';
import dismissKeyboard from '../../modules/dismissKeyboard';
import invariant from 'fbjs/lib/invariant';
import mergeRefs from '../../modules/mergeRefs';
import Platform from '../Platform';
import ScrollViewBase from './ScrollViewBase';
import StyleSheet from '../StyleSheet';
import TextInputState from '../../modules/TextInputState';
import UIManager from '../UIManager';
import View from '../View';
import React from 'react';
import warning from 'fbjs/lib/warning';
type ScrollViewProps = {
  ...ViewProps,
  centerContent?: boolean,
  contentContainerStyle?: ViewStyle,
  horizontal?: boolean,
  keyboardDismissMode?: 'none' | 'interactive' | 'on-drag',
  onContentSizeChange?: (e: any) => void,
  onScroll?: (e: any) => void,
  pagingEnabled?: boolean,
  refreshControl?: any,
  scrollEnabled?: boolean,
  scrollEventThrottle?: number,
  stickyHeaderIndices?: Array<number>,
};
type Event = Object;
const emptyObject = {};
const IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;
declare class ScrollView extends React.Component<ScrollViewProps> {
  _scrollNodeRef: any,
  _innerViewRef: any,
  isTouching: boolean,
  lastMomentumScrollBeginTime: number,
  lastMomentumScrollEndTime: number,
  observedScrollSinceBecomingResponder: boolean,
  becameResponderWhileAnimating: boolean,
  scrollResponderHandleScrollShouldSetResponder: boolean,
  scrollResponderHandleStartShouldSetResponder(): boolean,
  scrollResponderHandleStartShouldSetResponderCapture: boolean,
  scrollResponderHandleResponderReject(): any,
  scrollResponderHandleTerminationRequest: boolean,
  scrollResponderHandleTouchEnd: any,
  scrollResponderHandleResponderRelease: any,
  scrollResponderHandleScroll: any,
  scrollResponderHandleResponderGrant: any,
  scrollResponderHandleScrollBeginDrag: any,
  scrollResponderHandleScrollEndDrag: any,
  scrollResponderHandleMomentumScrollBegin: any,
  scrollResponderHandleMomentumScrollEnd: any,
  scrollResponderHandleTouchStart: any,
  scrollResponderHandleTouchMove: any,
  scrollResponderIsAnimating: boolean,
  scrollResponderScrollTo: any,
  scrollResponderZoomTo: any,
  scrollResponderFlashScrollIndicators(): any,
  scrollResponderScrollNativeHandleToKeyboard: any,
  scrollResponderInputMeasureAndScrollToKeyboard: any,
  scrollResponderTextInputFocusError(e: Event): any,
  scrollResponderKeyboardWillShow: any,
  scrollResponderKeyboardWillHide: any,
  scrollResponderKeyboardDidShow: any,
  scrollResponderKeyboardDidHide: any,
  flashScrollIndicators: any,
  getScrollResponder: ScrollView,
  getScrollableNode: any,
  getInnerViewRef: any,
  getInnerViewNode: any,
  getNativeScrollRef: any,
  scrollTo: any,
  scrollToEnd: any,
  render(): any,
  _handleContentOnLayout: any,
  _handleScroll: any,
  _setInnerViewRef: any,
  _setScrollNodeRef: any,
}
const commonStyle = {
  flexGrow: 1,
  flexShrink: 1,
  // Enable hardware compositing in modern browsers.
  // Creates a new layer with its own backing surface that can significantly
  // improve scroll performance.
  transform: 'translateZ(0)',
  // iOS native scrolling
  WebkitOverflowScrolling: 'touch'
};
const styles = StyleSheet.create({
  baseVertical: {
    ...commonStyle,
    flexDirection: 'column',
    overflowX: 'hidden',
    overflowY: 'auto'
  },
  baseHorizontal: {
    ...commonStyle,
    flexDirection: 'row',
    overflowX: 'auto',
    overflowY: 'hidden'
  },
  contentContainerHorizontal: {
    flexDirection: 'row'
  },
  contentContainerCenterContent: {
    justifyContent: 'center',
    flexGrow: 1
  },
  stickyHeader: {
    position: 'sticky',
    top: 0,
    zIndex: 10
  },
  pagingEnabledHorizontal: {
    scrollSnapType: 'x mandatory'
  },
  pagingEnabledVertical: {
    scrollSnapType: 'y mandatory'
  },
  pagingEnabledChild: {
    scrollSnapAlign: 'start'
  }
});
const ForwardedScrollView: React.AbstractComponent<React.ElementConfig<typeof ScrollView>, React.ElementRef<typeof ScrollView>> = React.forwardRef((props, forwardedRef) => {
  return <ScrollView {...props} forwardedRef={forwardedRef} />;
});
ForwardedScrollView.displayName = 'ScrollView';
export default ForwardedScrollView;