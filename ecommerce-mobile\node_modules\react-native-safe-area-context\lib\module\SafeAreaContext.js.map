{"version": 3, "names": ["React", "Dimensions", "StyleSheet", "NativeSafeAreaProvider", "isDev", "process", "env", "NODE_ENV", "SafeAreaInsetsContext", "createContext", "displayName", "SafeAreaFrameContext", "SafeAreaProvider", "children", "initialMetrics", "initialSafeAreaInsets", "style", "others", "parentInsets", "useParentSafeAreaInsets", "parentFrame", "useParentSafeAreaFrame", "insets", "setInsets", "useState", "frame", "set<PERSON>rame", "x", "y", "width", "get", "height", "onInsetsChange", "useCallback", "event", "nativeEvent", "next<PERSON><PERSON><PERSON>", "nextInsets", "curFrame", "curInsets", "bottom", "left", "right", "top", "createElement", "_extends", "styles", "fill", "Provider", "value", "create", "flex", "useContext", "NO_INSETS_ERROR", "useSafeAreaInsets", "Error", "useSafeAreaFrame", "withSafeAreaInsets", "WrappedComponent", "forwardRef", "props", "ref", "useSafeArea", "SafeAreaConsumer", "Consumer", "SafeAreaContext"], "sourceRoot": "../../src", "sources": ["SafeAreaContext.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAwB,cAAc;AACrE,SAASC,sBAAsB,QAAQ,0BAA0B;AAQjE,MAAMC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AAEnD,OAAO,MAAMC,qBAAqB,gBAAGR,KAAK,CAACS,aAAa,CACtD,IACF,CAAC;AACD,IAAIL,KAAK,EAAE;EACTI,qBAAqB,CAACE,WAAW,GAAG,uBAAuB;AAC7D;AAEA,OAAO,MAAMC,oBAAoB,gBAAGX,KAAK,CAACS,aAAa,CAAc,IAAI,CAAC;AAC1E,IAAIL,KAAK,EAAE;EACTO,oBAAoB,CAACD,WAAW,GAAG,sBAAsB;AAC3D;AAWA,OAAO,SAASE,gBAAgBA,CAAC;EAC/BC,QAAQ;EACRC,cAAc;EACdC,qBAAqB;EACrBC,KAAK;EACL,GAAGC;AACkB,CAAC,EAAE;EACxB,MAAMC,YAAY,GAAGC,uBAAuB,CAAC,CAAC;EAC9C,MAAMC,WAAW,GAAGC,sBAAsB,CAAC,CAAC;EAC5C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CACxCV,cAAc,EAAEQ,MAAM,IAAIP,qBAAqB,IAAIG,YAAY,IAAI,IACrE,CAAC;EACD,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,KAAK,CAACwB,QAAQ,CACtCV,cAAc,EAAEW,KAAK,IACnBL,WAAW,IAAI;IACb;IACAO,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE5B,UAAU,CAAC6B,GAAG,CAAC,QAAQ,CAAC,CAACD,KAAK;IACrCE,MAAM,EAAE9B,UAAU,CAAC6B,GAAG,CAAC,QAAQ,CAAC,CAACC;EACnC,CACJ,CAAC;EACD,MAAMC,cAAc,GAAGhC,KAAK,CAACiC,WAAW,CAAEC,KAAwB,IAAK;IACrE,MAAM;MACJC,WAAW,EAAE;QAAEV,KAAK,EAAEW,SAAS;QAAEd,MAAM,EAAEe;MAAW;IACtD,CAAC,GAAGH,KAAK;IAETR,QAAQ,CAAEY,QAAQ,IAAK;MACrB;MACE;MACAF,SAAS,KACRA,SAAS,CAACL,MAAM,KAAKO,QAAQ,CAACP,MAAM,IACnCK,SAAS,CAACP,KAAK,KAAKS,QAAQ,CAACT,KAAK,IAClCO,SAAS,CAACT,CAAC,KAAKW,QAAQ,CAACX,CAAC,IAC1BS,SAAS,CAACR,CAAC,KAAKU,QAAQ,CAACV,CAAC,CAAC,EAC7B;QACA,OAAOQ,SAAS;MAClB,CAAC,MAAM;QACL,OAAOE,QAAQ;MACjB;IACF,CAAC,CAAC;IAEFf,SAAS,CAAEgB,SAAS,IAAK;MACvB,IACE,CAACA,SAAS,IACVF,UAAU,CAACG,MAAM,KAAKD,SAAS,CAACC,MAAM,IACtCH,UAAU,CAACI,IAAI,KAAKF,SAAS,CAACE,IAAI,IAClCJ,UAAU,CAACK,KAAK,KAAKH,SAAS,CAACG,KAAK,IACpCL,UAAU,CAACM,GAAG,KAAKJ,SAAS,CAACI,GAAG,EAChC;QACA,OAAON,UAAU;MACnB,CAAC,MAAM;QACL,OAAOE,SAAS;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvC,KAAA,CAAA4C,aAAA,CAACzC,sBAAsB,EAAA0C,QAAA;IACrB7B,KAAK,EAAE,CAAC8B,MAAM,CAACC,IAAI,EAAE/B,KAAK,CAAE;IAC5BgB,cAAc,EAAEA;EAAe,GAC3Bf,MAAM,GAETK,MAAM,IAAI,IAAI,gBACbtB,KAAA,CAAA4C,aAAA,CAACjC,oBAAoB,CAACqC,QAAQ;IAACC,KAAK,EAAExB;EAAM,gBAC1CzB,KAAA,CAAA4C,aAAA,CAACpC,qBAAqB,CAACwC,QAAQ;IAACC,KAAK,EAAE3B;EAAO,GAC3CT,QAC6B,CACH,CAAC,GAC9B,IACkB,CAAC;AAE7B;AAEA,MAAMiC,MAAM,GAAG5C,UAAU,CAACgD,MAAM,CAAC;EAC/BH,IAAI,EAAE;IAAEI,IAAI,EAAE;EAAE;AAClB,CAAC,CAAC;AAEF,SAAShC,uBAAuBA,CAAA,EAAsB;EACpD,OAAOnB,KAAK,CAACoD,UAAU,CAAC5C,qBAAqB,CAAC;AAChD;AAEA,SAASa,sBAAsBA,CAAA,EAAgB;EAC7C,OAAOrB,KAAK,CAACoD,UAAU,CAACzC,oBAAoB,CAAC;AAC/C;AAEA,MAAM0C,eAAe,GACnB,wGAAwG;AAE1G,OAAO,SAASC,iBAAiBA,CAAA,EAAe;EAC9C,MAAMhC,MAAM,GAAGtB,KAAK,CAACoD,UAAU,CAAC5C,qBAAqB,CAAC;EACtD,IAAIc,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIiC,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAO/B,MAAM;AACf;AAEA,OAAO,SAASkC,gBAAgBA,CAAA,EAAS;EACvC,MAAM/B,KAAK,GAAGzB,KAAK,CAACoD,UAAU,CAACzC,oBAAoB,CAAC;EACpD,IAAIc,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAI8B,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAO5B,KAAK;AACd;AAMA,OAAO,SAASgC,kBAAkBA,CAChCC,gBAEC,EAGD;EACA,oBAAO1D,KAAK,CAAC2D,UAAU,CAAa,CAACC,KAAK,EAAEC,GAAG,KAAK;IAClD,MAAMvC,MAAM,GAAGgC,iBAAiB,CAAC,CAAC;IAClC,oBAAOtD,KAAA,CAAA4C,aAAA,CAACc,gBAAgB,EAAAb,QAAA,KAAKe,KAAK;MAAEtC,MAAM,EAAEA,MAAO;MAACuC,GAAG,EAAEA;IAAI,EAAE,CAAC;EAClE,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAA,EAAe;EACxC,OAAOR,iBAAiB,CAAC,CAAC;AAC5B;;AAEA;AACA;AACA;AACA,OAAO,MAAMS,gBAAgB,GAAGvD,qBAAqB,CAACwD,QAAQ;;AAE9D;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGzD,qBAAqB", "ignoreList": []}