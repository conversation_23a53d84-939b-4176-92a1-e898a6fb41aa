{"version": 3, "names": [], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\nimport type { Ref, Component } from 'react';\nimport type {\n  StyleProps,\n  BaseAnimationBuilder,\n  ILayoutAnimationBuilder,\n  EntryExitAnimationFunction,\n  SharedTransition,\n  SharedValue,\n} from '../reanimated2';\nimport type {\n  ViewDescriptorsSet,\n  ViewRefSet,\n} from '../reanimated2/ViewDescriptorsSet';\nimport type { SkipEnteringContext } from '../reanimated2/component/LayoutAnimationConfig';\nimport type { ShadowNodeWrapper } from '../reanimated2/commonTypes';\nimport type { ViewConfig } from '../ConfigHelper';\n\nexport interface AnimatedProps extends Record<string, unknown> {\n  viewDescriptors?: ViewDescriptorsSet;\n  viewsRef?: ViewRefSet<unknown>;\n  initial?: SharedValue<StyleProps>;\n}\n\nexport interface ViewInfo {\n  viewTag: number | HTMLElement | null;\n  viewName: string | null;\n  shadowNodeWrapper: ShadowNodeWrapper | null;\n  viewConfig: ViewConfig;\n}\n\nexport interface IInlinePropManager {\n  attachInlineProps(\n    animatedComponent: React.Component<unknown, unknown>,\n    viewInfo: ViewInfo\n  ): void;\n  detachInlineProps(): void;\n}\n\nexport interface IPropsFilter {\n  filterNonAnimatedProps: (\n    component: React.Component<unknown, unknown> & IAnimatedComponentInternal\n  ) => Record<string, unknown>;\n}\n\nexport interface IJSPropsUpdater {\n  addOnJSPropsChangeListener(\n    animatedComponent: React.Component<unknown, unknown> &\n      IAnimatedComponentInternal\n  ): void;\n  removeOnJSPropsChangeListener(\n    animatedComponent: React.Component<unknown, unknown> &\n      IAnimatedComponentInternal\n  ): void;\n}\n\nexport type LayoutAnimationStaticContext = {\n  presetName: string;\n};\n\nexport type AnimatedComponentProps<P extends Record<string, unknown>> = P & {\n  forwardedRef?: Ref<Component>;\n  style?: NestedArray<StyleProps>;\n  animatedProps?: Partial<AnimatedComponentProps<AnimatedProps>>;\n  animatedStyle?: StyleProps;\n  layout?: (\n    | BaseAnimationBuilder\n    | ILayoutAnimationBuilder\n    | typeof BaseAnimationBuilder\n  ) &\n    LayoutAnimationStaticContext;\n  entering?: (\n    | BaseAnimationBuilder\n    | typeof BaseAnimationBuilder\n    | EntryExitAnimationFunction\n    | Keyframe\n  ) &\n    LayoutAnimationStaticContext;\n  exiting?: (\n    | BaseAnimationBuilder\n    | typeof BaseAnimationBuilder\n    | EntryExitAnimationFunction\n    | Keyframe\n  ) &\n    LayoutAnimationStaticContext;\n  sharedTransitionTag?: string;\n  sharedTransitionStyle?: SharedTransition;\n};\n\nexport interface AnimatedComponentRef extends Component {\n  setNativeProps?: (props: Record<string, unknown>) => void;\n  getScrollableNode?: () => AnimatedComponentRef;\n  getAnimatableRef?: () => AnimatedComponentRef;\n}\n\nexport interface IAnimatedComponentInternal {\n  _styles: StyleProps[] | null;\n  _animatedProps?: Partial<AnimatedComponentProps<AnimatedProps>>;\n  _viewTag: number;\n  _isFirstRender: boolean;\n  jestAnimatedStyle: { value: StyleProps };\n  _component: AnimatedComponentRef | HTMLElement | null;\n  _sharedElementTransition: SharedTransition | null;\n  _jsPropsUpdater: IJSPropsUpdater;\n  _InlinePropManager: IInlinePropManager;\n  _PropsFilter: IPropsFilter;\n  _viewInfo?: ViewInfo;\n  context: React.ContextType<typeof SkipEnteringContext>;\n}\n\nexport type NestedArray<T> = T | NestedArray<T>[];\n\nexport interface InitialComponentProps extends Record<string, unknown> {\n  ref?: Ref<Component>;\n  collapsable?: boolean;\n}\n"], "mappings": "AAAA,YAAY;;AAAC", "ignoreList": []}