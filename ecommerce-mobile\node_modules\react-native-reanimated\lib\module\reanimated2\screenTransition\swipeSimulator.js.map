{"version": 3, "names": ["applyStyle", "applyStyleForBelowTopScreen", "RNScreensTurboModule", "BASE_VELOCITY", "ADDITIONAL_VELOCITY_FACTOR_X", "ADDITIONAL_VELOCITY_FACTOR_Y", "ADDITIONAL_VELOCITY_FACTOR_XY", "computeEasingProgress", "startingTimestamp", "distance", "velocity", "Math", "abs", "elapsedTime", "_getAnimationTimestamp", "currentPosition", "progress", "easing", "x", "pow", "computeProgress", "screenTransitionConfig", "event", "isTransitionCanceled", "screenDimensions", "progressX", "translationX", "width", "progressY", "translationY", "height", "max<PERSON>rogress", "max", "maybeScheduleNextFrame", "step", "didScreenReachDestination", "stackTag", "updateTransition", "requestAnimationFrame", "_screenTransitionConf", "onFinishAnimation", "call", "getSwipeSimulator", "lockAxis", "startTimestamp", "startingPosition", "y", "direction", "sign", "finalPosition", "euclideanDistance", "sqrt", "screenDiagonal", "velocityVectorLength", "didScreenReachDestinationCheck", "restoreOriginalStyleForBelowTopScreen", "computeFrame", "finished"], "sources": ["swipeSimulator.ts"], "sourcesContent": ["'use strict';\nimport type {\n  PanGestureHandlerEventPayload,\n  ScreenTransitionConfig,\n  LockAxis,\n} from './commonTypes';\nimport { applyStyle, applyStyleForBelowTopScreen } from './styleUpdater';\nimport { RNScreensTurboModule } from './RNScreensTurboModule';\n\nconst BASE_VELOCITY = 400;\nconst ADDITIONAL_VELOCITY_FACTOR_X = 400;\nconst ADDITIONAL_VELOCITY_FACTOR_Y = 500;\nconst ADDITIONAL_VELOCITY_FACTOR_XY = 600;\n\nfunction computeEasingProgress(\n  startingTimestamp: number,\n  distance: number,\n  velocity: number\n) {\n  'worklet';\n  if (Math.abs(distance) < 1) {\n    return 1;\n  }\n  const elapsedTime = (_getAnimationTimestamp() - startingTimestamp) / 1000;\n  const currentPosition = velocity * elapsedTime;\n  const progress = currentPosition / distance;\n  return progress;\n}\n\nfunction easing(x: number): number {\n  'worklet';\n  // based on https://easings.net/#easeOutQuart\n  return 1 - Math.pow(1 - x, 5);\n}\n\nfunction computeProgress(\n  screenTransitionConfig: ScreenTransitionConfig,\n  event: PanGestureHandlerEventPayload,\n  isTransitionCanceled: boolean\n) {\n  'worklet';\n  const screenDimensions = screenTransitionConfig.screenDimensions;\n  const progressX = Math.abs(event.translationX / screenDimensions.width);\n  const progressY = Math.abs(event.translationY / screenDimensions.height);\n  const maxProgress = Math.max(progressX, progressY);\n  const progress = isTransitionCanceled ? maxProgress / 2 : maxProgress;\n  return progress;\n}\n\nfunction maybeScheduleNextFrame(\n  step: () => void,\n  didScreenReachDestination: boolean,\n  screenTransitionConfig: ScreenTransitionConfig,\n  event: PanGestureHandlerEventPayload,\n  isTransitionCanceled: boolean\n) {\n  'worklet';\n  if (!didScreenReachDestination) {\n    const stackTag = screenTransitionConfig.stackTag;\n    const progress = computeProgress(\n      screenTransitionConfig,\n      event,\n      isTransitionCanceled\n    );\n    RNScreensTurboModule.updateTransition(stackTag, progress);\n    requestAnimationFrame(step);\n  } else {\n    screenTransitionConfig.onFinishAnimation?.();\n  }\n}\n\nexport function getSwipeSimulator(\n  event: PanGestureHandlerEventPayload,\n  screenTransitionConfig: ScreenTransitionConfig,\n  lockAxis?: LockAxis\n) {\n  'worklet';\n  const screenDimensions = screenTransitionConfig.screenDimensions;\n  const startTimestamp = _getAnimationTimestamp();\n  const { isTransitionCanceled } = screenTransitionConfig;\n  const startingPosition = {\n    x: event.translationX,\n    y: event.translationY,\n  };\n  const direction = {\n    x: Math.sign(event.translationX),\n    y: Math.sign(event.translationY),\n  };\n  const finalPosition = isTransitionCanceled\n    ? { x: 0, y: 0 }\n    : {\n        x: direction.x * screenDimensions.width,\n        y: direction.y * screenDimensions.height,\n      };\n  const distance = {\n    x: Math.abs(finalPosition.x - startingPosition.x),\n    y: Math.abs(finalPosition.y - startingPosition.y),\n  };\n  const didScreenReachDestination = {\n    x: false,\n    y: false,\n  };\n  const velocity = { x: BASE_VELOCITY, y: BASE_VELOCITY };\n  if (lockAxis === 'x') {\n    velocity.y = 0;\n    velocity.x +=\n      (ADDITIONAL_VELOCITY_FACTOR_X * distance.x) / screenDimensions.width;\n  } else if (lockAxis === 'y') {\n    velocity.x = 0;\n    velocity.y +=\n      (ADDITIONAL_VELOCITY_FACTOR_Y * distance.y) / screenDimensions.height;\n  } else {\n    const euclideanDistance = Math.sqrt(distance.x ** 2 + distance.y ** 2);\n    const screenDiagonal = Math.sqrt(\n      screenDimensions.width ** 2 + screenDimensions.height ** 2\n    );\n    const velocityVectorLength =\n      BASE_VELOCITY +\n      (ADDITIONAL_VELOCITY_FACTOR_XY * euclideanDistance) / screenDiagonal;\n    if (Math.abs(startingPosition.x) > Math.abs(startingPosition.y)) {\n      velocity.x = velocityVectorLength;\n      velocity.y =\n        velocityVectorLength *\n        Math.abs(startingPosition.y / startingPosition.x);\n    } else {\n      velocity.x =\n        velocityVectorLength *\n        Math.abs(startingPosition.x / startingPosition.y);\n      velocity.y = velocityVectorLength;\n    }\n  }\n\n  if (isTransitionCanceled) {\n    function didScreenReachDestinationCheck() {\n      if (lockAxis === 'x') {\n        return didScreenReachDestination.x;\n      } else if (lockAxis === 'y') {\n        return didScreenReachDestination.y;\n      } else {\n        return didScreenReachDestination.x && didScreenReachDestination.y;\n      }\n    }\n\n    function restoreOriginalStyleForBelowTopScreen() {\n      event.translationX = direction.x * screenDimensions.width;\n      event.translationY = direction.y * screenDimensions.height;\n      applyStyleForBelowTopScreen(screenTransitionConfig, event);\n    }\n\n    const computeFrame = () => {\n      const progress = {\n        x: computeEasingProgress(startTimestamp, distance.x, velocity.x),\n        y: computeEasingProgress(startTimestamp, distance.y, velocity.y),\n      };\n      event.translationX =\n        startingPosition.x - direction.x * distance.x * easing(progress.x);\n      event.translationY =\n        startingPosition.y - direction.y * distance.y * easing(progress.y);\n      if (direction.x > 0) {\n        if (event.translationX <= 0) {\n          didScreenReachDestination.x = true;\n          event.translationX = 0;\n        }\n      } else {\n        if (event.translationX >= 0) {\n          didScreenReachDestination.x = true;\n          event.translationX = 0;\n        }\n      }\n      if (direction.y > 0) {\n        if (event.translationY <= 0) {\n          didScreenReachDestination.y = true;\n          event.translationY = 0;\n        }\n      } else {\n        if (event.translationY >= 0) {\n          didScreenReachDestination.y = true;\n          event.translationY = 0;\n        }\n      }\n      applyStyle(screenTransitionConfig, event);\n      const finished = didScreenReachDestinationCheck();\n      if (finished) {\n        restoreOriginalStyleForBelowTopScreen();\n      }\n      maybeScheduleNextFrame(\n        computeFrame,\n        finished,\n        screenTransitionConfig,\n        event,\n        isTransitionCanceled\n      );\n    };\n    return computeFrame;\n  } else {\n    const computeFrame = () => {\n      const progress = {\n        x: computeEasingProgress(startTimestamp, distance.x, velocity.x),\n        y: computeEasingProgress(startTimestamp, distance.y, velocity.y),\n      };\n      event.translationX =\n        startingPosition.x + direction.x * distance.x * easing(progress.x);\n      event.translationY =\n        startingPosition.y + direction.y * distance.y * easing(progress.y);\n      if (direction.x > 0) {\n        if (event.translationX >= screenDimensions.width) {\n          didScreenReachDestination.x = true;\n          event.translationX = screenDimensions.width;\n        }\n      } else {\n        if (event.translationX <= -screenDimensions.width) {\n          didScreenReachDestination.x = true;\n          event.translationX = -screenDimensions.width;\n        }\n      }\n      if (direction.y > 0) {\n        if (event.translationY >= screenDimensions.height) {\n          didScreenReachDestination.y = true;\n          event.translationY = screenDimensions.height;\n        }\n      } else {\n        if (event.translationY <= -screenDimensions.height) {\n          didScreenReachDestination.y = true;\n          event.translationY = -screenDimensions.height;\n        }\n      }\n      applyStyle(screenTransitionConfig, event);\n      maybeScheduleNextFrame(\n        computeFrame,\n        didScreenReachDestination.x || didScreenReachDestination.y,\n        screenTransitionConfig,\n        event,\n        isTransitionCanceled\n      );\n    };\n    return computeFrame;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,UAAU,EAAEC,2BAA2B,QAAQ,gBAAgB;AACxE,SAASC,oBAAoB,QAAQ,wBAAwB;AAE7D,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,4BAA4B,GAAG,GAAG;AACxC,MAAMC,4BAA4B,GAAG,GAAG;AACxC,MAAMC,6BAA6B,GAAG,GAAG;AAEzC,SAASC,qBAAqBA,CAC5BC,iBAAyB,EACzBC,QAAgB,EAChBC,QAAgB,EAChB;EACA,SAAS;;EACT,IAAIC,IAAI,CAACC,GAAG,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC1B,OAAO,CAAC;EACV;EACA,MAAMI,WAAW,GAAG,CAACC,sBAAsB,CAAC,CAAC,GAAGN,iBAAiB,IAAI,IAAI;EACzE,MAAMO,eAAe,GAAGL,QAAQ,GAAGG,WAAW;EAC9C,MAAMG,QAAQ,GAAGD,eAAe,GAAGN,QAAQ;EAC3C,OAAOO,QAAQ;AACjB;AAEA,SAASC,MAAMA,CAACC,CAAS,EAAU;EACjC,SAAS;;EACT;EACA,OAAO,CAAC,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGD,CAAC,EAAE,CAAC,CAAC;AAC/B;AAEA,SAASE,eAAeA,CACtBC,sBAA8C,EAC9CC,KAAoC,EACpCC,oBAA6B,EAC7B;EACA,SAAS;;EACT,MAAMC,gBAAgB,GAAGH,sBAAsB,CAACG,gBAAgB;EAChE,MAAMC,SAAS,GAAGd,IAAI,CAACC,GAAG,CAACU,KAAK,CAACI,YAAY,GAAGF,gBAAgB,CAACG,KAAK,CAAC;EACvE,MAAMC,SAAS,GAAGjB,IAAI,CAACC,GAAG,CAACU,KAAK,CAACO,YAAY,GAAGL,gBAAgB,CAACM,MAAM,CAAC;EACxE,MAAMC,WAAW,GAAGpB,IAAI,CAACqB,GAAG,CAACP,SAAS,EAAEG,SAAS,CAAC;EAClD,MAAMZ,QAAQ,GAAGO,oBAAoB,GAAGQ,WAAW,GAAG,CAAC,GAAGA,WAAW;EACrE,OAAOf,QAAQ;AACjB;AAEA,SAASiB,sBAAsBA,CAC7BC,IAAgB,EAChBC,yBAAkC,EAClCd,sBAA8C,EAC9CC,KAAoC,EACpCC,oBAA6B,EAC7B;EACA,SAAS;;EACT,IAAI,CAACY,yBAAyB,EAAE;IAC9B,MAAMC,QAAQ,GAAGf,sBAAsB,CAACe,QAAQ;IAChD,MAAMpB,QAAQ,GAAGI,eAAe,CAC9BC,sBAAsB,EACtBC,KAAK,EACLC,oBACF,CAAC;IACDrB,oBAAoB,CAACmC,gBAAgB,CAACD,QAAQ,EAAEpB,QAAQ,CAAC;IACzDsB,qBAAqB,CAACJ,IAAI,CAAC;EAC7B,CAAC,MAAM;IAAA,IAAAK,qBAAA;IACL,CAAAA,qBAAA,GAAAlB,sBAAsB,CAACmB,iBAAiB,cAAAD,qBAAA,eAAxCA,qBAAA,CAAAE,IAAA,CAAApB,sBAA2C,CAAC;EAC9C;AACF;AAEA,OAAO,SAASqB,iBAAiBA,CAC/BpB,KAAoC,EACpCD,sBAA8C,EAC9CsB,QAAmB,EACnB;EACA,SAAS;;EACT,MAAMnB,gBAAgB,GAAGH,sBAAsB,CAACG,gBAAgB;EAChE,MAAMoB,cAAc,GAAG9B,sBAAsB,CAAC,CAAC;EAC/C,MAAM;IAAES;EAAqB,CAAC,GAAGF,sBAAsB;EACvD,MAAMwB,gBAAgB,GAAG;IACvB3B,CAAC,EAAEI,KAAK,CAACI,YAAY;IACrBoB,CAAC,EAAExB,KAAK,CAACO;EACX,CAAC;EACD,MAAMkB,SAAS,GAAG;IAChB7B,CAAC,EAAEP,IAAI,CAACqC,IAAI,CAAC1B,KAAK,CAACI,YAAY,CAAC;IAChCoB,CAAC,EAAEnC,IAAI,CAACqC,IAAI,CAAC1B,KAAK,CAACO,YAAY;EACjC,CAAC;EACD,MAAMoB,aAAa,GAAG1B,oBAAoB,GACtC;IAAEL,CAAC,EAAE,CAAC;IAAE4B,CAAC,EAAE;EAAE,CAAC,GACd;IACE5B,CAAC,EAAE6B,SAAS,CAAC7B,CAAC,GAAGM,gBAAgB,CAACG,KAAK;IACvCmB,CAAC,EAAEC,SAAS,CAACD,CAAC,GAAGtB,gBAAgB,CAACM;EACpC,CAAC;EACL,MAAMrB,QAAQ,GAAG;IACfS,CAAC,EAAEP,IAAI,CAACC,GAAG,CAACqC,aAAa,CAAC/B,CAAC,GAAG2B,gBAAgB,CAAC3B,CAAC,CAAC;IACjD4B,CAAC,EAAEnC,IAAI,CAACC,GAAG,CAACqC,aAAa,CAACH,CAAC,GAAGD,gBAAgB,CAACC,CAAC;EAClD,CAAC;EACD,MAAMX,yBAAyB,GAAG;IAChCjB,CAAC,EAAE,KAAK;IACR4B,CAAC,EAAE;EACL,CAAC;EACD,MAAMpC,QAAQ,GAAG;IAAEQ,CAAC,EAAEf,aAAa;IAAE2C,CAAC,EAAE3C;EAAc,CAAC;EACvD,IAAIwC,QAAQ,KAAK,GAAG,EAAE;IACpBjC,QAAQ,CAACoC,CAAC,GAAG,CAAC;IACdpC,QAAQ,CAACQ,CAAC,IACPd,4BAA4B,GAAGK,QAAQ,CAACS,CAAC,GAAIM,gBAAgB,CAACG,KAAK;EACxE,CAAC,MAAM,IAAIgB,QAAQ,KAAK,GAAG,EAAE;IAC3BjC,QAAQ,CAACQ,CAAC,GAAG,CAAC;IACdR,QAAQ,CAACoC,CAAC,IACPzC,4BAA4B,GAAGI,QAAQ,CAACqC,CAAC,GAAItB,gBAAgB,CAACM,MAAM;EACzE,CAAC,MAAM;IACL,MAAMoB,iBAAiB,GAAGvC,IAAI,CAACwC,IAAI,CAAC1C,QAAQ,CAACS,CAAC,IAAI,CAAC,GAAGT,QAAQ,CAACqC,CAAC,IAAI,CAAC,CAAC;IACtE,MAAMM,cAAc,GAAGzC,IAAI,CAACwC,IAAI,CAC9B3B,gBAAgB,CAACG,KAAK,IAAI,CAAC,GAAGH,gBAAgB,CAACM,MAAM,IAAI,CAC3D,CAAC;IACD,MAAMuB,oBAAoB,GACxBlD,aAAa,GACZG,6BAA6B,GAAG4C,iBAAiB,GAAIE,cAAc;IACtE,IAAIzC,IAAI,CAACC,GAAG,CAACiC,gBAAgB,CAAC3B,CAAC,CAAC,GAAGP,IAAI,CAACC,GAAG,CAACiC,gBAAgB,CAACC,CAAC,CAAC,EAAE;MAC/DpC,QAAQ,CAACQ,CAAC,GAAGmC,oBAAoB;MACjC3C,QAAQ,CAACoC,CAAC,GACRO,oBAAoB,GACpB1C,IAAI,CAACC,GAAG,CAACiC,gBAAgB,CAACC,CAAC,GAAGD,gBAAgB,CAAC3B,CAAC,CAAC;IACrD,CAAC,MAAM;MACLR,QAAQ,CAACQ,CAAC,GACRmC,oBAAoB,GACpB1C,IAAI,CAACC,GAAG,CAACiC,gBAAgB,CAAC3B,CAAC,GAAG2B,gBAAgB,CAACC,CAAC,CAAC;MACnDpC,QAAQ,CAACoC,CAAC,GAAGO,oBAAoB;IACnC;EACF;EAEA,IAAI9B,oBAAoB,EAAE;IACxB,SAAS+B,8BAA8BA,CAAA,EAAG;MACxC,IAAIX,QAAQ,KAAK,GAAG,EAAE;QACpB,OAAOR,yBAAyB,CAACjB,CAAC;MACpC,CAAC,MAAM,IAAIyB,QAAQ,KAAK,GAAG,EAAE;QAC3B,OAAOR,yBAAyB,CAACW,CAAC;MACpC,CAAC,MAAM;QACL,OAAOX,yBAAyB,CAACjB,CAAC,IAAIiB,yBAAyB,CAACW,CAAC;MACnE;IACF;IAEA,SAASS,qCAAqCA,CAAA,EAAG;MAC/CjC,KAAK,CAACI,YAAY,GAAGqB,SAAS,CAAC7B,CAAC,GAAGM,gBAAgB,CAACG,KAAK;MACzDL,KAAK,CAACO,YAAY,GAAGkB,SAAS,CAACD,CAAC,GAAGtB,gBAAgB,CAACM,MAAM;MAC1D7B,2BAA2B,CAACoB,sBAAsB,EAAEC,KAAK,CAAC;IAC5D;IAEA,MAAMkC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMxC,QAAQ,GAAG;QACfE,CAAC,EAAEX,qBAAqB,CAACqC,cAAc,EAAEnC,QAAQ,CAACS,CAAC,EAAER,QAAQ,CAACQ,CAAC,CAAC;QAChE4B,CAAC,EAAEvC,qBAAqB,CAACqC,cAAc,EAAEnC,QAAQ,CAACqC,CAAC,EAAEpC,QAAQ,CAACoC,CAAC;MACjE,CAAC;MACDxB,KAAK,CAACI,YAAY,GAChBmB,gBAAgB,CAAC3B,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAGT,QAAQ,CAACS,CAAC,GAAGD,MAAM,CAACD,QAAQ,CAACE,CAAC,CAAC;MACpEI,KAAK,CAACO,YAAY,GAChBgB,gBAAgB,CAACC,CAAC,GAAGC,SAAS,CAACD,CAAC,GAAGrC,QAAQ,CAACqC,CAAC,GAAG7B,MAAM,CAACD,QAAQ,CAAC8B,CAAC,CAAC;MACpE,IAAIC,SAAS,CAAC7B,CAAC,GAAG,CAAC,EAAE;QACnB,IAAII,KAAK,CAACI,YAAY,IAAI,CAAC,EAAE;UAC3BS,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAG,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAIJ,KAAK,CAACI,YAAY,IAAI,CAAC,EAAE;UAC3BS,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAG,CAAC;QACxB;MACF;MACA,IAAIqB,SAAS,CAACD,CAAC,GAAG,CAAC,EAAE;QACnB,IAAIxB,KAAK,CAACO,YAAY,IAAI,CAAC,EAAE;UAC3BM,yBAAyB,CAACW,CAAC,GAAG,IAAI;UAClCxB,KAAK,CAACO,YAAY,GAAG,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAIP,KAAK,CAACO,YAAY,IAAI,CAAC,EAAE;UAC3BM,yBAAyB,CAACW,CAAC,GAAG,IAAI;UAClCxB,KAAK,CAACO,YAAY,GAAG,CAAC;QACxB;MACF;MACA7B,UAAU,CAACqB,sBAAsB,EAAEC,KAAK,CAAC;MACzC,MAAMmC,QAAQ,GAAGH,8BAA8B,CAAC,CAAC;MACjD,IAAIG,QAAQ,EAAE;QACZF,qCAAqC,CAAC,CAAC;MACzC;MACAtB,sBAAsB,CACpBuB,YAAY,EACZC,QAAQ,EACRpC,sBAAsB,EACtBC,KAAK,EACLC,oBACF,CAAC;IACH,CAAC;IACD,OAAOiC,YAAY;EACrB,CAAC,MAAM;IACL,MAAMA,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMxC,QAAQ,GAAG;QACfE,CAAC,EAAEX,qBAAqB,CAACqC,cAAc,EAAEnC,QAAQ,CAACS,CAAC,EAAER,QAAQ,CAACQ,CAAC,CAAC;QAChE4B,CAAC,EAAEvC,qBAAqB,CAACqC,cAAc,EAAEnC,QAAQ,CAACqC,CAAC,EAAEpC,QAAQ,CAACoC,CAAC;MACjE,CAAC;MACDxB,KAAK,CAACI,YAAY,GAChBmB,gBAAgB,CAAC3B,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAGT,QAAQ,CAACS,CAAC,GAAGD,MAAM,CAACD,QAAQ,CAACE,CAAC,CAAC;MACpEI,KAAK,CAACO,YAAY,GAChBgB,gBAAgB,CAACC,CAAC,GAAGC,SAAS,CAACD,CAAC,GAAGrC,QAAQ,CAACqC,CAAC,GAAG7B,MAAM,CAACD,QAAQ,CAAC8B,CAAC,CAAC;MACpE,IAAIC,SAAS,CAAC7B,CAAC,GAAG,CAAC,EAAE;QACnB,IAAII,KAAK,CAACI,YAAY,IAAIF,gBAAgB,CAACG,KAAK,EAAE;UAChDQ,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAGF,gBAAgB,CAACG,KAAK;QAC7C;MACF,CAAC,MAAM;QACL,IAAIL,KAAK,CAACI,YAAY,IAAI,CAACF,gBAAgB,CAACG,KAAK,EAAE;UACjDQ,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAG,CAACF,gBAAgB,CAACG,KAAK;QAC9C;MACF;MACA,IAAIoB,SAAS,CAACD,CAAC,GAAG,CAAC,EAAE;QACnB,IAAIxB,KAAK,CAACO,YAAY,IAAIL,gBAAgB,CAACM,MAAM,EAAE;UACjDK,yBAAyB,CAACW,CAAC,GAAG,IAAI;UAClCxB,KAAK,CAACO,YAAY,GAAGL,gBAAgB,CAACM,MAAM;QAC9C;MACF,CAAC,MAAM;QACL,IAAIR,KAAK,CAACO,YAAY,IAAI,CAACL,gBAAgB,CAACM,MAAM,EAAE;UAClDK,yBAAyB,CAACW,CAAC,GAAG,IAAI;UAClCxB,KAAK,CAACO,YAAY,GAAG,CAACL,gBAAgB,CAACM,MAAM;QAC/C;MACF;MACA9B,UAAU,CAACqB,sBAAsB,EAAEC,KAAK,CAAC;MACzCW,sBAAsB,CACpBuB,YAAY,EACZrB,yBAAyB,CAACjB,CAAC,IAAIiB,yBAAyB,CAACW,CAAC,EAC1DzB,sBAAsB,EACtBC,KAAK,EACLC,oBACF,CAAC;IACH,CAAC;IACD,OAAOiC,YAAY;EACrB;AACF", "ignoreList": []}