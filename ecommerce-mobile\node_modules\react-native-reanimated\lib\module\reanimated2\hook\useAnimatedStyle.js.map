{"version": 3, "names": ["useEffect", "useRef", "makeShareable", "startMapper", "stopMapper", "updateProps", "updatePropsJestWrapper", "initialUpdaterRun", "useSharedValue", "buildWorkletsHash", "isAnimated", "shallowEqual", "validateAnimatedStyles", "makeViewDescriptorsSet", "useViewRefSet", "isJest", "shouldBeUseWeb", "isWorkletFunction", "SHOULD_BE_USE_WEB", "prepareAnimation", "frameTimestamp", "animatedProp", "lastAnimation", "lastValue", "Array", "isArray", "for<PERSON>ach", "prop", "index", "onFrame", "animation", "value", "current", "undefined", "callStart", "timestamp", "onStart", "Object", "keys", "key", "runAnimations", "result", "animationsActive", "allFinished", "entry", "finished", "callback", "k", "styleUpdater", "viewDescriptors", "updater", "state", "maybeViewRef", "isAnimatedProps", "animations", "newValues", "oldValues", "last", "nonAnimatedNewValues", "hasAnimations", "hasNonAnimatedValues", "global", "__frameTimestamp", "_getAnimationTimestamp", "frame", "isAnimationCancelled", "isAnimationRunning", "updates", "propName", "requestAnimationFrame", "jestStyleUpdater", "animatedStyle", "adapters", "length", "checkSharedValueUsage", "current<PERSON><PERSON>", "element", "Error", "useAnimatedStyle", "dependencies", "viewsRef", "animatedUpdaterData", "inputs", "values", "__closure", "_dependencies", "__DEV__", "adaptersArray", "adaptersHash", "areAnimationsActive", "jestAnimatedStyle", "__workletHash", "push", "initialStyle", "initial", "remoteState", "shareableViewDescriptors", "fun", "updaterFn", "adapter", "mapperId", "animatedStyleHandle"], "sources": ["useAnimatedStyle.ts"], "sourcesContent": ["'use strict';\nimport type { MutableRefObject } from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport { makeShareable, startMapper, stopMapper } from '../core';\nimport updateProps, { updatePropsJestWrapper } from '../UpdateProps';\nimport { initialUpdaterRun } from '../animation';\nimport { useSharedValue } from './useSharedValue';\nimport {\n  buildWorkletsHash,\n  isAnimated,\n  shallowEqual,\n  validateAnimatedStyles,\n} from './utils';\nimport type {\n  AnimatedStyleHandle,\n  DefaultStyle,\n  DependencyList,\n  Descriptor,\n  JestAnimatedStyleHandle,\n} from './commonTypes';\nimport type { ViewDescriptorsSet, ViewRefSet } from '../ViewDescriptorsSet';\nimport { makeViewDescriptorsSet, useViewRefSet } from '../ViewDescriptorsSet';\nimport { isJest, shouldBeUseWeb } from '../PlatformChecker';\nimport type {\n  AnimationObject,\n  Timestamp,\n  NestedObjectValues,\n  SharedValue,\n  StyleProps,\n  WorkletFunction,\n  AnimatedPropsAdapterFunction,\n  AnimatedPropsAdapterWorklet,\n} from '../commonTypes';\nimport type { AnimatedStyle } from '../helperTypes';\nimport { isWorkletFunction } from '../commonTypes';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\ninterface AnimatedState {\n  last: AnimatedStyle<any>;\n  animations: AnimatedStyle<any>;\n  isAnimationRunning: boolean;\n  isAnimationCancelled: boolean;\n}\n\ninterface AnimatedUpdaterData {\n  initial: {\n    value: AnimatedStyle<any>;\n    updater: () => AnimatedStyle<any>;\n  };\n  remoteState: AnimatedState;\n  viewDescriptors: ViewDescriptorsSet;\n}\n\nfunction prepareAnimation(\n  frameTimestamp: number,\n  animatedProp: AnimatedStyle<any>,\n  lastAnimation: AnimatedStyle<any>,\n  lastValue: AnimatedStyle<any>\n): void {\n  'worklet';\n  if (Array.isArray(animatedProp)) {\n    animatedProp.forEach((prop, index) => {\n      prepareAnimation(\n        frameTimestamp,\n        prop,\n        lastAnimation && lastAnimation[index],\n        lastValue && lastValue[index]\n      );\n    });\n    // return animatedProp;\n  }\n  if (typeof animatedProp === 'object' && animatedProp.onFrame) {\n    const animation = animatedProp;\n\n    let value = animation.current;\n    if (lastValue !== undefined && lastValue !== null) {\n      if (typeof lastValue === 'object') {\n        if (lastValue.value !== undefined) {\n          // previously it was a shared value\n          value = lastValue.value;\n        } else if (lastValue.onFrame !== undefined) {\n          if (lastAnimation?.current !== undefined) {\n            // it was an animation before, copy its state\n            value = lastAnimation.current;\n          } else if (lastValue?.current !== undefined) {\n            // it was initialized\n            value = lastValue.current;\n          }\n        }\n      } else {\n        // previously it was a plain value, just set it as starting point\n        value = lastValue;\n      }\n    }\n\n    animation.callStart = (timestamp: Timestamp) => {\n      animation.onStart(animation, value, timestamp, lastAnimation);\n    };\n    animation.callStart(frameTimestamp);\n    animation.callStart = null;\n  } else if (typeof animatedProp === 'object') {\n    // it is an object\n    Object.keys(animatedProp).forEach((key) =>\n      prepareAnimation(\n        frameTimestamp,\n        animatedProp[key],\n        lastAnimation && lastAnimation[key],\n        lastValue && lastValue[key]\n      )\n    );\n  }\n}\n\nfunction runAnimations(\n  animation: AnimatedStyle<any>,\n  timestamp: Timestamp,\n  key: number | string,\n  result: AnimatedStyle<any>,\n  animationsActive: SharedValue<boolean>\n): boolean {\n  'worklet';\n  if (!animationsActive.value) {\n    return true;\n  }\n  if (Array.isArray(animation)) {\n    result[key] = [];\n    let allFinished = true;\n    animation.forEach((entry, index) => {\n      if (\n        !runAnimations(entry, timestamp, index, result[key], animationsActive)\n      ) {\n        allFinished = false;\n      }\n    });\n    return allFinished;\n  } else if (typeof animation === 'object' && animation.onFrame) {\n    let finished = true;\n    if (!animation.finished) {\n      if (animation.callStart) {\n        animation.callStart(timestamp);\n        animation.callStart = null;\n      }\n      finished = animation.onFrame(animation, timestamp);\n      animation.timestamp = timestamp;\n      if (finished) {\n        animation.finished = true;\n        animation.callback && animation.callback(true /* finished */);\n      }\n    }\n    result[key] = animation.current;\n    return finished;\n  } else if (typeof animation === 'object') {\n    result[key] = {};\n    let allFinished = true;\n    Object.keys(animation).forEach((k) => {\n      if (\n        !runAnimations(\n          animation[k],\n          timestamp,\n          k,\n          result[key],\n          animationsActive\n        )\n      ) {\n        allFinished = false;\n      }\n    });\n    return allFinished;\n  } else {\n    result[key] = animation;\n    return true;\n  }\n}\n\nfunction styleUpdater(\n  viewDescriptors: SharedValue<Descriptor[]>,\n  updater: WorkletFunction<[], AnimatedStyle<any>> | (() => AnimatedStyle<any>),\n  state: AnimatedState,\n  maybeViewRef: ViewRefSet<any> | undefined,\n  animationsActive: SharedValue<boolean>,\n  isAnimatedProps = false\n): void {\n  'worklet';\n  const animations = state.animations ?? {};\n  const newValues = updater() ?? {};\n  const oldValues = state.last;\n  const nonAnimatedNewValues: StyleProps = {};\n\n  let hasAnimations = false;\n  let frameTimestamp: number | undefined;\n  let hasNonAnimatedValues = false;\n  for (const key in newValues) {\n    const value = newValues[key];\n    if (isAnimated(value)) {\n      frameTimestamp =\n        global.__frameTimestamp || global._getAnimationTimestamp();\n      prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n      animations[key] = value;\n      hasAnimations = true;\n    } else {\n      hasNonAnimatedValues = true;\n      nonAnimatedNewValues[key] = value;\n      delete animations[key];\n    }\n  }\n\n  if (hasAnimations) {\n    const frame = (timestamp: Timestamp) => {\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      const { animations, last, isAnimationCancelled } = state;\n      if (isAnimationCancelled) {\n        state.isAnimationRunning = false;\n        return;\n      }\n\n      const updates: AnimatedStyle<any> = {};\n      let allFinished = true;\n      for (const propName in animations) {\n        const finished = runAnimations(\n          animations[propName],\n          timestamp,\n          propName,\n          updates,\n          animationsActive\n        );\n        if (finished) {\n          last[propName] = updates[propName];\n          delete animations[propName];\n        } else {\n          allFinished = false;\n        }\n      }\n\n      if (updates) {\n        updateProps(viewDescriptors, updates, maybeViewRef);\n      }\n\n      if (!allFinished) {\n        requestAnimationFrame(frame);\n      } else {\n        state.isAnimationRunning = false;\n      }\n    };\n\n    state.animations = animations;\n    if (!state.isAnimationRunning) {\n      state.isAnimationCancelled = false;\n      state.isAnimationRunning = true;\n      frame(frameTimestamp!);\n    }\n\n    if (hasNonAnimatedValues) {\n      updateProps(viewDescriptors, nonAnimatedNewValues, maybeViewRef);\n    }\n  } else {\n    state.isAnimationCancelled = true;\n    state.animations = [];\n\n    if (!shallowEqual(oldValues, newValues)) {\n      updateProps(viewDescriptors, newValues, maybeViewRef, isAnimatedProps);\n    }\n  }\n  state.last = newValues;\n}\n\nfunction jestStyleUpdater(\n  viewDescriptors: SharedValue<Descriptor[]>,\n  updater: WorkletFunction<[], AnimatedStyle<any>> | (() => AnimatedStyle<any>),\n  state: AnimatedState,\n  maybeViewRef: ViewRefSet<any> | undefined,\n  animationsActive: SharedValue<boolean>,\n  animatedStyle: MutableRefObject<AnimatedStyle<any>>,\n  adapters: AnimatedPropsAdapterFunction[]\n): void {\n  'worklet';\n  const animations: AnimatedStyle<any> = state.animations ?? {};\n  const newValues = updater() ?? {};\n  const oldValues = state.last;\n\n  // extract animated props\n  let hasAnimations = false;\n  let frameTimestamp: number | undefined;\n  Object.keys(animations).forEach((key) => {\n    const value = newValues[key];\n    if (!isAnimated(value)) {\n      delete animations[key];\n    }\n  });\n  Object.keys(newValues).forEach((key) => {\n    const value = newValues[key];\n    if (isAnimated(value)) {\n      frameTimestamp =\n        global.__frameTimestamp || global._getAnimationTimestamp();\n      prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n      animations[key] = value;\n      hasAnimations = true;\n    }\n  });\n\n  function frame(timestamp: Timestamp) {\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const { animations, last, isAnimationCancelled } = state;\n    if (isAnimationCancelled) {\n      state.isAnimationRunning = false;\n      return;\n    }\n\n    const updates: AnimatedStyle<any> = {};\n    let allFinished = true;\n    Object.keys(animations).forEach((propName) => {\n      const finished = runAnimations(\n        animations[propName],\n        timestamp,\n        propName,\n        updates,\n        animationsActive\n      );\n      if (finished) {\n        last[propName] = updates[propName];\n        delete animations[propName];\n      } else {\n        allFinished = false;\n      }\n    });\n\n    if (Object.keys(updates).length) {\n      updatePropsJestWrapper(\n        viewDescriptors,\n        updates,\n        maybeViewRef,\n        animatedStyle,\n        adapters\n      );\n    }\n\n    if (!allFinished) {\n      requestAnimationFrame(frame);\n    } else {\n      state.isAnimationRunning = false;\n    }\n  }\n\n  if (hasAnimations) {\n    state.animations = animations;\n    if (!state.isAnimationRunning) {\n      state.isAnimationCancelled = false;\n      state.isAnimationRunning = true;\n      frame(frameTimestamp!);\n    }\n  } else {\n    state.isAnimationCancelled = true;\n    state.animations = [];\n  }\n\n  // calculate diff\n  state.last = newValues;\n\n  if (!shallowEqual(oldValues, newValues)) {\n    updatePropsJestWrapper(\n      viewDescriptors,\n      newValues,\n      maybeViewRef,\n      animatedStyle,\n      adapters\n    );\n  }\n}\n\n// check for invalid usage of shared values in returned object\nfunction checkSharedValueUsage(\n  prop: NestedObjectValues<AnimationObject>,\n  currentKey?: string\n): void {\n  if (Array.isArray(prop)) {\n    // if it's an array (i.ex. transform) validate all its elements\n    for (const element of prop) {\n      checkSharedValueUsage(element, currentKey);\n    }\n  } else if (\n    typeof prop === 'object' &&\n    prop !== null &&\n    prop.value === undefined\n  ) {\n    // if it's a nested object, run validation for all its props\n    for (const key of Object.keys(prop)) {\n      checkSharedValueUsage(prop[key], key);\n    }\n  } else if (\n    currentKey !== undefined &&\n    typeof prop === 'object' &&\n    prop !== null &&\n    prop.value !== undefined\n  ) {\n    // if shared value is passed insted of its value, throw an error\n    throw new Error(\n      `[Reanimated] Invalid value passed to \\`${currentKey}\\`, maybe you forgot to use \\`.value\\`?`\n    );\n  }\n}\n\n/**\n * Lets you create a styles object, similar to StyleSheet styles, which can be animated using shared values.\n *\n * @param updater - A function returning an object with style properties you want to animate.\n * @param dependencies - An optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.\n * @returns An animated style object which has to be passed to the `style` property of an Animated component you want to animate.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle\n */\n// You cannot pass Shared Values to `useAnimatedStyle` directly.\n// @ts-expect-error This overload is required by our API.\nexport function useAnimatedStyle<Style extends DefaultStyle>(\n  updater: () => Style,\n  dependencies?: DependencyList | null\n): Style;\n\nexport function useAnimatedStyle<Style extends DefaultStyle>(\n  updater:\n    | WorkletFunction<[], Style>\n    | ((() => Style) & Record<string, unknown>),\n  dependencies?: DependencyList | null,\n  adapters?: AnimatedPropsAdapterWorklet | AnimatedPropsAdapterWorklet[] | null,\n  isAnimatedProps = false\n): AnimatedStyleHandle<Style> | JestAnimatedStyleHandle<Style> {\n  const viewsRef: ViewRefSet<unknown> | undefined = useViewRefSet();\n  const animatedUpdaterData = useRef<AnimatedUpdaterData>();\n  let inputs = Object.values(updater.__closure ?? {});\n  if (SHOULD_BE_USE_WEB) {\n    if (!inputs.length && dependencies?.length) {\n      // let web work without a Babel plugin\n      inputs = dependencies;\n    }\n    if (\n      __DEV__ &&\n      !inputs.length &&\n      !dependencies &&\n      !isWorkletFunction(updater)\n    ) {\n      throw new Error(\n        `[Reanimated] \\`useAnimatedStyle\\` was used without a dependency array or Babel plugin. Please explicitly pass a dependency array, or enable the Babel plugin.\nFor more, see the docs: \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/web-support#web-without-the-babel-plugin\\`.`\n      );\n    }\n  }\n  const adaptersArray = adapters\n    ? Array.isArray(adapters)\n      ? adapters\n      : [adapters]\n    : [];\n  const adaptersHash = adapters ? buildWorkletsHash(adaptersArray) : null;\n  const areAnimationsActive = useSharedValue<boolean>(true);\n  const jestAnimatedStyle = useRef<Style>({} as Style);\n\n  // build dependencies\n  if (!dependencies) {\n    dependencies = [...inputs, updater.__workletHash];\n  } else {\n    dependencies.push(updater.__workletHash);\n  }\n  adaptersHash && dependencies.push(adaptersHash);\n\n  if (!animatedUpdaterData.current) {\n    const initialStyle = initialUpdaterRun(updater);\n    if (__DEV__) {\n      validateAnimatedStyles(initialStyle);\n    }\n    animatedUpdaterData.current = {\n      initial: {\n        value: initialStyle,\n        updater,\n      },\n      remoteState: makeShareable({\n        last: initialStyle,\n        animations: {},\n        isAnimationCancelled: false,\n        isAnimationRunning: false,\n      }),\n      viewDescriptors: makeViewDescriptorsSet(),\n    };\n  }\n\n  const { initial, remoteState, viewDescriptors } = animatedUpdaterData.current;\n  const shareableViewDescriptors = viewDescriptors.shareableViewDescriptors;\n\n  dependencies.push(shareableViewDescriptors);\n\n  useEffect(() => {\n    let fun;\n    let updaterFn = updater;\n    if (adapters) {\n      updaterFn = (() => {\n        'worklet';\n        const newValues = updater();\n        adaptersArray.forEach((adapter) => {\n          adapter(newValues as Record<string, unknown>);\n        });\n        return newValues;\n      }) as WorkletFunction<[], Style>;\n    }\n\n    if (isJest()) {\n      fun = () => {\n        'worklet';\n        jestStyleUpdater(\n          shareableViewDescriptors,\n          updater,\n          remoteState,\n          viewsRef,\n          areAnimationsActive,\n          jestAnimatedStyle,\n          adaptersArray\n        );\n      };\n    } else {\n      fun = () => {\n        'worklet';\n        styleUpdater(\n          shareableViewDescriptors,\n          updaterFn,\n          remoteState,\n          viewsRef,\n          areAnimationsActive,\n          isAnimatedProps\n        );\n      };\n    }\n    const mapperId = startMapper(fun, inputs);\n    return () => {\n      stopMapper(mapperId);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n\n  useEffect(() => {\n    areAnimationsActive.value = true;\n    return () => {\n      areAnimationsActive.value = false;\n    };\n  }, [areAnimationsActive]);\n\n  checkSharedValueUsage(initial.value);\n\n  const animatedStyleHandle = useRef<\n    AnimatedStyleHandle<Style> | JestAnimatedStyleHandle<Style> | null\n  >(null);\n\n  if (!animatedStyleHandle.current) {\n    animatedStyleHandle.current = isJest()\n      ? { viewDescriptors, initial, viewsRef, jestAnimatedStyle }\n      : { initial, viewsRef, viewDescriptors };\n  }\n\n  return animatedStyleHandle.current;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,SAASC,aAAa,EAAEC,WAAW,EAAEC,UAAU,QAAQ,SAAS;AAChE,OAAOC,WAAW,IAAIC,sBAAsB,QAAQ,gBAAgB;AACpE,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SACEC,iBAAiB,EACjBC,UAAU,EACVC,YAAY,EACZC,sBAAsB,QACjB,SAAS;AAShB,SAASC,sBAAsB,EAAEC,aAAa,QAAQ,uBAAuB;AAC7E,SAASC,MAAM,EAAEC,cAAc,QAAQ,oBAAoB;AAY3D,SAASC,iBAAiB,QAAQ,gBAAgB;AAElD,MAAMC,iBAAiB,GAAGF,cAAc,CAAC,CAAC;AAkB1C,SAASG,gBAAgBA,CACvBC,cAAsB,EACtBC,YAAgC,EAChCC,aAAiC,EACjCC,SAA6B,EACvB;EACN,SAAS;;EACT,IAAIC,KAAK,CAACC,OAAO,CAACJ,YAAY,CAAC,EAAE;IAC/BA,YAAY,CAACK,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACpCT,gBAAgB,CACdC,cAAc,EACdO,IAAI,EACJL,aAAa,IAAIA,aAAa,CAACM,KAAK,CAAC,EACrCL,SAAS,IAAIA,SAAS,CAACK,KAAK,CAC9B,CAAC;IACH,CAAC,CAAC;IACF;EACF;EACA,IAAI,OAAOP,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACQ,OAAO,EAAE;IAC5D,MAAMC,SAAS,GAAGT,YAAY;IAE9B,IAAIU,KAAK,GAAGD,SAAS,CAACE,OAAO;IAC7B,IAAIT,SAAS,KAAKU,SAAS,IAAIV,SAAS,KAAK,IAAI,EAAE;MACjD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAIA,SAAS,CAACQ,KAAK,KAAKE,SAAS,EAAE;UACjC;UACAF,KAAK,GAAGR,SAAS,CAACQ,KAAK;QACzB,CAAC,MAAM,IAAIR,SAAS,CAACM,OAAO,KAAKI,SAAS,EAAE;UAC1C,IAAI,CAAAX,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,OAAO,MAAKC,SAAS,EAAE;YACxC;YACAF,KAAK,GAAGT,aAAa,CAACU,OAAO;UAC/B,CAAC,MAAM,IAAI,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,OAAO,MAAKC,SAAS,EAAE;YAC3C;YACAF,KAAK,GAAGR,SAAS,CAACS,OAAO;UAC3B;QACF;MACF,CAAC,MAAM;QACL;QACAD,KAAK,GAAGR,SAAS;MACnB;IACF;IAEAO,SAAS,CAACI,SAAS,GAAIC,SAAoB,IAAK;MAC9CL,SAAS,CAACM,OAAO,CAACN,SAAS,EAAEC,KAAK,EAAEI,SAAS,EAAEb,aAAa,CAAC;IAC/D,CAAC;IACDQ,SAAS,CAACI,SAAS,CAACd,cAAc,CAAC;IACnCU,SAAS,CAACI,SAAS,GAAG,IAAI;EAC5B,CAAC,MAAM,IAAI,OAAOb,YAAY,KAAK,QAAQ,EAAE;IAC3C;IACAgB,MAAM,CAACC,IAAI,CAACjB,YAAY,CAAC,CAACK,OAAO,CAAEa,GAAG,IACpCpB,gBAAgB,CACdC,cAAc,EACdC,YAAY,CAACkB,GAAG,CAAC,EACjBjB,aAAa,IAAIA,aAAa,CAACiB,GAAG,CAAC,EACnChB,SAAS,IAAIA,SAAS,CAACgB,GAAG,CAC5B,CACF,CAAC;EACH;AACF;AAEA,SAASC,aAAaA,CACpBV,SAA6B,EAC7BK,SAAoB,EACpBI,GAAoB,EACpBE,MAA0B,EAC1BC,gBAAsC,EAC7B;EACT,SAAS;;EACT,IAAI,CAACA,gBAAgB,CAACX,KAAK,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAIP,KAAK,CAACC,OAAO,CAACK,SAAS,CAAC,EAAE;IAC5BW,MAAM,CAACF,GAAG,CAAC,GAAG,EAAE;IAChB,IAAII,WAAW,GAAG,IAAI;IACtBb,SAAS,CAACJ,OAAO,CAAC,CAACkB,KAAK,EAAEhB,KAAK,KAAK;MAClC,IACE,CAACY,aAAa,CAACI,KAAK,EAAET,SAAS,EAAEP,KAAK,EAAEa,MAAM,CAACF,GAAG,CAAC,EAAEG,gBAAgB,CAAC,EACtE;QACAC,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,CAAC;IACF,OAAOA,WAAW;EACpB,CAAC,MAAM,IAAI,OAAOb,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACD,OAAO,EAAE;IAC7D,IAAIgB,QAAQ,GAAG,IAAI;IACnB,IAAI,CAACf,SAAS,CAACe,QAAQ,EAAE;MACvB,IAAIf,SAAS,CAACI,SAAS,EAAE;QACvBJ,SAAS,CAACI,SAAS,CAACC,SAAS,CAAC;QAC9BL,SAAS,CAACI,SAAS,GAAG,IAAI;MAC5B;MACAW,QAAQ,GAAGf,SAAS,CAACD,OAAO,CAACC,SAAS,EAAEK,SAAS,CAAC;MAClDL,SAAS,CAACK,SAAS,GAAGA,SAAS;MAC/B,IAAIU,QAAQ,EAAE;QACZf,SAAS,CAACe,QAAQ,GAAG,IAAI;QACzBf,SAAS,CAACgB,QAAQ,IAAIhB,SAAS,CAACgB,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;MAC/D;IACF;IACAL,MAAM,CAACF,GAAG,CAAC,GAAGT,SAAS,CAACE,OAAO;IAC/B,OAAOa,QAAQ;EACjB,CAAC,MAAM,IAAI,OAAOf,SAAS,KAAK,QAAQ,EAAE;IACxCW,MAAM,CAACF,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,IAAII,WAAW,GAAG,IAAI;IACtBN,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACJ,OAAO,CAAEqB,CAAC,IAAK;MACpC,IACE,CAACP,aAAa,CACZV,SAAS,CAACiB,CAAC,CAAC,EACZZ,SAAS,EACTY,CAAC,EACDN,MAAM,CAACF,GAAG,CAAC,EACXG,gBACF,CAAC,EACD;QACAC,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,CAAC;IACF,OAAOA,WAAW;EACpB,CAAC,MAAM;IACLF,MAAM,CAACF,GAAG,CAAC,GAAGT,SAAS;IACvB,OAAO,IAAI;EACb;AACF;AAEA,SAASkB,YAAYA,CACnBC,eAA0C,EAC1CC,OAA6E,EAC7EC,KAAoB,EACpBC,YAAyC,EACzCV,gBAAsC,EACtCW,eAAe,GAAG,KAAK,EACjB;EACN,SAAS;;EACT,MAAMC,UAAU,GAAGH,KAAK,CAACG,UAAU,IAAI,CAAC,CAAC;EACzC,MAAMC,SAAS,GAAGL,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMM,SAAS,GAAGL,KAAK,CAACM,IAAI;EAC5B,MAAMC,oBAAgC,GAAG,CAAC,CAAC;EAE3C,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIvC,cAAkC;EACtC,IAAIwC,oBAAoB,GAAG,KAAK;EAChC,KAAK,MAAMrB,GAAG,IAAIgB,SAAS,EAAE;IAC3B,MAAMxB,KAAK,GAAGwB,SAAS,CAAChB,GAAG,CAAC;IAC5B,IAAI7B,UAAU,CAACqB,KAAK,CAAC,EAAE;MACrBX,cAAc,GACZyC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;MAC5D5C,gBAAgB,CAACC,cAAc,EAAEW,KAAK,EAAEuB,UAAU,CAACf,GAAG,CAAC,EAAEiB,SAAS,CAACjB,GAAG,CAAC,CAAC;MACxEe,UAAU,CAACf,GAAG,CAAC,GAAGR,KAAK;MACvB4B,aAAa,GAAG,IAAI;IACtB,CAAC,MAAM;MACLC,oBAAoB,GAAG,IAAI;MAC3BF,oBAAoB,CAACnB,GAAG,CAAC,GAAGR,KAAK;MACjC,OAAOuB,UAAU,CAACf,GAAG,CAAC;IACxB;EACF;EAEA,IAAIoB,aAAa,EAAE;IACjB,MAAMK,KAAK,GAAI7B,SAAoB,IAAK;MACtC;MACA,MAAM;QAAEmB,UAAU;QAAEG,IAAI;QAAEQ;MAAqB,CAAC,GAAGd,KAAK;MACxD,IAAIc,oBAAoB,EAAE;QACxBd,KAAK,CAACe,kBAAkB,GAAG,KAAK;QAChC;MACF;MAEA,MAAMC,OAA2B,GAAG,CAAC,CAAC;MACtC,IAAIxB,WAAW,GAAG,IAAI;MACtB,KAAK,MAAMyB,QAAQ,IAAId,UAAU,EAAE;QACjC,MAAMT,QAAQ,GAAGL,aAAa,CAC5Bc,UAAU,CAACc,QAAQ,CAAC,EACpBjC,SAAS,EACTiC,QAAQ,EACRD,OAAO,EACPzB,gBACF,CAAC;QACD,IAAIG,QAAQ,EAAE;UACZY,IAAI,CAACW,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC;UAClC,OAAOd,UAAU,CAACc,QAAQ,CAAC;QAC7B,CAAC,MAAM;UACLzB,WAAW,GAAG,KAAK;QACrB;MACF;MAEA,IAAIwB,OAAO,EAAE;QACX9D,WAAW,CAAC4C,eAAe,EAAEkB,OAAO,EAAEf,YAAY,CAAC;MACrD;MAEA,IAAI,CAACT,WAAW,EAAE;QAChB0B,qBAAqB,CAACL,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLb,KAAK,CAACe,kBAAkB,GAAG,KAAK;MAClC;IACF,CAAC;IAEDf,KAAK,CAACG,UAAU,GAAGA,UAAU;IAC7B,IAAI,CAACH,KAAK,CAACe,kBAAkB,EAAE;MAC7Bf,KAAK,CAACc,oBAAoB,GAAG,KAAK;MAClCd,KAAK,CAACe,kBAAkB,GAAG,IAAI;MAC/BF,KAAK,CAAC5C,cAAe,CAAC;IACxB;IAEA,IAAIwC,oBAAoB,EAAE;MACxBvD,WAAW,CAAC4C,eAAe,EAAES,oBAAoB,EAAEN,YAAY,CAAC;IAClE;EACF,CAAC,MAAM;IACLD,KAAK,CAACc,oBAAoB,GAAG,IAAI;IACjCd,KAAK,CAACG,UAAU,GAAG,EAAE;IAErB,IAAI,CAAC3C,YAAY,CAAC6C,SAAS,EAAED,SAAS,CAAC,EAAE;MACvClD,WAAW,CAAC4C,eAAe,EAAEM,SAAS,EAAEH,YAAY,EAAEC,eAAe,CAAC;IACxE;EACF;EACAF,KAAK,CAACM,IAAI,GAAGF,SAAS;AACxB;AAEA,SAASe,gBAAgBA,CACvBrB,eAA0C,EAC1CC,OAA6E,EAC7EC,KAAoB,EACpBC,YAAyC,EACzCV,gBAAsC,EACtC6B,aAAmD,EACnDC,QAAwC,EAClC;EACN,SAAS;;EACT,MAAMlB,UAA8B,GAAGH,KAAK,CAACG,UAAU,IAAI,CAAC,CAAC;EAC7D,MAAMC,SAAS,GAAGL,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMM,SAAS,GAAGL,KAAK,CAACM,IAAI;;EAE5B;EACA,IAAIE,aAAa,GAAG,KAAK;EACzB,IAAIvC,cAAkC;EACtCiB,MAAM,CAACC,IAAI,CAACgB,UAAU,CAAC,CAAC5B,OAAO,CAAEa,GAAG,IAAK;IACvC,MAAMR,KAAK,GAAGwB,SAAS,CAAChB,GAAG,CAAC;IAC5B,IAAI,CAAC7B,UAAU,CAACqB,KAAK,CAAC,EAAE;MACtB,OAAOuB,UAAU,CAACf,GAAG,CAAC;IACxB;EACF,CAAC,CAAC;EACFF,MAAM,CAACC,IAAI,CAACiB,SAAS,CAAC,CAAC7B,OAAO,CAAEa,GAAG,IAAK;IACtC,MAAMR,KAAK,GAAGwB,SAAS,CAAChB,GAAG,CAAC;IAC5B,IAAI7B,UAAU,CAACqB,KAAK,CAAC,EAAE;MACrBX,cAAc,GACZyC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;MAC5D5C,gBAAgB,CAACC,cAAc,EAAEW,KAAK,EAAEuB,UAAU,CAACf,GAAG,CAAC,EAAEiB,SAAS,CAACjB,GAAG,CAAC,CAAC;MACxEe,UAAU,CAACf,GAAG,CAAC,GAAGR,KAAK;MACvB4B,aAAa,GAAG,IAAI;IACtB;EACF,CAAC,CAAC;EAEF,SAASK,KAAKA,CAAC7B,SAAoB,EAAE;IACnC;IACA,MAAM;MAAEmB,UAAU;MAAEG,IAAI;MAAEQ;IAAqB,CAAC,GAAGd,KAAK;IACxD,IAAIc,oBAAoB,EAAE;MACxBd,KAAK,CAACe,kBAAkB,GAAG,KAAK;MAChC;IACF;IAEA,MAAMC,OAA2B,GAAG,CAAC,CAAC;IACtC,IAAIxB,WAAW,GAAG,IAAI;IACtBN,MAAM,CAACC,IAAI,CAACgB,UAAU,CAAC,CAAC5B,OAAO,CAAE0C,QAAQ,IAAK;MAC5C,MAAMvB,QAAQ,GAAGL,aAAa,CAC5Bc,UAAU,CAACc,QAAQ,CAAC,EACpBjC,SAAS,EACTiC,QAAQ,EACRD,OAAO,EACPzB,gBACF,CAAC;MACD,IAAIG,QAAQ,EAAE;QACZY,IAAI,CAACW,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC;QAClC,OAAOd,UAAU,CAACc,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACLzB,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,CAAC;IAEF,IAAIN,MAAM,CAACC,IAAI,CAAC6B,OAAO,CAAC,CAACM,MAAM,EAAE;MAC/BnE,sBAAsB,CACpB2C,eAAe,EACfkB,OAAO,EACPf,YAAY,EACZmB,aAAa,EACbC,QACF,CAAC;IACH;IAEA,IAAI,CAAC7B,WAAW,EAAE;MAChB0B,qBAAqB,CAACL,KAAK,CAAC;IAC9B,CAAC,MAAM;MACLb,KAAK,CAACe,kBAAkB,GAAG,KAAK;IAClC;EACF;EAEA,IAAIP,aAAa,EAAE;IACjBR,KAAK,CAACG,UAAU,GAAGA,UAAU;IAC7B,IAAI,CAACH,KAAK,CAACe,kBAAkB,EAAE;MAC7Bf,KAAK,CAACc,oBAAoB,GAAG,KAAK;MAClCd,KAAK,CAACe,kBAAkB,GAAG,IAAI;MAC/BF,KAAK,CAAC5C,cAAe,CAAC;IACxB;EACF,CAAC,MAAM;IACL+B,KAAK,CAACc,oBAAoB,GAAG,IAAI;IACjCd,KAAK,CAACG,UAAU,GAAG,EAAE;EACvB;;EAEA;EACAH,KAAK,CAACM,IAAI,GAAGF,SAAS;EAEtB,IAAI,CAAC5C,YAAY,CAAC6C,SAAS,EAAED,SAAS,CAAC,EAAE;IACvCjD,sBAAsB,CACpB2C,eAAe,EACfM,SAAS,EACTH,YAAY,EACZmB,aAAa,EACbC,QACF,CAAC;EACH;AACF;;AAEA;AACA,SAASE,qBAAqBA,CAC5B/C,IAAyC,EACzCgD,UAAmB,EACb;EACN,IAAInD,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,EAAE;IACvB;IACA,KAAK,MAAMiD,OAAO,IAAIjD,IAAI,EAAE;MAC1B+C,qBAAqB,CAACE,OAAO,EAAED,UAAU,CAAC;IAC5C;EACF,CAAC,MAAM,IACL,OAAOhD,IAAI,KAAK,QAAQ,IACxBA,IAAI,KAAK,IAAI,IACbA,IAAI,CAACI,KAAK,KAAKE,SAAS,EACxB;IACA;IACA,KAAK,MAAMM,GAAG,IAAIF,MAAM,CAACC,IAAI,CAACX,IAAI,CAAC,EAAE;MACnC+C,qBAAqB,CAAC/C,IAAI,CAACY,GAAG,CAAC,EAAEA,GAAG,CAAC;IACvC;EACF,CAAC,MAAM,IACLoC,UAAU,KAAK1C,SAAS,IACxB,OAAON,IAAI,KAAK,QAAQ,IACxBA,IAAI,KAAK,IAAI,IACbA,IAAI,CAACI,KAAK,KAAKE,SAAS,EACxB;IACA;IACA,MAAM,IAAI4C,KAAK,CACZ,0CAAyCF,UAAW,yCACvD,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASG,gBAAgBA,CAC9B5B,OAE6C,EAC7C6B,YAAoC,EACpCP,QAA6E,EAC7EnB,eAAe,GAAG,KAAK,EACsC;EAC7D,MAAM2B,QAAyC,GAAGlE,aAAa,CAAC,CAAC;EACjE,MAAMmE,mBAAmB,GAAGhF,MAAM,CAAsB,CAAC;EACzD,IAAIiF,MAAM,GAAG7C,MAAM,CAAC8C,MAAM,CAACjC,OAAO,CAACkC,SAAS,IAAI,CAAC,CAAC,CAAC;EACnD,IAAIlE,iBAAiB,EAAE;IAAA,IAAAmE,aAAA;IACrB,IAAI,CAACH,MAAM,CAACT,MAAM,KAAAY,aAAA,GAAIN,YAAY,cAAAM,aAAA,eAAZA,aAAA,CAAcZ,MAAM,EAAE;MAC1C;MACAS,MAAM,GAAGH,YAAY;IACvB;IACA,IACEO,OAAO,IACP,CAACJ,MAAM,CAACT,MAAM,IACd,CAACM,YAAY,IACb,CAAC9D,iBAAiB,CAACiC,OAAO,CAAC,EAC3B;MACA,MAAM,IAAI2B,KAAK,CACZ;AACT,qIACM,CAAC;IACH;EACF;EACA,MAAMU,aAAa,GAAGf,QAAQ,GAC1BhD,KAAK,CAACC,OAAO,CAAC+C,QAAQ,CAAC,GACrBA,QAAQ,GACR,CAACA,QAAQ,CAAC,GACZ,EAAE;EACN,MAAMgB,YAAY,GAAGhB,QAAQ,GAAG/D,iBAAiB,CAAC8E,aAAa,CAAC,GAAG,IAAI;EACvE,MAAME,mBAAmB,GAAGjF,cAAc,CAAU,IAAI,CAAC;EACzD,MAAMkF,iBAAiB,GAAGzF,MAAM,CAAQ,CAAC,CAAU,CAAC;;EAEpD;EACA,IAAI,CAAC8E,YAAY,EAAE;IACjBA,YAAY,GAAG,CAAC,GAAGG,MAAM,EAAEhC,OAAO,CAACyC,aAAa,CAAC;EACnD,CAAC,MAAM;IACLZ,YAAY,CAACa,IAAI,CAAC1C,OAAO,CAACyC,aAAa,CAAC;EAC1C;EACAH,YAAY,IAAIT,YAAY,CAACa,IAAI,CAACJ,YAAY,CAAC;EAE/C,IAAI,CAACP,mBAAmB,CAACjD,OAAO,EAAE;IAChC,MAAM6D,YAAY,GAAGtF,iBAAiB,CAAC2C,OAAO,CAAC;IAC/C,IAAIoC,OAAO,EAAE;MACX1E,sBAAsB,CAACiF,YAAY,CAAC;IACtC;IACAZ,mBAAmB,CAACjD,OAAO,GAAG;MAC5B8D,OAAO,EAAE;QACP/D,KAAK,EAAE8D,YAAY;QACnB3C;MACF,CAAC;MACD6C,WAAW,EAAE7F,aAAa,CAAC;QACzBuD,IAAI,EAAEoC,YAAY;QAClBvC,UAAU,EAAE,CAAC,CAAC;QACdW,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAE;MACtB,CAAC,CAAC;MACFjB,eAAe,EAAEpC,sBAAsB,CAAC;IAC1C,CAAC;EACH;EAEA,MAAM;IAAEiF,OAAO;IAAEC,WAAW;IAAE9C;EAAgB,CAAC,GAAGgC,mBAAmB,CAACjD,OAAO;EAC7E,MAAMgE,wBAAwB,GAAG/C,eAAe,CAAC+C,wBAAwB;EAEzEjB,YAAY,CAACa,IAAI,CAACI,wBAAwB,CAAC;EAE3ChG,SAAS,CAAC,MAAM;IACd,IAAIiG,GAAG;IACP,IAAIC,SAAS,GAAGhD,OAAO;IACvB,IAAIsB,QAAQ,EAAE;MACZ0B,SAAS,GAAIA,CAAA,KAAM;QACjB,SAAS;;QACT,MAAM3C,SAAS,GAAGL,OAAO,CAAC,CAAC;QAC3BqC,aAAa,CAAC7D,OAAO,CAAEyE,OAAO,IAAK;UACjCA,OAAO,CAAC5C,SAAoC,CAAC;QAC/C,CAAC,CAAC;QACF,OAAOA,SAAS;MAClB,CAAgC;IAClC;IAEA,IAAIxC,MAAM,CAAC,CAAC,EAAE;MACZkF,GAAG,GAAGA,CAAA,KAAM;QACV,SAAS;;QACT3B,gBAAgB,CACd0B,wBAAwB,EACxB9C,OAAO,EACP6C,WAAW,EACXf,QAAQ,EACRS,mBAAmB,EACnBC,iBAAiB,EACjBH,aACF,CAAC;MACH,CAAC;IACH,CAAC,MAAM;MACLU,GAAG,GAAGA,CAAA,KAAM;QACV,SAAS;;QACTjD,YAAY,CACVgD,wBAAwB,EACxBE,SAAS,EACTH,WAAW,EACXf,QAAQ,EACRS,mBAAmB,EACnBpC,eACF,CAAC;MACH,CAAC;IACH;IACA,MAAM+C,QAAQ,GAAGjG,WAAW,CAAC8F,GAAG,EAAEf,MAAM,CAAC;IACzC,OAAO,MAAM;MACX9E,UAAU,CAACgG,QAAQ,CAAC;IACtB,CAAC;IACD;EACF,CAAC,EAAErB,YAAY,CAAC;EAEhB/E,SAAS,CAAC,MAAM;IACdyF,mBAAmB,CAAC1D,KAAK,GAAG,IAAI;IAChC,OAAO,MAAM;MACX0D,mBAAmB,CAAC1D,KAAK,GAAG,KAAK;IACnC,CAAC;EACH,CAAC,EAAE,CAAC0D,mBAAmB,CAAC,CAAC;EAEzBf,qBAAqB,CAACoB,OAAO,CAAC/D,KAAK,CAAC;EAEpC,MAAMsE,mBAAmB,GAAGpG,MAAM,CAEhC,IAAI,CAAC;EAEP,IAAI,CAACoG,mBAAmB,CAACrE,OAAO,EAAE;IAChCqE,mBAAmB,CAACrE,OAAO,GAAGjB,MAAM,CAAC,CAAC,GAClC;MAAEkC,eAAe;MAAE6C,OAAO;MAAEd,QAAQ;MAAEU;IAAkB,CAAC,GACzD;MAAEI,OAAO;MAAEd,QAAQ;MAAE/B;IAAgB,CAAC;EAC5C;EAEA,OAAOoD,mBAAmB,CAACrE,OAAO;AACpC", "ignoreList": []}