{"version": 3, "names": ["React", "Children", "Component", "createContext", "useEffect", "useRef", "setShouldAnimateExitingForTag", "findNodeHandle", "jsx", "_jsx", "SkipEnteringContext", "SkipEntering", "props", "skipV<PERSON>ue<PERSON>ef", "shouldSkip", "current", "Provider", "value", "children", "LayoutAnimationConfig", "getMaybeWrappedChildren", "count", "skipExiting", "map", "child", "setShouldAnimateExiting", "tag", "componentWillUnmount", "undefined", "render", "skipEntering"], "sourceRoot": "../../../src", "sources": ["component/LayoutAnimationConfig.tsx"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,KAAK,IACVC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,SAAS,EACTC,MAAM,QACD,OAAO;AAEd,SAASC,6BAA6B,QAAQ,YAAS;AACvD,SAASC,cAAc,QAAQ,qCAAqC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAErE,OAAO,MAAMC,mBAAmB,gBAC9BP,aAAa,CAAyC,IAAI,CAAC;;AAE7D;AACA;;AAOA,SAASQ,YAAYA,CAACC,KAAmD,EAAE;EACzE,MAAMC,YAAY,GAAGR,MAAM,CAACO,KAAK,CAACE,UAAU,CAAC;EAE7CV,SAAS,CAAC,MAAM;IACdS,YAAY,CAACE,OAAO,GAAG,KAAK;EAC9B,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;EAElB,oBACEJ,IAAA,CAACC,mBAAmB,CAACM,QAAQ;IAACC,KAAK,EAAEJ,YAAa;IAAAK,QAAA,EAC/CN,KAAK,CAACM;EAAQ,CACa,CAAC;AAEnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,SAASjB,SAAS,CAA6B;EAC/EkB,uBAAuBA,CAAA,EAAG;IACxB,OAAOnB,QAAQ,CAACoB,KAAK,CAAC,IAAI,CAACT,KAAK,CAACM,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAACN,KAAK,CAACU,WAAW,GACpErB,QAAQ,CAACsB,GAAG,CAAC,IAAI,CAACX,KAAK,CAACM,QAAQ,EAAGM,KAAK,iBACtCf,IAAA,CAACU,qBAAqB;MAACG,WAAW;MAAAJ,QAAA,EAAEM;IAAK,CAAwB,CAClE,CAAC,GACF,IAAI,CAACZ,KAAK,CAACM,QAAQ;EACzB;EAEAO,uBAAuBA,CAAA,EAAG;IACxB,IAAIxB,QAAQ,CAACoB,KAAK,CAAC,IAAI,CAACT,KAAK,CAACM,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,MAAMQ,GAAG,GAAGnB,cAAc,CAAC,IAAI,CAAC;MAChC,IAAImB,GAAG,EAAE;QACPpB,6BAA6B,CAACoB,GAAG,EAAE,CAAC,IAAI,CAACd,KAAK,CAACU,WAAW,CAAC;MAC7D;IACF;EACF;EAEAK,oBAAoBA,CAAA,EAAS;IAC3B,IAAI,IAAI,CAACf,KAAK,CAACU,WAAW,KAAKM,SAAS,EAAE;MACxC,IAAI,CAACH,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEAI,MAAMA,CAAA,EAAc;IAClB,MAAMX,QAAQ,GAAG,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAE/C,IAAI,IAAI,CAACR,KAAK,CAACkB,YAAY,KAAKF,SAAS,EAAE;MACzC,OAAOV,QAAQ;IACjB;IAEA,oBACET,IAAA,CAACE,YAAY;MAACG,UAAU,EAAE,IAAI,CAACF,KAAK,CAACkB,YAAa;MAAAZ,QAAA,EAC/CA;IAAQ,CACG,CAAC;EAEnB;AACF", "ignoreList": []}