{"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineProperty", "enumerable", "configurable", "writable", "t", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "findNodeHandle", "Platform", "WorkletEventHandler", "invariant", "adaptViewConfig", "<PERSON><PERSON><PERSON><PERSON>", "enableLayoutAnimations", "SharedTransition", "LayoutAnimationType", "getShadowNodeWrapperFromRef", "removeFromPropsRegistry", "getReduceMotionFromConfig", "maybeBuild", "SkipEnteringContext", "JSPropsUpdater", "has", "flattenArray", "setAndForwardRef", "isF<PERSON><PERSON>", "isJest", "isWeb", "shouldBeUseWeb", "InlinePropManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startWebLayoutAnimation", "tryActivateLayoutTransition", "configureWebLayoutAnimations", "getReducedMotionFromConfig", "saveSnapshot", "updateLayoutAnimations", "addHTMLMutationObserver", "getViewInfo", "IS_WEB", "onlyAnimatedStyles", "styles", "filter", "style", "viewDescriptors", "createAnimatedComponent", "Component", "options", "isReactComponent", "name", "AnimatedComponent", "constructor", "props", "getForwardedRef", "forwardedRef", "setLocalRef", "ref", "tag", "_viewTag", "layout", "entering", "exiting", "sharedTransitionTag", "_this$context", "_configureSharedTransition", "skipEntering", "context", "current", "_this$props", "ENTERING", "displayName", "_component", "jestAnimatedStyle", "componentDidMount", "_getViewInfo", "viewTag", "_attachNativeEvents", "_jsPropsUpdater", "addOnJSPropsChangeListener", "_attachAnimatedStyles", "_InlinePropManager", "attachInlineProps", "_configureLayoutTransition", "_isFirstRender", "componentWillUnmount", "_this$_sharedElementT", "_detachNativeEvents", "removeOnJSPropsChangeListener", "_detachStyles", "detachInlineProps", "_sharedElementTransition", "unregisterTransition", "EXITING", "reduceMotionInExiting", "getReduceMotion", "_this$props2", "_getEventViewRef", "_this$_component", "_getScrollableNode", "_ref", "getScrollableNode", "prop", "workletEventHandler", "registerForEvents", "unregisterFromEvents", "_styles", "viewsRef", "remove", "_this$props$animatedP", "animatedProps", "_updateNativeEvents", "prevProps", "prevProp", "newProp", "_updateFromNative", "setNativeProps", "_this$_component2", "_this$_component2$set", "_this$_component3", "_getAnimatableRef", "_ref2", "_viewInfo", "undefined", "viewName", "shadowNodeWrapper", "viewConfig", "component", "getAnimatableRef", "hostInstance", "findHostInstance_DEPRECATED", "Error", "viewInfo", "_this$props$animatedP2", "_this$props$animatedP3", "prevStyles", "prevAnimatedProps", "_animatedProps", "hasReanimated2Props", "hasOneSameStyle", "prevStyle", "isPresent", "some", "for<PERSON>ach", "add", "initial", "componentDidUpdate", "_prevState", "snapshot", "oldLayout", "LAYOUT", "isUnmounting", "_this$_sharedElementT2", "sharedElementTransition", "sharedTransitionStyle", "registerTransition", "getSnapshotBeforeUpdate", "_this$_component4", "getBoundingClientRect", "render", "filteredProps", "_Props<PERSON>ilter", "filterNonAnimatedProps", "visibility", "platformProps", "select", "web", "default", "collapsable", "createElement", "_setComponentRef", "forwardRef"], "sources": ["createAnimatedComponent.tsx"], "sourcesContent": ["'use strict';\nimport type {\n  Component,\n  ComponentClass,\n  ComponentType,\n  FunctionComponent,\n  MutableRefObject,\n} from 'react';\nimport React from 'react';\nimport { findNodeHandle, Platform } from 'react-native';\nimport { WorkletEventHandler } from '../reanimated2/WorkletEventHandler';\nimport '../reanimated2/layoutReanimation/animationsManager';\nimport invariant from 'invariant';\nimport { adaptViewConfig } from '../ConfigHelper';\nimport { RNRenderer } from '../reanimated2/platform-specific/RNRenderer';\nimport { enableLayoutAnimations } from '../reanimated2/core';\nimport {\n  SharedTransition,\n  LayoutAnimationType,\n} from '../reanimated2/layoutReanimation';\nimport type { StyleProps, ShadowNodeWrapper } from '../reanimated2/commonTypes';\nimport { getShadowNodeWrapperFromRef } from '../reanimated2/fabricUtils';\nimport { removeFromPropsRegistry } from '../reanimated2/PropsRegistry';\nimport { getReduceMotionFromConfig } from '../reanimated2/animation/util';\nimport { maybeBuild } from '../animationBuilder';\nimport { SkipEnteringContext } from '../reanimated2/component/LayoutAnimationConfig';\nimport type { AnimateProps } from '../reanimated2';\nimport JSPropsUpdater from './JSPropsUpdater';\nimport type {\n  AnimatedComponentProps,\n  AnimatedProps,\n  InitialComponentProps,\n  AnimatedComponentRef,\n  IAnimatedComponentInternal,\n  ViewInfo,\n} from './commonTypes';\nimport { has, flattenArray } from './utils';\nimport setAndForwardRef from './setAndForwardRef';\nimport {\n  isFabric,\n  isJest,\n  isWeb,\n  shouldBeUseWeb,\n} from '../reanimated2/PlatformChecker';\nimport { InlinePropManager } from './InlinePropManager';\nimport { PropsFilter } from './PropsFilter';\nimport {\n  startWebLayoutAnimation,\n  tryActivateLayoutTransition,\n  configureWebLayoutAnimations,\n  getReducedMotionFromConfig,\n  saveSnapshot,\n} from '../reanimated2/layoutReanimation/web';\nimport { updateLayoutAnimations } from '../reanimated2/UpdateLayoutAnimations';\nimport type { CustomConfig } from '../reanimated2/layoutReanimation/web/config';\nimport type { FlatList, FlatListProps } from 'react-native';\nimport { addHTMLMutationObserver } from '../reanimated2/layoutReanimation/web/domUtils';\nimport { getViewInfo } from './getViewInfo';\n\nconst IS_WEB = isWeb();\n\nif (IS_WEB) {\n  configureWebLayoutAnimations();\n}\n\nfunction onlyAnimatedStyles(styles: StyleProps[]): StyleProps[] {\n  return styles.filter((style) => style?.viewDescriptors);\n}\n\ntype Options<P> = {\n  setNativeProps: (ref: AnimatedComponentRef, props: P) => void;\n};\n\n/**\n * Lets you create an Animated version of any React Native component.\n *\n * @param component - The component you want to make animatable.\n * @returns A component that Reanimated is capable of animating.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/createAnimatedComponent\n */\n\n// Don't change the order of overloads, since such a change breaks current behavior\nexport function createAnimatedComponent<P extends object>(\n  component: FunctionComponent<P>,\n  options?: Options<P>\n): FunctionComponent<AnimateProps<P>>;\n\nexport function createAnimatedComponent<P extends object>(\n  component: ComponentClass<P>,\n  options?: Options<P>\n): ComponentClass<AnimateProps<P>>;\n\nexport function createAnimatedComponent<P extends object>(\n  // Actually ComponentType<P = {}> = ComponentClass<P> | FunctionComponent<P> but we need this overload too\n  // since some external components (like FastImage) are typed just as ComponentType\n  component: ComponentType<P>,\n  options?: Options<P>\n): FunctionComponent<AnimateProps<P>> | ComponentClass<AnimateProps<P>>;\n\n/**\n * @deprecated Please use `Animated.FlatList` component instead of calling `Animated.createAnimatedComponent(FlatList)` manually.\n */\n// @ts-ignore This is required to create this overload, since type of createAnimatedComponent is incorrect and doesn't include typeof FlatList\nexport function createAnimatedComponent(\n  component: typeof FlatList<unknown>,\n  options?: Options<any>\n): ComponentClass<AnimateProps<FlatListProps<unknown>>>;\n\nexport function createAnimatedComponent(\n  Component: ComponentType<InitialComponentProps>,\n  options?: Options<InitialComponentProps>\n): any {\n  invariant(\n    typeof Component !== 'function' ||\n      (Component.prototype && Component.prototype.isReactComponent),\n    `Looks like you're passing a function component \\`${Component.name}\\` to \\`createAnimatedComponent\\` function which supports only class components. Please wrap your function component with \\`React.forwardRef()\\` or use a class component instead.`\n  );\n\n  class AnimatedComponent\n    extends React.Component<AnimatedComponentProps<InitialComponentProps>>\n    implements IAnimatedComponentInternal\n  {\n    _styles: StyleProps[] | null = null;\n    _animatedProps?: Partial<AnimatedComponentProps<AnimatedProps>>;\n    _viewTag = -1;\n    _isFirstRender = true;\n    jestAnimatedStyle: { value: StyleProps } = { value: {} };\n    _component: AnimatedComponentRef | HTMLElement | null = null;\n    _sharedElementTransition: SharedTransition | null = null;\n    _jsPropsUpdater = new JSPropsUpdater();\n    _InlinePropManager = new InlinePropManager();\n    _PropsFilter = new PropsFilter();\n    _viewInfo?: ViewInfo;\n    static displayName: string;\n    static contextType = SkipEnteringContext;\n    context!: React.ContextType<typeof SkipEnteringContext>;\n\n    constructor(props: AnimatedComponentProps<InitialComponentProps>) {\n      super(props);\n      if (isJest()) {\n        this.jestAnimatedStyle = { value: {} };\n      }\n    }\n\n    componentDidMount() {\n      this._viewTag = this._getViewInfo().viewTag as number;\n      this._attachNativeEvents();\n      this._jsPropsUpdater.addOnJSPropsChangeListener(this);\n      this._attachAnimatedStyles();\n      this._InlinePropManager.attachInlineProps(this, this._getViewInfo());\n\n      const layout = this.props.layout;\n      if (layout) {\n        this._configureLayoutTransition();\n      }\n\n      if (IS_WEB) {\n        if (this.props.exiting) {\n          saveSnapshot(this._component as HTMLElement);\n        }\n\n        if (\n          !this.props.entering ||\n          getReducedMotionFromConfig(this.props.entering as CustomConfig)\n        ) {\n          this._isFirstRender = false;\n          return;\n        }\n\n        startWebLayoutAnimation(\n          this.props,\n          this._component as HTMLElement,\n          LayoutAnimationType.ENTERING\n        );\n      }\n\n      this._isFirstRender = false;\n    }\n\n    componentWillUnmount() {\n      this._detachNativeEvents();\n      this._jsPropsUpdater.removeOnJSPropsChangeListener(this);\n      this._detachStyles();\n      this._InlinePropManager.detachInlineProps();\n      if (this.props.sharedTransitionTag) {\n        this._configureSharedTransition(true);\n      }\n      this._sharedElementTransition?.unregisterTransition(this._viewTag, true);\n\n      const exiting = this.props.exiting;\n      if (\n        IS_WEB &&\n        this._component &&\n        this.props.exiting &&\n        !getReducedMotionFromConfig(this.props.exiting as CustomConfig)\n      ) {\n        addHTMLMutationObserver();\n\n        startWebLayoutAnimation(\n          this.props,\n          this._component as HTMLElement,\n          LayoutAnimationType.EXITING\n        );\n      } else if (exiting) {\n        const reduceMotionInExiting =\n          'getReduceMotion' in exiting &&\n          typeof exiting.getReduceMotion === 'function'\n            ? getReduceMotionFromConfig(exiting.getReduceMotion())\n            : getReduceMotionFromConfig();\n        if (!reduceMotionInExiting) {\n          updateLayoutAnimations(\n            this._viewTag,\n            LayoutAnimationType.EXITING,\n            maybeBuild(\n              exiting,\n              this.props?.style,\n              AnimatedComponent.displayName\n            )\n          );\n        }\n      }\n    }\n\n    _getEventViewRef() {\n      // Make sure to get the scrollable node for components that implement\n      // `ScrollResponder.Mixin`.\n      return (this._component as AnimatedComponentRef)?.getScrollableNode\n        ? (this._component as AnimatedComponentRef).getScrollableNode?.()\n        : this._component;\n    }\n\n    _attachNativeEvents() {\n      for (const key in this.props) {\n        const prop = this.props[key];\n        if (\n          has('workletEventHandler', prop) &&\n          prop.workletEventHandler instanceof WorkletEventHandler\n        ) {\n          prop.workletEventHandler.registerForEvents(this._viewTag, key);\n        }\n      }\n    }\n\n    _detachNativeEvents() {\n      for (const key in this.props) {\n        const prop = this.props[key];\n        if (\n          has('workletEventHandler', prop) &&\n          prop.workletEventHandler instanceof WorkletEventHandler\n        ) {\n          prop.workletEventHandler.unregisterFromEvents(this._viewTag);\n        }\n      }\n    }\n\n    _detachStyles() {\n      if (IS_WEB && this._styles !== null) {\n        for (const style of this._styles) {\n          style.viewsRef.remove(this);\n        }\n      } else if (this._viewTag !== -1 && this._styles !== null) {\n        for (const style of this._styles) {\n          style.viewDescriptors.remove(this._viewTag);\n        }\n        if (this.props.animatedProps?.viewDescriptors) {\n          this.props.animatedProps.viewDescriptors.remove(this._viewTag);\n        }\n        if (isFabric()) {\n          removeFromPropsRegistry(this._viewTag);\n        }\n      }\n    }\n\n    _updateNativeEvents(\n      prevProps: AnimatedComponentProps<InitialComponentProps>\n    ) {\n      for (const key in prevProps) {\n        const prevProp = prevProps[key];\n        if (\n          has('workletEventHandler', prevProp) &&\n          prevProp.workletEventHandler instanceof WorkletEventHandler\n        ) {\n          const newProp = this.props[key];\n          if (!newProp) {\n            // Prop got deleted\n            prevProp.workletEventHandler.unregisterFromEvents(this._viewTag);\n          } else if (\n            has('workletEventHandler', newProp) &&\n            newProp.workletEventHandler instanceof WorkletEventHandler &&\n            newProp.workletEventHandler !== prevProp.workletEventHandler\n          ) {\n            // Prop got changed\n            prevProp.workletEventHandler.unregisterFromEvents(this._viewTag);\n            newProp.workletEventHandler.registerForEvents(this._viewTag);\n          }\n        }\n      }\n\n      for (const key in this.props) {\n        const newProp = this.props[key];\n        if (\n          has('workletEventHandler', newProp) &&\n          newProp.workletEventHandler instanceof WorkletEventHandler &&\n          !prevProps[key]\n        ) {\n          // Prop got added\n          newProp.workletEventHandler.registerForEvents(this._viewTag);\n        }\n      }\n    }\n\n    _updateFromNative(props: StyleProps) {\n      if (options?.setNativeProps) {\n        options.setNativeProps(this._component as AnimatedComponentRef, props);\n      } else {\n        (this._component as AnimatedComponentRef)?.setNativeProps?.(props);\n      }\n    }\n\n    _getViewInfo(): ViewInfo {\n      if (this._viewInfo !== undefined) {\n        return this._viewInfo;\n      }\n\n      let viewTag: number | HTMLElement | null;\n      let viewName: string | null;\n      let shadowNodeWrapper: ShadowNodeWrapper | null = null;\n      let viewConfig;\n      // Component can specify ref which should be animated when animated version of the component is created.\n      // Otherwise, we animate the component itself.\n      const component = (this._component as AnimatedComponentRef)\n        ?.getAnimatableRef\n        ? (this._component as AnimatedComponentRef).getAnimatableRef?.()\n        : this;\n\n      if (IS_WEB) {\n        // At this point I assume that `_setComponentRef` was already called and `_component` is set.\n        // `this._component` on web represents HTMLElement of our component, that's why we use casting\n        viewTag = this._component as HTMLElement;\n        viewName = null;\n        shadowNodeWrapper = null;\n        viewConfig = null;\n      } else {\n        // hostInstance can be null for a component that doesn't render anything (render function returns null). Example: svg Stop: https://github.com/react-native-svg/react-native-svg/blob/develop/src/elements/Stop.tsx\n        const hostInstance = RNRenderer.findHostInstance_DEPRECATED(component);\n        if (!hostInstance) {\n          throw new Error(\n            '[Reanimated] Cannot find host instance for this component. Maybe it renders nothing?'\n          );\n        }\n\n        const viewInfo = getViewInfo(hostInstance);\n        viewTag = viewInfo.viewTag;\n        viewName = viewInfo.viewName;\n        viewConfig = viewInfo.viewConfig;\n        shadowNodeWrapper = isFabric()\n          ? getShadowNodeWrapperFromRef(this)\n          : null;\n      }\n      this._viewInfo = { viewTag, viewName, shadowNodeWrapper, viewConfig };\n      return this._viewInfo;\n    }\n\n    _attachAnimatedStyles() {\n      const styles = this.props.style\n        ? onlyAnimatedStyles(flattenArray<StyleProps>(this.props.style))\n        : [];\n      const prevStyles = this._styles;\n      this._styles = styles;\n\n      const prevAnimatedProps = this._animatedProps;\n      this._animatedProps = this.props.animatedProps;\n\n      const { viewTag, viewName, shadowNodeWrapper, viewConfig } =\n        this._getViewInfo();\n\n      // update UI props whitelist for this view\n      const hasReanimated2Props =\n        this.props.animatedProps?.viewDescriptors || styles.length;\n      if (hasReanimated2Props && viewConfig) {\n        adaptViewConfig(viewConfig);\n      }\n\n      this._viewTag = viewTag as number;\n\n      // remove old styles\n      if (prevStyles) {\n        // in most of the cases, views have only a single animated style and it remains unchanged\n        const hasOneSameStyle =\n          styles.length === 1 &&\n          prevStyles.length === 1 &&\n          styles[0] === prevStyles[0];\n\n        if (!hasOneSameStyle) {\n          // otherwise, remove each style that is not present in new styles\n          for (const prevStyle of prevStyles) {\n            const isPresent = styles.some((style) => style === prevStyle);\n            if (!isPresent) {\n              prevStyle.viewDescriptors.remove(viewTag);\n            }\n          }\n        }\n      }\n\n      styles.forEach((style) => {\n        style.viewDescriptors.add({\n          tag: viewTag,\n          name: viewName,\n          shadowNodeWrapper,\n        });\n        if (isJest()) {\n          /**\n           * We need to connect Jest's TestObject instance whose contains just props object\n           * with the updateProps() function where we update the properties of the component.\n           * We can't update props object directly because TestObject contains a copy of props - look at render function:\n           * const props = this._filterNonAnimatedProps(this.props);\n           */\n          this.jestAnimatedStyle.value = {\n            ...this.jestAnimatedStyle.value,\n            ...style.initial.value,\n          };\n          style.jestAnimatedStyle.current = this.jestAnimatedStyle;\n        }\n      });\n\n      // detach old animatedProps\n      if (prevAnimatedProps && prevAnimatedProps !== this.props.animatedProps) {\n        prevAnimatedProps.viewDescriptors!.remove(viewTag as number);\n      }\n\n      // attach animatedProps property\n      if (this.props.animatedProps?.viewDescriptors) {\n        this.props.animatedProps.viewDescriptors.add({\n          tag: viewTag as number,\n          name: viewName!,\n          shadowNodeWrapper: shadowNodeWrapper!,\n        });\n      }\n    }\n\n    componentDidUpdate(\n      prevProps: AnimatedComponentProps<InitialComponentProps>,\n      _prevState: Readonly<unknown>,\n      // This type comes straight from React\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      snapshot: DOMRect | null\n    ) {\n      const layout = this.props.layout;\n      const oldLayout = prevProps.layout;\n      if (layout !== oldLayout) {\n        this._configureLayoutTransition();\n      }\n      if (\n        this.props.sharedTransitionTag !== undefined ||\n        prevProps.sharedTransitionTag !== undefined\n      ) {\n        this._configureSharedTransition();\n      }\n      this._updateNativeEvents(prevProps);\n      this._attachAnimatedStyles();\n      this._InlinePropManager.attachInlineProps(this, this._getViewInfo());\n\n      if (IS_WEB && this.props.exiting) {\n        saveSnapshot(this._component as HTMLElement);\n      }\n\n      // Snapshot won't be undefined because it comes from getSnapshotBeforeUpdate method\n      if (\n        IS_WEB &&\n        snapshot !== null &&\n        this.props.layout &&\n        !getReducedMotionFromConfig(this.props.layout as CustomConfig)\n      ) {\n        tryActivateLayoutTransition(\n          this.props,\n          this._component as HTMLElement,\n          snapshot\n        );\n      }\n    }\n\n    _configureLayoutTransition() {\n      const layout = this.props.layout\n        ? maybeBuild(\n            this.props.layout,\n            undefined /* We don't have to warn user if style has common properties with animation for LAYOUT */,\n            AnimatedComponent.displayName\n          )\n        : undefined;\n      updateLayoutAnimations(this._viewTag, LayoutAnimationType.LAYOUT, layout);\n    }\n\n    _configureSharedTransition(isUnmounting = false) {\n      if (IS_WEB) {\n        return;\n      }\n      const { sharedTransitionTag } = this.props;\n      if (!sharedTransitionTag) {\n        this._sharedElementTransition?.unregisterTransition(\n          this._viewTag,\n          isUnmounting\n        );\n        this._sharedElementTransition = null;\n        return;\n      }\n      const sharedElementTransition =\n        this.props.sharedTransitionStyle ??\n        this._sharedElementTransition ??\n        new SharedTransition();\n      sharedElementTransition.registerTransition(\n        this._viewTag,\n        sharedTransitionTag,\n        isUnmounting\n      );\n      this._sharedElementTransition = sharedElementTransition;\n    }\n\n    _setComponentRef = setAndForwardRef<Component | HTMLElement>({\n      getForwardedRef: () =>\n        this.props.forwardedRef as MutableRefObject<\n          Component<Record<string, unknown>, Record<string, unknown>, unknown>\n        >,\n      setLocalRef: (ref) => {\n        // TODO update config\n\n        const tag = IS_WEB\n          ? (ref as HTMLElement)\n          : findNodeHandle(ref as Component);\n\n        this._viewTag = tag as number;\n\n        const { layout, entering, exiting, sharedTransitionTag } = this.props;\n        if (\n          (layout || entering || exiting || sharedTransitionTag) &&\n          tag != null\n        ) {\n          if (!shouldBeUseWeb()) {\n            enableLayoutAnimations(true, false);\n          }\n\n          if (sharedTransitionTag) {\n            this._configureSharedTransition();\n          }\n\n          const skipEntering = this.context?.current;\n          if (entering && !skipEntering) {\n            updateLayoutAnimations(\n              tag as number,\n              LayoutAnimationType.ENTERING,\n              maybeBuild(\n                entering,\n                this.props?.style,\n                AnimatedComponent.displayName\n              )\n            );\n          }\n        }\n\n        if (ref !== this._component) {\n          this._component = ref;\n        }\n      },\n    });\n\n    // This is a component lifecycle method from React, therefore we are not calling it directly.\n    // It is called before the component gets rerendered. This way we can access components' position before it changed\n    // and later on, in componentDidUpdate, calculate translation for layout transition.\n    getSnapshotBeforeUpdate() {\n      if (\n        IS_WEB &&\n        (this._component as HTMLElement)?.getBoundingClientRect !== undefined\n      ) {\n        return (this._component as HTMLElement).getBoundingClientRect();\n      }\n\n      return null;\n    }\n\n    render() {\n      const filteredProps = this._PropsFilter.filterNonAnimatedProps(this);\n\n      if (isJest()) {\n        filteredProps.jestAnimatedStyle = this.jestAnimatedStyle;\n      }\n\n      // Layout animations on web are set inside `componentDidMount` method, which is called after first render.\n      // Because of that we can encounter a situation in which component is visible for a short amount of time, and later on animation triggers.\n      // I've tested that on various browsers and devices and it did not happen to me. To be sure that it won't happen to someone else,\n      // I've decided to hide component at first render. Its visibility is reset in `componentDidMount`.\n      if (\n        this._isFirstRender &&\n        IS_WEB &&\n        filteredProps.entering &&\n        !getReducedMotionFromConfig(filteredProps.entering as CustomConfig)\n      ) {\n        filteredProps.style = {\n          ...(filteredProps.style ?? {}),\n          visibility: 'hidden', // Hide component until `componentDidMount` triggers\n        };\n      }\n\n      const platformProps = Platform.select({\n        web: {},\n        default: { collapsable: false },\n      });\n\n      return (\n        <Component\n          {...filteredProps}\n          // Casting is used here, because ref can be null - in that case it cannot be assigned to HTMLElement.\n          // After spending some time trying to figure out what to do with this problem, we decided to leave it this way\n          ref={this._setComponentRef as (ref: Component) => void}\n          {...platformProps}\n        />\n      );\n    }\n  }\n\n  AnimatedComponent.displayName = `AnimatedComponent(${\n    Component.displayName || Component.name || 'Component'\n  })`;\n\n  return React.forwardRef<Component>((props, ref) => {\n    return (\n      <AnimatedComponent\n        {...props}\n        {...(ref === null ? null : { forwardedRef: ref })}\n      />\n    );\n  });\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAAA,SAAAQ,gBAAAC,GAAA,EAAAN,GAAA,EAAAO,KAAA,IAAAP,GAAA,GAAAQ,cAAA,CAAAR,GAAA,OAAAA,GAAA,IAAAM,GAAA,IAAAd,MAAA,CAAAiB,cAAA,CAAAH,GAAA,EAAAN,GAAA,IAAAO,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAN,GAAA,CAAAN,GAAA,IAAAO,KAAA,WAAAD,GAAA;AAAA,SAAAE,eAAAK,CAAA,QAAAjB,CAAA,GAAAkB,YAAA,CAAAD,CAAA,uCAAAjB,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAkB,aAAAD,CAAA,EAAAE,CAAA,2BAAAF,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAG,CAAA,GAAAH,CAAA,CAAAI,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAApB,CAAA,GAAAoB,CAAA,CAAAb,IAAA,CAAAU,CAAA,EAAAE,CAAA,uCAAAnB,CAAA,SAAAA,CAAA,YAAAuB,SAAA,yEAAAJ,CAAA,GAAAK,MAAA,GAAAC,MAAA,EAAAR,CAAA;AAQb,OAAOS,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,EAAEC,QAAQ,QAAQ,cAAc;AACvD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,OAAO,oDAAoD;AAC3D,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,UAAU,QAAQ,6CAA6C;AACxE,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SACEC,gBAAgB,EAChBC,mBAAmB,QACd,kCAAkC;AAEzC,SAASC,2BAA2B,QAAQ,4BAA4B;AACxE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,mBAAmB,QAAQ,gDAAgD;AAEpF,OAAOC,cAAc,MAAM,kBAAkB;AAS7C,SAASC,GAAG,EAAEC,YAAY,QAAQ,SAAS;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,cAAc,QACT,gCAAgC;AACvC,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,YAAY,QACP,sCAAsC;AAC7C,SAASC,sBAAsB,QAAQ,uCAAuC;AAG9E,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,WAAW,QAAQ,eAAe;AAE3C,MAAMC,MAAM,GAAGZ,KAAK,CAAC,CAAC;AAEtB,IAAIY,MAAM,EAAE;EACVN,4BAA4B,CAAC,CAAC;AAChC;AAEA,SAASO,kBAAkBA,CAACC,MAAoB,EAAgB;EAC9D,OAAOA,MAAM,CAACC,MAAM,CAAEC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,eAAe,CAAC;AACzD;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAkBA;AACA;AACA;AACA;;AAMA,OAAO,SAASC,uBAAuBA,CACrCC,SAA+C,EAC/CC,OAAwC,EACnC;EACLrC,SAAS,CACP,OAAOoC,SAAS,KAAK,UAAU,IAC5BA,SAAS,CAAC7D,SAAS,IAAI6D,SAAS,CAAC7D,SAAS,CAAC+D,gBAAiB,EAC9D,oDAAmDF,SAAS,CAACG,IAAK,oLACrE,CAAC;EAED,MAAMC,iBAAiB,SACb5C,KAAK,CAACwC,SAAS,CAEzB;IAgBEK,WAAWA,CAACC,KAAoD,EAAE;MAChE,KAAK,CAACA,KAAK,CAAC;MAAC/D,eAAA,kBAhBgB,IAAI;MAAAA,eAAA;MAAAA,eAAA,mBAExB,CAAC,CAAC;MAAAA,eAAA,yBACI,IAAI;MAAAA,eAAA,4BACsB;QAAEE,KAAK,EAAE,CAAC;MAAE,CAAC;MAAAF,eAAA,qBACA,IAAI;MAAAA,eAAA,mCACR,IAAI;MAAAA,eAAA,0BACtC,IAAIgC,cAAc,CAAC,CAAC;MAAAhC,eAAA,6BACjB,IAAIwC,iBAAiB,CAAC,CAAC;MAAAxC,eAAA,uBAC7B,IAAIyC,WAAW,CAAC,CAAC;MAAAzC,eAAA;MAAAA,eAAA;MAAAA,eAAA,2BAkYbmC,gBAAgB,CAA0B;QAC3D6B,eAAe,EAAEA,CAAA,KACf,IAAI,CAACD,KAAK,CAACE,YAEV;QACHC,WAAW,EAAGC,GAAG,IAAK;UACpB;;UAEA,MAAMC,GAAG,GAAGlB,MAAM,GACbiB,GAAG,GACJjD,cAAc,CAACiD,GAAgB,CAAC;UAEpC,IAAI,CAACE,QAAQ,GAAGD,GAAa;UAE7B,MAAM;YAAEE,MAAM;YAAEC,QAAQ;YAAEC,OAAO;YAAEC;UAAoB,CAAC,GAAG,IAAI,CAACV,KAAK;UACrE,IACE,CAACO,MAAM,IAAIC,QAAQ,IAAIC,OAAO,IAAIC,mBAAmB,KACrDL,GAAG,IAAI,IAAI,EACX;YAAA,IAAAM,aAAA;YACA,IAAI,CAACnC,cAAc,CAAC,CAAC,EAAE;cACrBf,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC;YACrC;YAEA,IAAIiD,mBAAmB,EAAE;cACvB,IAAI,CAACE,0BAA0B,CAAC,CAAC;YACnC;YAEA,MAAMC,YAAY,IAAAF,aAAA,GAAG,IAAI,CAACG,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,OAAO;YAC1C,IAAIP,QAAQ,IAAI,CAACK,YAAY,EAAE;cAAA,IAAAG,WAAA;cAC7BhC,sBAAsB,CACpBqB,GAAG,EACH1C,mBAAmB,CAACsD,QAAQ,EAC5BlD,UAAU,CACRyC,QAAQ,GAAAQ,WAAA,GACR,IAAI,CAAChB,KAAK,cAAAgB,WAAA,uBAAVA,WAAA,CAAYzB,KAAK,EACjBO,iBAAiB,CAACoB,WACpB,CACF,CAAC;YACH;UACF;UAEA,IAAId,GAAG,KAAK,IAAI,CAACe,UAAU,EAAE;YAC3B,IAAI,CAACA,UAAU,GAAGf,GAAG;UACvB;QACF;MACF,CAAC,CAAC;MAvaA,IAAI9B,MAAM,CAAC,CAAC,EAAE;QACZ,IAAI,CAAC8C,iBAAiB,GAAG;UAAEjF,KAAK,EAAE,CAAC;QAAE,CAAC;MACxC;IACF;IAEAkF,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACf,QAAQ,GAAG,IAAI,CAACgB,YAAY,CAAC,CAAC,CAACC,OAAiB;MACrD,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,eAAe,CAACC,0BAA0B,CAAC,IAAI,CAAC;MACrD,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,kBAAkB,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACP,YAAY,CAAC,CAAC,CAAC;MAEpE,MAAMf,MAAM,GAAG,IAAI,CAACP,KAAK,CAACO,MAAM;MAChC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACuB,0BAA0B,CAAC,CAAC;MACnC;MAEA,IAAI3C,MAAM,EAAE;QACV,IAAI,IAAI,CAACa,KAAK,CAACS,OAAO,EAAE;UACtB1B,YAAY,CAAC,IAAI,CAACoC,UAAyB,CAAC;QAC9C;QAEA,IACE,CAAC,IAAI,CAACnB,KAAK,CAACQ,QAAQ,IACpB1B,0BAA0B,CAAC,IAAI,CAACkB,KAAK,CAACQ,QAAwB,CAAC,EAC/D;UACA,IAAI,CAACuB,cAAc,GAAG,KAAK;UAC3B;QACF;QAEApD,uBAAuB,CACrB,IAAI,CAACqB,KAAK,EACV,IAAI,CAACmB,UAAU,EACfxD,mBAAmB,CAACsD,QACtB,CAAC;MACH;MAEA,IAAI,CAACc,cAAc,GAAG,KAAK;IAC7B;IAEAC,oBAAoBA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MACrB,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACT,eAAe,CAACU,6BAA6B,CAAC,IAAI,CAAC;MACxD,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACR,kBAAkB,CAACS,iBAAiB,CAAC,CAAC;MAC3C,IAAI,IAAI,CAACrC,KAAK,CAACU,mBAAmB,EAAE;QAClC,IAAI,CAACE,0BAA0B,CAAC,IAAI,CAAC;MACvC;MACA,CAAAqB,qBAAA,OAAI,CAACK,wBAAwB,cAAAL,qBAAA,eAA7BA,qBAAA,CAA+BM,oBAAoB,CAAC,IAAI,CAACjC,QAAQ,EAAE,IAAI,CAAC;MAExE,MAAMG,OAAO,GAAG,IAAI,CAACT,KAAK,CAACS,OAAO;MAClC,IACEtB,MAAM,IACN,IAAI,CAACgC,UAAU,IACf,IAAI,CAACnB,KAAK,CAACS,OAAO,IAClB,CAAC3B,0BAA0B,CAAC,IAAI,CAACkB,KAAK,CAACS,OAAuB,CAAC,EAC/D;QACAxB,uBAAuB,CAAC,CAAC;QAEzBN,uBAAuB,CACrB,IAAI,CAACqB,KAAK,EACV,IAAI,CAACmB,UAAU,EACfxD,mBAAmB,CAAC6E,OACtB,CAAC;MACH,CAAC,MAAM,IAAI/B,OAAO,EAAE;QAClB,MAAMgC,qBAAqB,GACzB,iBAAiB,IAAIhC,OAAO,IAC5B,OAAOA,OAAO,CAACiC,eAAe,KAAK,UAAU,GACzC5E,yBAAyB,CAAC2C,OAAO,CAACiC,eAAe,CAAC,CAAC,CAAC,GACpD5E,yBAAyB,CAAC,CAAC;QACjC,IAAI,CAAC2E,qBAAqB,EAAE;UAAA,IAAAE,YAAA;UAC1B3D,sBAAsB,CACpB,IAAI,CAACsB,QAAQ,EACb3C,mBAAmB,CAAC6E,OAAO,EAC3BzE,UAAU,CACR0C,OAAO,GAAAkC,YAAA,GACP,IAAI,CAAC3C,KAAK,cAAA2C,YAAA,uBAAVA,YAAA,CAAYpD,KAAK,EACjBO,iBAAiB,CAACoB,WACpB,CACF,CAAC;QACH;MACF;IACF;IAEA0B,gBAAgBA,CAAA,EAAG;MAAA,IAAAC,gBAAA,EAAAC,kBAAA,EAAAC,IAAA;MACjB;MACA;MACA,OAAO,CAAAF,gBAAA,GAAC,IAAI,CAAC1B,UAAU,cAAA0B,gBAAA,eAAhBA,gBAAA,CAA2CG,iBAAiB,IAAAF,kBAAA,GAC/D,CAAAC,IAAA,GAAC,IAAI,CAAC5B,UAAU,EAA0B6B,iBAAiB,cAAAF,kBAAA,uBAA3DA,kBAAA,CAAA/G,IAAA,CAAAgH,IAA8D,CAAC,GAC/D,IAAI,CAAC5B,UAAU;IACrB;IAEAK,mBAAmBA,CAAA,EAAG;MACpB,KAAK,MAAM5F,GAAG,IAAI,IAAI,CAACoE,KAAK,EAAE;QAC5B,MAAMiD,IAAI,GAAG,IAAI,CAACjD,KAAK,CAACpE,GAAG,CAAC;QAC5B,IACEsC,GAAG,CAAC,qBAAqB,EAAE+E,IAAI,CAAC,IAChCA,IAAI,CAACC,mBAAmB,YAAY7F,mBAAmB,EACvD;UACA4F,IAAI,CAACC,mBAAmB,CAACC,iBAAiB,CAAC,IAAI,CAAC7C,QAAQ,EAAE1E,GAAG,CAAC;QAChE;MACF;IACF;IAEAsG,mBAAmBA,CAAA,EAAG;MACpB,KAAK,MAAMtG,GAAG,IAAI,IAAI,CAACoE,KAAK,EAAE;QAC5B,MAAMiD,IAAI,GAAG,IAAI,CAACjD,KAAK,CAACpE,GAAG,CAAC;QAC5B,IACEsC,GAAG,CAAC,qBAAqB,EAAE+E,IAAI,CAAC,IAChCA,IAAI,CAACC,mBAAmB,YAAY7F,mBAAmB,EACvD;UACA4F,IAAI,CAACC,mBAAmB,CAACE,oBAAoB,CAAC,IAAI,CAAC9C,QAAQ,CAAC;QAC9D;MACF;IACF;IAEA8B,aAAaA,CAAA,EAAG;MACd,IAAIjD,MAAM,IAAI,IAAI,CAACkE,OAAO,KAAK,IAAI,EAAE;QACnC,KAAK,MAAM9D,KAAK,IAAI,IAAI,CAAC8D,OAAO,EAAE;UAChC9D,KAAK,CAAC+D,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;QAC7B;MACF,CAAC,MAAM,IAAI,IAAI,CAACjD,QAAQ,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC+C,OAAO,KAAK,IAAI,EAAE;QAAA,IAAAG,qBAAA;QACxD,KAAK,MAAMjE,KAAK,IAAI,IAAI,CAAC8D,OAAO,EAAE;UAChC9D,KAAK,CAACC,eAAe,CAAC+D,MAAM,CAAC,IAAI,CAACjD,QAAQ,CAAC;QAC7C;QACA,KAAAkD,qBAAA,GAAI,IAAI,CAACxD,KAAK,CAACyD,aAAa,cAAAD,qBAAA,eAAxBA,qBAAA,CAA0BhE,eAAe,EAAE;UAC7C,IAAI,CAACQ,KAAK,CAACyD,aAAa,CAACjE,eAAe,CAAC+D,MAAM,CAAC,IAAI,CAACjD,QAAQ,CAAC;QAChE;QACA,IAAIjC,QAAQ,CAAC,CAAC,EAAE;UACdR,uBAAuB,CAAC,IAAI,CAACyC,QAAQ,CAAC;QACxC;MACF;IACF;IAEAoD,mBAAmBA,CACjBC,SAAwD,EACxD;MACA,KAAK,MAAM/H,GAAG,IAAI+H,SAAS,EAAE;QAC3B,MAAMC,QAAQ,GAAGD,SAAS,CAAC/H,GAAG,CAAC;QAC/B,IACEsC,GAAG,CAAC,qBAAqB,EAAE0F,QAAQ,CAAC,IACpCA,QAAQ,CAACV,mBAAmB,YAAY7F,mBAAmB,EAC3D;UACA,MAAMwG,OAAO,GAAG,IAAI,CAAC7D,KAAK,CAACpE,GAAG,CAAC;UAC/B,IAAI,CAACiI,OAAO,EAAE;YACZ;YACAD,QAAQ,CAACV,mBAAmB,CAACE,oBAAoB,CAAC,IAAI,CAAC9C,QAAQ,CAAC;UAClE,CAAC,MAAM,IACLpC,GAAG,CAAC,qBAAqB,EAAE2F,OAAO,CAAC,IACnCA,OAAO,CAACX,mBAAmB,YAAY7F,mBAAmB,IAC1DwG,OAAO,CAACX,mBAAmB,KAAKU,QAAQ,CAACV,mBAAmB,EAC5D;YACA;YACAU,QAAQ,CAACV,mBAAmB,CAACE,oBAAoB,CAAC,IAAI,CAAC9C,QAAQ,CAAC;YAChEuD,OAAO,CAACX,mBAAmB,CAACC,iBAAiB,CAAC,IAAI,CAAC7C,QAAQ,CAAC;UAC9D;QACF;MACF;MAEA,KAAK,MAAM1E,GAAG,IAAI,IAAI,CAACoE,KAAK,EAAE;QAC5B,MAAM6D,OAAO,GAAG,IAAI,CAAC7D,KAAK,CAACpE,GAAG,CAAC;QAC/B,IACEsC,GAAG,CAAC,qBAAqB,EAAE2F,OAAO,CAAC,IACnCA,OAAO,CAACX,mBAAmB,YAAY7F,mBAAmB,IAC1D,CAACsG,SAAS,CAAC/H,GAAG,CAAC,EACf;UACA;UACAiI,OAAO,CAACX,mBAAmB,CAACC,iBAAiB,CAAC,IAAI,CAAC7C,QAAQ,CAAC;QAC9D;MACF;IACF;IAEAwD,iBAAiBA,CAAC9D,KAAiB,EAAE;MACnC,IAAIL,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEoE,cAAc,EAAE;QAC3BpE,OAAO,CAACoE,cAAc,CAAC,IAAI,CAAC5C,UAAU,EAA0BnB,KAAK,CAAC;MACxE,CAAC,MAAM;QAAA,IAAAgE,iBAAA,EAAAC,qBAAA;QACL,CAAAD,iBAAA,GAAC,IAAI,CAAC7C,UAAU,cAAA6C,iBAAA,gBAAAC,qBAAA,GAAhBD,iBAAA,CAA2CD,cAAc,cAAAE,qBAAA,eAAzDA,qBAAA,CAAAlI,IAAA,CAAAiI,iBAAA,EAA4DhE,KAAK,CAAC;MACpE;IACF;IAEAsB,YAAYA,CAAA,EAAa;MAAA,IAAA4C,iBAAA,EAAAC,iBAAA,EAAAC,KAAA;MACvB,IAAI,IAAI,CAACC,SAAS,KAAKC,SAAS,EAAE;QAChC,OAAO,IAAI,CAACD,SAAS;MACvB;MAEA,IAAI9C,OAAoC;MACxC,IAAIgD,QAAuB;MAC3B,IAAIC,iBAA2C,GAAG,IAAI;MACtD,IAAIC,UAAU;MACd;MACA;MACA,MAAMC,SAAS,GAAG,CAAAR,iBAAA,GAAC,IAAI,CAAC/C,UAAU,cAAA+C,iBAAA,eAAhBA,iBAAA,CACdS,gBAAgB,IAAAR,iBAAA,GAChB,CAAAC,KAAA,GAAC,IAAI,CAACjD,UAAU,EAA0BwD,gBAAgB,cAAAR,iBAAA,uBAA1DA,iBAAA,CAAApI,IAAA,CAAAqI,KAA6D,CAAC,GAC9D,IAAI;MAER,IAAIjF,MAAM,EAAE;QACV;QACA;QACAoC,OAAO,GAAG,IAAI,CAACJ,UAAyB;QACxCoD,QAAQ,GAAG,IAAI;QACfC,iBAAiB,GAAG,IAAI;QACxBC,UAAU,GAAG,IAAI;MACnB,CAAC,MAAM;QACL;QACA,MAAMG,YAAY,GAAGpH,UAAU,CAACqH,2BAA2B,CAACH,SAAS,CAAC;QACtE,IAAI,CAACE,YAAY,EAAE;UACjB,MAAM,IAAIE,KAAK,CACb,sFACF,CAAC;QACH;QAEA,MAAMC,QAAQ,GAAG7F,WAAW,CAAC0F,YAAY,CAAC;QAC1CrD,OAAO,GAAGwD,QAAQ,CAACxD,OAAO;QAC1BgD,QAAQ,GAAGQ,QAAQ,CAACR,QAAQ;QAC5BE,UAAU,GAAGM,QAAQ,CAACN,UAAU;QAChCD,iBAAiB,GAAGnG,QAAQ,CAAC,CAAC,GAC1BT,2BAA2B,CAAC,IAAI,CAAC,GACjC,IAAI;MACV;MACA,IAAI,CAACyG,SAAS,GAAG;QAAE9C,OAAO;QAAEgD,QAAQ;QAAEC,iBAAiB;QAAEC;MAAW,CAAC;MACrE,OAAO,IAAI,CAACJ,SAAS;IACvB;IAEA1C,qBAAqBA,CAAA,EAAG;MAAA,IAAAqD,sBAAA,EAAAC,sBAAA;MACtB,MAAM5F,MAAM,GAAG,IAAI,CAACW,KAAK,CAACT,KAAK,GAC3BH,kBAAkB,CAACjB,YAAY,CAAa,IAAI,CAAC6B,KAAK,CAACT,KAAK,CAAC,CAAC,GAC9D,EAAE;MACN,MAAM2F,UAAU,GAAG,IAAI,CAAC7B,OAAO;MAC/B,IAAI,CAACA,OAAO,GAAGhE,MAAM;MAErB,MAAM8F,iBAAiB,GAAG,IAAI,CAACC,cAAc;MAC7C,IAAI,CAACA,cAAc,GAAG,IAAI,CAACpF,KAAK,CAACyD,aAAa;MAE9C,MAAM;QAAElC,OAAO;QAAEgD,QAAQ;QAAEC,iBAAiB;QAAEC;MAAW,CAAC,GACxD,IAAI,CAACnD,YAAY,CAAC,CAAC;;MAErB;MACA,MAAM+D,mBAAmB,GACvB,EAAAL,sBAAA,OAAI,CAAChF,KAAK,CAACyD,aAAa,cAAAuB,sBAAA,uBAAxBA,sBAAA,CAA0BxF,eAAe,KAAIH,MAAM,CAAC3D,MAAM;MAC5D,IAAI2J,mBAAmB,IAAIZ,UAAU,EAAE;QACrClH,eAAe,CAACkH,UAAU,CAAC;MAC7B;MAEA,IAAI,CAACnE,QAAQ,GAAGiB,OAAiB;;MAEjC;MACA,IAAI2D,UAAU,EAAE;QACd;QACA,MAAMI,eAAe,GACnBjG,MAAM,CAAC3D,MAAM,KAAK,CAAC,IACnBwJ,UAAU,CAACxJ,MAAM,KAAK,CAAC,IACvB2D,MAAM,CAAC,CAAC,CAAC,KAAK6F,UAAU,CAAC,CAAC,CAAC;QAE7B,IAAI,CAACI,eAAe,EAAE;UACpB;UACA,KAAK,MAAMC,SAAS,IAAIL,UAAU,EAAE;YAClC,MAAMM,SAAS,GAAGnG,MAAM,CAACoG,IAAI,CAAElG,KAAK,IAAKA,KAAK,KAAKgG,SAAS,CAAC;YAC7D,IAAI,CAACC,SAAS,EAAE;cACdD,SAAS,CAAC/F,eAAe,CAAC+D,MAAM,CAAChC,OAAO,CAAC;YAC3C;UACF;QACF;MACF;MAEAlC,MAAM,CAACqG,OAAO,CAAEnG,KAAK,IAAK;QACxBA,KAAK,CAACC,eAAe,CAACmG,GAAG,CAAC;UACxBtF,GAAG,EAAEkB,OAAO;UACZ1B,IAAI,EAAE0E,QAAQ;UACdC;QACF,CAAC,CAAC;QACF,IAAIlG,MAAM,CAAC,CAAC,EAAE;UACZ;AACV;AACA;AACA;AACA;AACA;UACU,IAAI,CAAC8C,iBAAiB,CAACjF,KAAK,GAAG;YAC7B,GAAG,IAAI,CAACiF,iBAAiB,CAACjF,KAAK;YAC/B,GAAGoD,KAAK,CAACqG,OAAO,CAACzJ;UACnB,CAAC;UACDoD,KAAK,CAAC6B,iBAAiB,CAACL,OAAO,GAAG,IAAI,CAACK,iBAAiB;QAC1D;MACF,CAAC,CAAC;;MAEF;MACA,IAAI+D,iBAAiB,IAAIA,iBAAiB,KAAK,IAAI,CAACnF,KAAK,CAACyD,aAAa,EAAE;QACvE0B,iBAAiB,CAAC3F,eAAe,CAAE+D,MAAM,CAAChC,OAAiB,CAAC;MAC9D;;MAEA;MACA,KAAA0D,sBAAA,GAAI,IAAI,CAACjF,KAAK,CAACyD,aAAa,cAAAwB,sBAAA,eAAxBA,sBAAA,CAA0BzF,eAAe,EAAE;QAC7C,IAAI,CAACQ,KAAK,CAACyD,aAAa,CAACjE,eAAe,CAACmG,GAAG,CAAC;UAC3CtF,GAAG,EAAEkB,OAAiB;UACtB1B,IAAI,EAAE0E,QAAS;UACfC,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;IACF;IAEAqB,kBAAkBA,CAChBlC,SAAwD,EACxDmC,UAA6B;IAC7B;IACA;IACAC,QAAwB,EACxB;MACA,MAAMxF,MAAM,GAAG,IAAI,CAACP,KAAK,CAACO,MAAM;MAChC,MAAMyF,SAAS,GAAGrC,SAAS,CAACpD,MAAM;MAClC,IAAIA,MAAM,KAAKyF,SAAS,EAAE;QACxB,IAAI,CAAClE,0BAA0B,CAAC,CAAC;MACnC;MACA,IACE,IAAI,CAAC9B,KAAK,CAACU,mBAAmB,KAAK4D,SAAS,IAC5CX,SAAS,CAACjD,mBAAmB,KAAK4D,SAAS,EAC3C;QACA,IAAI,CAAC1D,0BAA0B,CAAC,CAAC;MACnC;MACA,IAAI,CAAC8C,mBAAmB,CAACC,SAAS,CAAC;MACnC,IAAI,CAAChC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,kBAAkB,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACP,YAAY,CAAC,CAAC,CAAC;MAEpE,IAAInC,MAAM,IAAI,IAAI,CAACa,KAAK,CAACS,OAAO,EAAE;QAChC1B,YAAY,CAAC,IAAI,CAACoC,UAAyB,CAAC;MAC9C;;MAEA;MACA,IACEhC,MAAM,IACN4G,QAAQ,KAAK,IAAI,IACjB,IAAI,CAAC/F,KAAK,CAACO,MAAM,IACjB,CAACzB,0BAA0B,CAAC,IAAI,CAACkB,KAAK,CAACO,MAAsB,CAAC,EAC9D;QACA3B,2BAA2B,CACzB,IAAI,CAACoB,KAAK,EACV,IAAI,CAACmB,UAAU,EACf4E,QACF,CAAC;MACH;IACF;IAEAjE,0BAA0BA,CAAA,EAAG;MAC3B,MAAMvB,MAAM,GAAG,IAAI,CAACP,KAAK,CAACO,MAAM,GAC5BxC,UAAU,CACR,IAAI,CAACiC,KAAK,CAACO,MAAM,EACjB+D,SAAS,CAAC,2FACVxE,iBAAiB,CAACoB,WACpB,CAAC,GACDoD,SAAS;MACbtF,sBAAsB,CAAC,IAAI,CAACsB,QAAQ,EAAE3C,mBAAmB,CAACsI,MAAM,EAAE1F,MAAM,CAAC;IAC3E;IAEAK,0BAA0BA,CAACsF,YAAY,GAAG,KAAK,EAAE;MAC/C,IAAI/G,MAAM,EAAE;QACV;MACF;MACA,MAAM;QAAEuB;MAAoB,CAAC,GAAG,IAAI,CAACV,KAAK;MAC1C,IAAI,CAACU,mBAAmB,EAAE;QAAA,IAAAyF,sBAAA;QACxB,CAAAA,sBAAA,OAAI,CAAC7D,wBAAwB,cAAA6D,sBAAA,eAA7BA,sBAAA,CAA+B5D,oBAAoB,CACjD,IAAI,CAACjC,QAAQ,EACb4F,YACF,CAAC;QACD,IAAI,CAAC5D,wBAAwB,GAAG,IAAI;QACpC;MACF;MACA,MAAM8D,uBAAuB,GAC3B,IAAI,CAACpG,KAAK,CAACqG,qBAAqB,IAChC,IAAI,CAAC/D,wBAAwB,IAC7B,IAAI5E,gBAAgB,CAAC,CAAC;MACxB0I,uBAAuB,CAACE,kBAAkB,CACxC,IAAI,CAAChG,QAAQ,EACbI,mBAAmB,EACnBwF,YACF,CAAC;MACD,IAAI,CAAC5D,wBAAwB,GAAG8D,uBAAuB;IACzD;IAiDA;IACA;IACA;IACAG,uBAAuBA,CAAA,EAAG;MAAA,IAAAC,iBAAA;MACxB,IACErH,MAAM,IACN,EAAAqH,iBAAA,GAAC,IAAI,CAACrF,UAAU,cAAAqF,iBAAA,uBAAhBA,iBAAA,CAAkCC,qBAAqB,MAAKnC,SAAS,EACrE;QACA,OAAQ,IAAI,CAACnD,UAAU,CAAiBsF,qBAAqB,CAAC,CAAC;MACjE;MAEA,OAAO,IAAI;IACb;IAEAC,MAAMA,CAAA,EAAG;MACP,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAACC,sBAAsB,CAAC,IAAI,CAAC;MAEpE,IAAIvI,MAAM,CAAC,CAAC,EAAE;QACZqI,aAAa,CAACvF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC1D;;MAEA;MACA;MACA;MACA;MACA,IACE,IAAI,CAACW,cAAc,IACnB5C,MAAM,IACNwH,aAAa,CAACnG,QAAQ,IACtB,CAAC1B,0BAA0B,CAAC6H,aAAa,CAACnG,QAAwB,CAAC,EACnE;QACAmG,aAAa,CAACpH,KAAK,GAAG;UACpB,IAAIoH,aAAa,CAACpH,KAAK,IAAI,CAAC,CAAC,CAAC;UAC9BuH,UAAU,EAAE,QAAQ,CAAE;QACxB,CAAC;MACH;MAEA,MAAMC,aAAa,GAAG3J,QAAQ,CAAC4J,MAAM,CAAC;QACpCC,GAAG,EAAE,CAAC,CAAC;QACPC,OAAO,EAAE;UAAEC,WAAW,EAAE;QAAM;MAChC,CAAC,CAAC;MAEF,oBACEjK,KAAA,CAAAkK,aAAA,CAAC1H,SAAS,EAAAvE,QAAA,KACJwL,aAAa;QACjB;QACA;QACAvG,GAAG,EAAE,IAAI,CAACiH;MAA6C,GACnDN,aAAa,CAClB,CAAC;IAEN;EACF;EAAC9K,eAAA,CAlfK6D,iBAAiB;EAAA7D,eAAA,CAAjB6D,iBAAiB,iBAgBA9B,mBAAmB;EAoe1C8B,iBAAiB,CAACoB,WAAW,GAAI,qBAC/BxB,SAAS,CAACwB,WAAW,IAAIxB,SAAS,CAACG,IAAI,IAAI,WAC5C,GAAE;EAEH,oBAAO3C,KAAK,CAACoK,UAAU,CAAY,CAACtH,KAAK,EAAEI,GAAG,KAAK;IACjD,oBACElD,KAAA,CAAAkK,aAAA,CAACtH,iBAAiB,EAAA3E,QAAA,KACZ6E,KAAK,EACJI,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG;MAAEF,YAAY,EAAEE;IAAI,CAAC,CACjD,CAAC;EAEN,CAAC,CAAC;AACJ", "ignoreList": []}