{"version": 3, "names": ["isWindowAvailable", "setDummyPosition", "snapshots", "Animations", "PREDEFINED_WEB_ANIMATIONS_ID", "CUSTOM_WEB_ANIMATIONS_ID", "animationNameToIndex", "Map", "animationNameList", "isObserverSet", "configureWebLayoutAnimations", "document", "getElementById", "predefinedAnimationsStyleTag", "createElement", "id", "onload", "sheet", "console", "error", "animationName", "insertRule", "style", "customAnimationsStyleTag", "head", "append<PERSON><PERSON><PERSON>", "insertWebAnimation", "keyframe", "styleTag", "unshift", "set", "i", "length", "nextAnimationName", "nextAnimationIndex", "get", "undefined", "Error", "removeWebAnimation", "_styleTag$sheet", "currentAnimationIndex", "deleteRule", "splice", "delete", "timeoutScale", "frameDurationMs", "minimumFrames", "scheduleAnimationCleanup", "animationDuration", "timeoutValue", "Math", "max", "setTimeout", "reattachElementToAncestor", "child", "parent", "childSnapshot", "removedAfterAnimation", "originalOnAnimationEnd", "onanimationend", "event", "<PERSON><PERSON><PERSON><PERSON>", "call", "findDescendantWithExitingAnimation", "node", "root", "HTMLElement", "reanimatedDummy", "children", "Array", "from", "checkIfScreenWasChanged", "<PERSON><PERSON><PERSON><PERSON>", "_mutationTarget$react", "reactFiberKey", "key", "Object", "keys", "startsWith", "memoizedProps", "navigation", "addHTMLMutationObserver", "observer", "MutationObserver", "mutationsList", "rootMutation", "target", "removedNodes", "observe", "body", "childList", "subtree", "areDOMRectsEqual", "r1", "r2", "x", "y", "width", "height"], "sources": ["domUtils.ts"], "sourcesContent": ["'use strict';\n\nimport type { ReanimatedHTMLElement } from '../../js-reanimated';\nimport { isWindowAvailable } from '../../PlatformChecker';\nimport { setDummyPosition, snapshots } from './componentStyle';\nimport { Animations } from './config';\nimport type { AnimationNames } from './config';\n\nconst PREDEFINED_WEB_ANIMATIONS_ID = 'ReanimatedPredefinedWebAnimationsStyle';\nconst CUSTOM_WEB_ANIMATIONS_ID = 'ReanimatedCustomWebAnimationsStyle';\n\n// Since we cannot remove keyframe from DOM by its name, we have to store its id\nconst animationNameToIndex = new Map<string, number>();\nconst animationNameList: string[] = [];\n\nlet isObserverSet = false;\n\n/**\n *  Creates `HTMLStyleElement`, inserts it into DOM and then inserts CSS rules into the stylesheet.\n *  If style element already exists, nothing happens.\n */\nexport function configureWebLayoutAnimations() {\n  if (\n    !isWindowAvailable() || // Without this check SSR crashes because document is undefined (NextExample on CI)\n    document.getElementById(PREDEFINED_WEB_ANIMATIONS_ID) !== null\n  ) {\n    return;\n  }\n\n  const predefinedAnimationsStyleTag = document.createElement('style');\n  predefinedAnimationsStyleTag.id = PREDEFINED_WEB_ANIMATIONS_ID;\n\n  predefinedAnimationsStyleTag.onload = () => {\n    if (!predefinedAnimationsStyleTag.sheet) {\n      console.error(\n        '[Reanimated] Failed to create layout animations stylesheet.'\n      );\n      return;\n    }\n\n    for (const animationName in Animations) {\n      predefinedAnimationsStyleTag.sheet.insertRule(\n        Animations[animationName as AnimationNames].style\n      );\n    }\n  };\n\n  const customAnimationsStyleTag = document.createElement('style');\n  customAnimationsStyleTag.id = CUSTOM_WEB_ANIMATIONS_ID;\n\n  document.head.appendChild(predefinedAnimationsStyleTag);\n  document.head.appendChild(customAnimationsStyleTag);\n}\n\nexport function insertWebAnimation(animationName: string, keyframe: string) {\n  // Without this check SSR crashes because document is undefined (NextExample on CI)\n  if (!isWindowAvailable()) {\n    return;\n  }\n\n  const styleTag = document.getElementById(\n    CUSTOM_WEB_ANIMATIONS_ID\n  ) as HTMLStyleElement;\n\n  if (!styleTag.sheet) {\n    console.error(\n      '[Reanimated] Failed to create layout animations stylesheet.'\n    );\n    return;\n  }\n\n  styleTag.sheet.insertRule(keyframe, 0);\n  animationNameList.unshift(animationName);\n  animationNameToIndex.set(animationName, 0);\n\n  for (let i = 1; i < animationNameList.length; ++i) {\n    const nextAnimationName = animationNameList[i];\n    const nextAnimationIndex = animationNameToIndex.get(nextAnimationName);\n\n    if (nextAnimationIndex === undefined) {\n      throw new Error('[Reanimated] Failed to obtain animation index.');\n    }\n\n    animationNameToIndex.set(animationNameList[i], nextAnimationIndex + 1);\n  }\n}\n\nfunction removeWebAnimation(animationName: string) {\n  // Without this check SSR crashes because document is undefined (NextExample on CI)\n  if (!isWindowAvailable()) {\n    return;\n  }\n\n  const styleTag = document.getElementById(\n    CUSTOM_WEB_ANIMATIONS_ID\n  ) as HTMLStyleElement;\n\n  const currentAnimationIndex = animationNameToIndex.get(animationName);\n\n  if (currentAnimationIndex === undefined) {\n    throw new Error('[Reanimated] Failed to obtain animation index.');\n  }\n\n  styleTag.sheet?.deleteRule(currentAnimationIndex);\n  animationNameList.splice(currentAnimationIndex, 1);\n  animationNameToIndex.delete(animationName);\n\n  for (let i = currentAnimationIndex; i < animationNameList.length; ++i) {\n    const nextAnimationName = animationNameList[i];\n    const nextAnimationIndex = animationNameToIndex.get(nextAnimationName);\n\n    if (nextAnimationIndex === undefined) {\n      throw new Error('[Reanimated] Failed to obtain animation index.');\n    }\n\n    animationNameToIndex.set(animationNameList[i], nextAnimationIndex - 1);\n  }\n}\n\nconst timeoutScale = 1.25; // We use this value to enlarge timeout duration. It can prove useful if animation lags.\nconst frameDurationMs = 16; // Just an approximation.\nconst minimumFrames = 10;\n\nexport function scheduleAnimationCleanup(\n  animationName: string,\n  animationDuration: number\n) {\n  // If duration is very short, we want to keep remove delay to at least 10 frames\n  // In our case it is exactly 160/1099 s, which is approximately 0.15s\n  const timeoutValue = Math.max(\n    animationDuration * timeoutScale * 1000,\n    animationDuration + frameDurationMs * minimumFrames\n  );\n\n  setTimeout(() => removeWebAnimation(animationName), timeoutValue);\n}\n\nfunction reattachElementToAncestor(child: ReanimatedHTMLElement, parent: Node) {\n  const childSnapshot = snapshots.get(child);\n\n  if (!childSnapshot) {\n    console.error('[Reanimated] Failed to obtain snapshot.');\n    return;\n  }\n\n  // We use that so we don't end up in infinite loop\n  child.removedAfterAnimation = true;\n  parent.appendChild(child);\n\n  setDummyPosition(child, childSnapshot);\n\n  const originalOnAnimationEnd = child.onanimationend;\n\n  child.onanimationend = function (event: AnimationEvent) {\n    parent.removeChild(child);\n\n    // Given that this function overrides onAnimationEnd, it won't be null\n    originalOnAnimationEnd?.call(this, event);\n  };\n}\n\nfunction findDescendantWithExitingAnimation(\n  node: ReanimatedHTMLElement,\n  root: Node\n) {\n  // Node could be something else than HTMLElement, for example TextNode (treated as plain text, not as HTML object),\n  // therefore it won't have children prop and calling Array.from(node.children) will cause error.\n  if (!(node instanceof HTMLElement)) {\n    return;\n  }\n\n  if (node.reanimatedDummy && node.removedAfterAnimation === undefined) {\n    reattachElementToAncestor(node, root);\n  }\n\n  const children = Array.from(node.children);\n\n  for (let i = 0; i < children.length; ++i) {\n    findDescendantWithExitingAnimation(\n      children[i] as ReanimatedHTMLElement,\n      root\n    );\n  }\n}\n\ntype FiberNodeKey = `__reactFiber${string}`;\n\ninterface FiberNode {\n  memoizedProps?: {\n    navigation?: unknown;\n  };\n\n  child?: FiberNode;\n}\n\ntype WithFiberNode = {\n  [key: FiberNodeKey]: FiberNode;\n};\n\ntype MaybeWithFiberNode = Partial<WithFiberNode>;\n\nfunction checkIfScreenWasChanged(\n  mutationTarget: ReanimatedHTMLElement & MaybeWithFiberNode\n) {\n  let reactFiberKey: FiberNodeKey = '__reactFiber';\n\n  for (const key of Object.keys(mutationTarget)) {\n    if (key.startsWith('__reactFiber')) {\n      reactFiberKey = key as FiberNodeKey;\n      break;\n    }\n  }\n\n  return (\n    mutationTarget[reactFiberKey]?.child?.memoizedProps?.navigation !==\n    undefined\n  );\n}\n\nexport function addHTMLMutationObserver() {\n  if (isObserverSet || !isWindowAvailable()) {\n    return;\n  }\n\n  isObserverSet = true;\n\n  const observer = new MutationObserver((mutationsList) => {\n    const rootMutation = mutationsList[mutationsList.length - 1];\n\n    if (\n      checkIfScreenWasChanged(\n        rootMutation.target as ReanimatedHTMLElement & MaybeWithFiberNode\n      )\n    ) {\n      return;\n    }\n\n    for (let i = 0; i < rootMutation.removedNodes.length; ++i) {\n      findDescendantWithExitingAnimation(\n        rootMutation.removedNodes[i] as ReanimatedHTMLElement,\n        rootMutation.target\n      );\n    }\n  });\n\n  observer.observe(document.body, { childList: true, subtree: true });\n}\n\nexport function areDOMRectsEqual(r1: DOMRect, r2: DOMRect) {\n  // There are 4 more fields, but checking these should suffice\n  return (\n    r1.x === r2.x &&\n    r1.y === r2.y &&\n    r1.width === r2.width &&\n    r1.height === r2.height\n  );\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,gBAAgB,EAAEC,SAAS,QAAQ,kBAAkB;AAC9D,SAASC,UAAU,QAAQ,UAAU;AAGrC,MAAMC,4BAA4B,GAAG,wCAAwC;AAC7E,MAAMC,wBAAwB,GAAG,oCAAoC;;AAErE;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAiB,CAAC;AACtD,MAAMC,iBAA2B,GAAG,EAAE;AAEtC,IAAIC,aAAa,GAAG,KAAK;;AAEzB;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAAA,EAAG;EAC7C,IACE,CAACV,iBAAiB,CAAC,CAAC;EAAI;EACxBW,QAAQ,CAACC,cAAc,CAACR,4BAA4B,CAAC,KAAK,IAAI,EAC9D;IACA;EACF;EAEA,MAAMS,4BAA4B,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpED,4BAA4B,CAACE,EAAE,GAAGX,4BAA4B;EAE9DS,4BAA4B,CAACG,MAAM,GAAG,MAAM;IAC1C,IAAI,CAACH,4BAA4B,CAACI,KAAK,EAAE;MACvCC,OAAO,CAACC,KAAK,CACX,6DACF,CAAC;MACD;IACF;IAEA,KAAK,MAAMC,aAAa,IAAIjB,UAAU,EAAE;MACtCU,4BAA4B,CAACI,KAAK,CAACI,UAAU,CAC3ClB,UAAU,CAACiB,aAAa,CAAmB,CAACE,KAC9C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAGZ,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EAChES,wBAAwB,CAACR,EAAE,GAAGV,wBAAwB;EAEtDM,QAAQ,CAACa,IAAI,CAACC,WAAW,CAACZ,4BAA4B,CAAC;EACvDF,QAAQ,CAACa,IAAI,CAACC,WAAW,CAACF,wBAAwB,CAAC;AACrD;AAEA,OAAO,SAASG,kBAAkBA,CAACN,aAAqB,EAAEO,QAAgB,EAAE;EAC1E;EACA,IAAI,CAAC3B,iBAAiB,CAAC,CAAC,EAAE;IACxB;EACF;EAEA,MAAM4B,QAAQ,GAAGjB,QAAQ,CAACC,cAAc,CACtCP,wBACF,CAAqB;EAErB,IAAI,CAACuB,QAAQ,CAACX,KAAK,EAAE;IACnBC,OAAO,CAACC,KAAK,CACX,6DACF,CAAC;IACD;EACF;EAEAS,QAAQ,CAACX,KAAK,CAACI,UAAU,CAACM,QAAQ,EAAE,CAAC,CAAC;EACtCnB,iBAAiB,CAACqB,OAAO,CAACT,aAAa,CAAC;EACxCd,oBAAoB,CAACwB,GAAG,CAACV,aAAa,EAAE,CAAC,CAAC;EAE1C,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,iBAAiB,CAACwB,MAAM,EAAE,EAAED,CAAC,EAAE;IACjD,MAAME,iBAAiB,GAAGzB,iBAAiB,CAACuB,CAAC,CAAC;IAC9C,MAAMG,kBAAkB,GAAG5B,oBAAoB,CAAC6B,GAAG,CAACF,iBAAiB,CAAC;IAEtE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;MACpC,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA/B,oBAAoB,CAACwB,GAAG,CAACtB,iBAAiB,CAACuB,CAAC,CAAC,EAAEG,kBAAkB,GAAG,CAAC,CAAC;EACxE;AACF;AAEA,SAASI,kBAAkBA,CAAClB,aAAqB,EAAE;EAAA,IAAAmB,eAAA;EACjD;EACA,IAAI,CAACvC,iBAAiB,CAAC,CAAC,EAAE;IACxB;EACF;EAEA,MAAM4B,QAAQ,GAAGjB,QAAQ,CAACC,cAAc,CACtCP,wBACF,CAAqB;EAErB,MAAMmC,qBAAqB,GAAGlC,oBAAoB,CAAC6B,GAAG,CAACf,aAAa,CAAC;EAErE,IAAIoB,qBAAqB,KAAKJ,SAAS,EAAE;IACvC,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EAEA,CAAAE,eAAA,GAAAX,QAAQ,CAACX,KAAK,cAAAsB,eAAA,eAAdA,eAAA,CAAgBE,UAAU,CAACD,qBAAqB,CAAC;EACjDhC,iBAAiB,CAACkC,MAAM,CAACF,qBAAqB,EAAE,CAAC,CAAC;EAClDlC,oBAAoB,CAACqC,MAAM,CAACvB,aAAa,CAAC;EAE1C,KAAK,IAAIW,CAAC,GAAGS,qBAAqB,EAAET,CAAC,GAAGvB,iBAAiB,CAACwB,MAAM,EAAE,EAAED,CAAC,EAAE;IACrE,MAAME,iBAAiB,GAAGzB,iBAAiB,CAACuB,CAAC,CAAC;IAC9C,MAAMG,kBAAkB,GAAG5B,oBAAoB,CAAC6B,GAAG,CAACF,iBAAiB,CAAC;IAEtE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;MACpC,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA/B,oBAAoB,CAACwB,GAAG,CAACtB,iBAAiB,CAACuB,CAAC,CAAC,EAAEG,kBAAkB,GAAG,CAAC,CAAC;EACxE;AACF;AAEA,MAAMU,YAAY,GAAG,IAAI,CAAC,CAAC;AAC3B,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAMC,aAAa,GAAG,EAAE;AAExB,OAAO,SAASC,wBAAwBA,CACtC3B,aAAqB,EACrB4B,iBAAyB,EACzB;EACA;EACA;EACA,MAAMC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAC3BH,iBAAiB,GAAGJ,YAAY,GAAG,IAAI,EACvCI,iBAAiB,GAAGH,eAAe,GAAGC,aACxC,CAAC;EAEDM,UAAU,CAAC,MAAMd,kBAAkB,CAAClB,aAAa,CAAC,EAAE6B,YAAY,CAAC;AACnE;AAEA,SAASI,yBAAyBA,CAACC,KAA4B,EAAEC,MAAY,EAAE;EAC7E,MAAMC,aAAa,GAAGtD,SAAS,CAACiC,GAAG,CAACmB,KAAK,CAAC;EAE1C,IAAI,CAACE,aAAa,EAAE;IAClBtC,OAAO,CAACC,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF;;EAEA;EACAmC,KAAK,CAACG,qBAAqB,GAAG,IAAI;EAClCF,MAAM,CAAC9B,WAAW,CAAC6B,KAAK,CAAC;EAEzBrD,gBAAgB,CAACqD,KAAK,EAAEE,aAAa,CAAC;EAEtC,MAAME,sBAAsB,GAAGJ,KAAK,CAACK,cAAc;EAEnDL,KAAK,CAACK,cAAc,GAAG,UAAUC,KAAqB,EAAE;IACtDL,MAAM,CAACM,WAAW,CAACP,KAAK,CAAC;;IAEzB;IACAI,sBAAsB,aAAtBA,sBAAsB,eAAtBA,sBAAsB,CAAEI,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;EAC3C,CAAC;AACH;AAEA,SAASG,kCAAkCA,CACzCC,IAA2B,EAC3BC,IAAU,EACV;EACA;EACA;EACA,IAAI,EAAED,IAAI,YAAYE,WAAW,CAAC,EAAE;IAClC;EACF;EAEA,IAAIF,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACP,qBAAqB,KAAKrB,SAAS,EAAE;IACpEiB,yBAAyB,CAACW,IAAI,EAAEC,IAAI,CAAC;EACvC;EAEA,MAAMG,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACN,IAAI,CAACI,QAAQ,CAAC;EAE1C,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,CAACpC,MAAM,EAAE,EAAED,CAAC,EAAE;IACxCgC,kCAAkC,CAChCK,QAAQ,CAACrC,CAAC,CAAC,EACXkC,IACF,CAAC;EACH;AACF;AAkBA,SAASM,uBAAuBA,CAC9BC,cAA0D,EAC1D;EAAA,IAAAC,qBAAA;EACA,IAAIC,aAA2B,GAAG,cAAc;EAEhD,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,EAAE;IAC7C,IAAIG,GAAG,CAACG,UAAU,CAAC,cAAc,CAAC,EAAE;MAClCJ,aAAa,GAAGC,GAAmB;MACnC;IACF;EACF;EAEA,OACE,EAAAF,qBAAA,GAAAD,cAAc,CAACE,aAAa,CAAC,cAAAD,qBAAA,gBAAAA,qBAAA,GAA7BA,qBAAA,CAA+BnB,KAAK,cAAAmB,qBAAA,gBAAAA,qBAAA,GAApCA,qBAAA,CAAsCM,aAAa,cAAAN,qBAAA,uBAAnDA,qBAAA,CAAqDO,UAAU,MAC/D5C,SAAS;AAEb;AAEA,OAAO,SAAS6C,uBAAuBA,CAAA,EAAG;EACxC,IAAIxE,aAAa,IAAI,CAACT,iBAAiB,CAAC,CAAC,EAAE;IACzC;EACF;EAEAS,aAAa,GAAG,IAAI;EAEpB,MAAMyE,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,aAAa,IAAK;IACvD,MAAMC,YAAY,GAAGD,aAAa,CAACA,aAAa,CAACpD,MAAM,GAAG,CAAC,CAAC;IAE5D,IACEuC,uBAAuB,CACrBc,YAAY,CAACC,MACf,CAAC,EACD;MACA;IACF;IAEA,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,YAAY,CAACE,YAAY,CAACvD,MAAM,EAAE,EAAED,CAAC,EAAE;MACzDgC,kCAAkC,CAChCsB,YAAY,CAACE,YAAY,CAACxD,CAAC,CAAC,EAC5BsD,YAAY,CAACC,MACf,CAAC;IACH;EACF,CAAC,CAAC;EAEFJ,QAAQ,CAACM,OAAO,CAAC7E,QAAQ,CAAC8E,IAAI,EAAE;IAAEC,SAAS,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;AACrE;AAEA,OAAO,SAASC,gBAAgBA,CAACC,EAAW,EAAEC,EAAW,EAAE;EACzD;EACA,OACED,EAAE,CAACE,CAAC,KAAKD,EAAE,CAACC,CAAC,IACbF,EAAE,CAACG,CAAC,KAAKF,EAAE,CAACE,CAAC,IACbH,EAAE,CAACI,KAAK,KAAKH,EAAE,CAACG,KAAK,IACrBJ,EAAE,CAACK,MAAM,KAAKJ,EAAE,CAACI,MAAM;AAE3B", "ignoreList": []}