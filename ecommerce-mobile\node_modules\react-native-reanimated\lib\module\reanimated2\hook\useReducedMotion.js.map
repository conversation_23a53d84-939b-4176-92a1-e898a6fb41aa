{"version": 3, "names": ["isReducedMotion", "IS_REDUCED_MOTION", "useReducedMotion"], "sources": ["useReducedMotion.ts"], "sourcesContent": ["'use strict';\nimport { isReducedMotion } from '../PlatformChecker';\n\nconst IS_REDUCED_MOTION = isReducedMotion();\n\n/**\n * Lets you query the reduced motion system setting.\n *\n * Changing the reduced motion system setting doesn't cause your components to rerender.\n *\n * @returns A boolean indicating whether the reduced motion setting was enabled when the app started.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useReducedMotion\n */\nexport function useReducedMotion() {\n  return IS_REDUCED_MOTION;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,oBAAoB;AAEpD,MAAMC,iBAAiB,GAAGD,eAAe,CAAC,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAAA,EAAG;EACjC,OAAOD,iBAAiB;AAC1B", "ignoreList": []}