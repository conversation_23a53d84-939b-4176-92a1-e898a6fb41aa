{"version": 3, "names": ["SequencedTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "reversed", "scaleValue", "sequencedTransition", "style", "transform", "scale", "duration"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/transition/Sequenced.web.ts"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,mBAAmBA,CACjCC,IAAY,EACZC,cAA8B,EAC9B;EACA,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGL,cAAc;EAE3E,MAAMM,UAAU,GAAGD,QAAQ,GAAG,KAAKF,MAAM,EAAE,GAAG,GAAGC,MAAM,IAAI;EAE3D,MAAMG,mBAAmB,GAAG;IAC1BR,IAAI;IACJS,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACER,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BC,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BQ,KAAK,EAAE,GAAGP,MAAM,IAAIC,MAAM;QAC5B,CAAC;MAEL,CAAC;MACD,EAAE,EAAE;QACFK,SAAS,EAAE,CACT;UACER,UAAU,EAAEI,QAAQ,GAAG,GAAGJ,UAAU,IAAI,GAAG,KAAK;UAChDC,UAAU,EAAEG,QAAQ,GAAG,KAAK,GAAG,GAAGH,UAAU,IAAI;UAChDQ,KAAK,EAAEJ;QACT,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHG,SAAS,EAAE,CAAC;UAAER,UAAU,EAAE,KAAK;UAAEC,UAAU,EAAE,KAAK;UAAEQ,KAAK,EAAE;QAAM,CAAC;MACpE;IACF,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC;EAED,OAAOJ,mBAAmB;AAC5B", "ignoreList": []}