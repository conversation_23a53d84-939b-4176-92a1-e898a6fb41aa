{"version": 3, "names": ["getViewInfo", "element", "_nativeTag", "undefined", "__nativeTag", "getViewInfo73", "getViewInfoLatest", "_element$viewConfig", "viewName", "viewConfig", "uiViewClassName", "viewTag", "_element$_viewConfig", "_viewConfig"], "sources": ["getViewInfo.ts"], "sourcesContent": ["'use strict';\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n// This is a makeshift solution to handle both 0.73 and 0.74 versions of React Native.\n\nexport let getViewInfo = (element: any) => {\n  if (element._nativeTag !== undefined && element.__nativeTag !== null) {\n    getViewInfo = getViewInfo73;\n    return getViewInfo73(element);\n  } else if (\n    element.__nativeTag !== undefined &&\n    element.__nativeTag !== null\n  ) {\n    getViewInfo = getViewInfoLatest;\n    return getViewInfoLatest(element);\n  }\n  return getViewInfo73(element);\n};\n\nfunction getViewInfo73(element: any) {\n  return {\n    // we can access view tag in the same way it's accessed here https://github.com/facebook/react/blob/e3f4eb7272d4ca0ee49f27577156b57eeb07cf73/packages/react-native-renderer/src/ReactFabric.js#L146\n    viewName: element?.viewConfig?.uiViewClassName,\n    /**\n     * R<PERSON> uses viewConfig for components for storing different properties of the component(example: https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js#L24).\n     * The name we're looking for is in the field named uiViewClassName.\n     */\n    viewTag: element?._nativeTag,\n    viewConfig: element?.viewConfig,\n  };\n}\n\nfunction getViewInfoLatest(element: any) {\n  return {\n    viewName: element?._viewConfig?.uiViewClassName,\n    viewTag: element?.__nativeTag,\n    viewConfig: element?._viewConfig,\n  };\n}\n"], "mappings": "AAAA,YAAY;;AACZ;;AAEA;AAEA,OAAO,IAAIA,WAAW,GAAIC,OAAY,IAAK;EACzC,IAAIA,OAAO,CAACC,UAAU,KAAKC,SAAS,IAAIF,OAAO,CAACG,WAAW,KAAK,IAAI,EAAE;IACpEJ,WAAW,GAAGK,aAAa;IAC3B,OAAOA,aAAa,CAACJ,OAAO,CAAC;EAC/B,CAAC,MAAM,IACLA,OAAO,CAACG,WAAW,KAAKD,SAAS,IACjCF,OAAO,CAACG,WAAW,KAAK,IAAI,EAC5B;IACAJ,WAAW,GAAGM,iBAAiB;IAC/B,OAAOA,iBAAiB,CAACL,OAAO,CAAC;EACnC;EACA,OAAOI,aAAa,CAACJ,OAAO,CAAC;AAC/B,CAAC;AAED,SAASI,aAAaA,CAACJ,OAAY,EAAE;EAAA,IAAAM,mBAAA;EACnC,OAAO;IACL;IACAC,QAAQ,EAAEP,OAAO,aAAPA,OAAO,gBAAAM,mBAAA,GAAPN,OAAO,CAAEQ,UAAU,cAAAF,mBAAA,uBAAnBA,mBAAA,CAAqBG,eAAe;IAC9C;AACJ;AACA;AACA;IACIC,OAAO,EAAEV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,UAAU;IAC5BO,UAAU,EAAER,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ;EACvB,CAAC;AACH;AAEA,SAASH,iBAAiBA,CAACL,OAAY,EAAE;EAAA,IAAAW,oBAAA;EACvC,OAAO;IACLJ,QAAQ,EAAEP,OAAO,aAAPA,OAAO,gBAAAW,oBAAA,GAAPX,OAAO,CAAEY,WAAW,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBF,eAAe;IAC/CC,OAAO,EAAEV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,WAAW;IAC7BK,UAAU,EAAER,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEY;EACvB,CAAC;AACH", "ignoreList": []}