{"version": 3, "names": ["Easing", "JumpingTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "d", "Math", "max", "abs", "peakTranslateY", "jumpingTransition", "style", "transform", "scale", "easing", "exp", "duration"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/transition/Jumping.web.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,oBAAiB;AAExC,OAAO,SAASC,iBAAiBA,CAC/BC,IAAY,EACZC,cAA8B,EAC9B;EACA,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGJ,cAAc;EAEjE,MAAMK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACP,UAAU,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACN,UAAU,CAAC,CAAC,GAAG,CAAC;EAClE,MAAMO,cAAc,GAAGP,UAAU,IAAI,CAAC,GAAGA,UAAU,GAAGG,CAAC,GAAG,CAACH,UAAU,GAAGG,CAAC;EAEzE,MAAMK,iBAAiB,GAAG;IACxBX,IAAI;IACJY,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEX,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BC,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BW,KAAK,EAAE,GAAGV,MAAM,IAAIC,MAAM;QAC5B,CAAC,CACF;QACDU,MAAM,EAAEjB,MAAM,CAACkB;MACjB,CAAC;MACD,EAAE,EAAE;QACFH,SAAS,EAAE,CACT;UACEX,UAAU,EAAE,GAAGA,UAAU,GAAG,CAAC,IAAI;UACjCC,UAAU,EAAE,GAAGO,cAAc,IAAI;UACjCI,KAAK,EAAE,GAAGV,MAAM,IAAIC,MAAM;QAC5B,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHQ,SAAS,EAAE,CAAC;UAAEX,UAAU,EAAE,KAAK;UAAEC,UAAU,EAAE,KAAK;UAAEW,KAAK,EAAE;QAAM,CAAC;MACpE;IACF,CAAC;IACDG,QAAQ,EAAE;EACZ,CAAC;EAED,OAAON,iBAAiB;AAC1B", "ignoreList": []}