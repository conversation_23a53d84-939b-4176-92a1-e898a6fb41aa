{"version": 3, "names": ["React", "forwardRef", "ScrollView", "createAnimatedComponent", "useAnimatedRef", "useScrollViewOffset", "jsx", "_jsx", "AnimatedScrollViewComponent", "AnimatedScrollView", "props", "ref", "scrollViewOffset", "restProps", "animatedRef", "scrollEventThrottle"], "sourceRoot": "../../../src", "sources": ["component/ScrollView.tsx"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAEzC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,uBAAuB,QAAQ,qCAA4B;AAGpE,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,kBAAS;;AAQ7D;AACA;AAAA,SAAAC,GAAA,IAAAC,IAAA;AAKA,MAAMC,2BAA2B,GAAGL,uBAAuB,CAACD,UAAU,CAAC;AAEvE,OAAO,MAAMO,kBAAkB,gBAAGR,UAAU,CAC1C,CAACS,KAA8B,EAAEC,GAAqC,KAAK;EACzE,MAAM;IAAEC,gBAAgB;IAAE,GAAGC;EAAU,CAAC,GAAGH,KAAK;EAChD,MAAMI,WAAW,GACfH,GAAG,KAAK,IAAI;EACR;EACAP,cAAc,CAAa,CAAC,GAC5BO,GAC8B;EAEpC,IAAIC,gBAAgB,EAAE;IACpB;IACAP,mBAAmB,CAACS,WAAW,EAAEF,gBAAgB,CAAC;EACpD;;EAEA;EACA;EACA;EACA;EACA,IAAI,EAAE,qBAAqB,IAAIC,SAAS,CAAC,EAAE;IACzCA,SAAS,CAACE,mBAAmB,GAAG,CAAC;EACnC;EAEA,oBAAOR,IAAA,CAACC,2BAA2B;IAACG,GAAG,EAAEG,WAAY;IAAA,GAAKD;EAAS,CAAG,CAAC;AACzE,CACF,CAAC", "ignoreList": []}