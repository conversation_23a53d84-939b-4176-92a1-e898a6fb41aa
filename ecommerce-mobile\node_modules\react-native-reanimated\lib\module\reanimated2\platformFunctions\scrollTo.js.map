{"version": 3, "names": ["isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "dispatchCommand", "scrollTo", "scrollToFabric", "animatedRef", "x", "y", "animated", "scrollToPaper", "_WORKLET", "viewTag", "global", "_scrollToPaper", "scrollToJest", "console", "warn", "scrollToChromeDebugger", "scrollToDefault"], "sources": ["scrollTo.ts"], "sourcesContent": ["'use strict';\nimport {\n  isChromeDebugger,\n  isF<PERSON><PERSON>,\n  isJest,\n  shouldBeUseWeb,\n} from '../PlatformChecker';\nimport { dispatchCommand } from './dispatchCommand';\nimport type {\n  AnimatedRef,\n  AnimatedRefOnJS,\n  AnimatedRefOnUI,\n} from '../hook/commonTypes';\nimport type { Component } from 'react';\n\ntype ScrollTo = <T extends Component>(\n  animatedRef: AnimatedRef<T>,\n  x: number,\n  y: number,\n  animated: boolean\n) => void;\n\n/**\n * Lets you synchronously scroll to a given position of a `ScrollView`.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef) attached to an `Animated.ScrollView` component.\n * @param x - The x position you want to scroll to.\n * @param y - The y position you want to scroll to.\n * @param animated - Whether the scrolling should be smooth or instant.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/scrollTo\n */\nexport let scrollTo: ScrollTo;\n\nfunction scrollToFabric(\n  animatedRef: AnimatedRefOnJS | AnimatedRefOnUI,\n  x: number,\n  y: number,\n  animated: boolean\n) {\n  'worklet';\n  dispatchCommand(\n    // This assertion is needed to comply to `dispatchCommand` interface\n    animatedRef as unknown as AnimatedRef<Component>,\n    'scrollTo',\n    [x, y, animated]\n  );\n}\n\nfunction scrollToPaper(\n  animatedRef: AnimatedRefOnJS | AnimatedRefOnUI,\n  x: number,\n  y: number,\n  animated: boolean\n) {\n  'worklet';\n  if (!_WORKLET) {\n    return;\n  }\n\n  const viewTag = animatedRef() as number;\n  global._scrollToPaper!(viewTag, x, y, animated);\n}\n\nfunction scrollToJest() {\n  console.warn('[Reanimated] scrollTo() is not supported with Jest.');\n}\n\nfunction scrollToChromeDebugger() {\n  console.warn(\n    '[Reanimated] scrollTo() is not supported with Chrome Debugger.'\n  );\n}\n\nfunction scrollToDefault() {\n  console.warn(\n    '[Reanimated] scrollTo() is not supported on this configuration.'\n  );\n}\n\nif (!shouldBeUseWeb()) {\n  // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n  // mapped as a different function in `shareableMappingCache` and\n  // TypeScript is not able to infer that.\n  if (isFabric()) {\n    scrollTo = scrollToFabric as unknown as ScrollTo;\n  } else {\n    scrollTo = scrollToPaper as unknown as ScrollTo;\n  }\n} else if (isJest()) {\n  scrollTo = scrollToJest;\n} else if (isChromeDebugger()) {\n  scrollTo = scrollToChromeDebugger;\n} else {\n  scrollTo = scrollToDefault;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SACEA,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,oBAAoB;AAC3B,SAASC,eAAe,QAAQ,mBAAmB;AAenD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAkB;AAE7B,SAASC,cAAcA,CACrBC,WAA8C,EAC9CC,CAAS,EACTC,CAAS,EACTC,QAAiB,EACjB;EACA,SAAS;;EACTN,eAAe;EACb;EACAG,WAAW,EACX,UAAU,EACV,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,CACjB,CAAC;AACH;AAEA,SAASC,aAAaA,CACpBJ,WAA8C,EAC9CC,CAAS,EACTC,CAAS,EACTC,QAAiB,EACjB;EACA,SAAS;;EACT,IAAI,CAACE,QAAQ,EAAE;IACb;EACF;EAEA,MAAMC,OAAO,GAAGN,WAAW,CAAC,CAAW;EACvCO,MAAM,CAACC,cAAc,CAAEF,OAAO,EAAEL,CAAC,EAAEC,CAAC,EAAEC,QAAQ,CAAC;AACjD;AAEA,SAASM,YAAYA,CAAA,EAAG;EACtBC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;AACrE;AAEA,SAASC,sBAAsBA,CAAA,EAAG;EAChCF,OAAO,CAACC,IAAI,CACV,gEACF,CAAC;AACH;AAEA,SAASE,eAAeA,CAAA,EAAG;EACzBH,OAAO,CAACC,IAAI,CACV,iEACF,CAAC;AACH;AAEA,IAAI,CAACf,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdI,QAAQ,GAAGC,cAAqC;EAClD,CAAC,MAAM;IACLD,QAAQ,GAAGM,aAAoC;EACjD;AACF,CAAC,MAAM,IAAIT,MAAM,CAAC,CAAC,EAAE;EACnBG,QAAQ,GAAGW,YAAY;AACzB,CAAC,MAAM,IAAIhB,gBAAgB,CAAC,CAAC,EAAE;EAC7BK,QAAQ,GAAGc,sBAAsB;AACnC,CAAC,MAAM;EACLd,QAAQ,GAAGe,eAAe;AAC5B", "ignoreList": []}