{"version": 3, "names": ["<PERSON><PERSON>", "linear", "t", "ease", "quad", "cubic", "poly", "n", "Math", "pow", "sin", "cos", "PI", "circle", "sqrt", "exp", "elastic", "bounciness", "p", "back", "s", "bounce", "t2", "bezier", "x1", "y1", "x2", "y2", "factory", "bezierFn", "in_", "easing", "out", "inOut", "steps", "roundToNextStep", "value", "min", "max", "ceil", "floor", "EasingObject", "in", "Easing"], "sources": ["Easing.ts"], "sourcesContent": ["'use strict';\nimport { Bezier } from './Bezier';\n\n/**\n * The `Easing` module implements common easing functions. This module is used\n * by [Animate.timing()](docs/animate.html#timing) to convey physically\n * believable motion in animations.\n *\n * You can find a visualization of some common easing functions at\n * http://easings.net/\n *\n * ### Predefined animations\n *\n * The `Easing` module provides several predefined animations through the\n * following methods:\n *\n * - [`back`](docs/easing.html#back) provides a simple animation where the\n *   object goes slightly back before moving forward\n * - [`bounce`](docs/easing.html#bounce) provides a bouncing animation\n * - [`ease`](docs/easing.html#ease) provides a simple inertial animation\n * - [`elastic`](docs/easing.html#elastic) provides a simple spring interaction\n *\n * ### Standard functions\n *\n * Three standard easing functions are provided:\n *\n * - [`linear`](docs/easing.html#linear)\n * - [`quad`](docs/easing.html#quad)\n * - [`cubic`](docs/easing.html#cubic)\n *\n * The [`poly`](docs/easing.html#poly) function can be used to implement\n * quartic, quintic, and other higher power functions.\n *\n * ### Additional functions\n *\n * Additional mathematical functions are provided by the following methods:\n *\n * - [`bezier`](docs/easing.html#bezier) provides a cubic bezier curve\n * - [`circle`](docs/easing.html#circle) provides a circular function\n * - [`sin`](docs/easing.html#sin) provides a sinusoidal function\n * - [`exp`](docs/easing.html#exp) provides an exponential function\n *\n * The following helpers are used to modify other easing functions.\n *\n * - [`in`](docs/easing.html#in) runs an easing function forwards\n * - [`inOut`](docs/easing.html#inout) makes any easing function symmetrical\n * - [`out`](docs/easing.html#out) runs an easing function backwards\n */\n\nexport type EasingFunction = (t: number) => number;\n\n/**\n * @deprecated Please use {@link EasingFunction} type instead.\n */\nexport type EasingFn = EasingFunction;\n\nexport type EasingFunctionFactory = { factory: () => EasingFunction };\n\n/**\n * @deprecated Please use {@link EasingFunctionFactory} type instead.\n */\nexport type EasingFactoryFn = EasingFunctionFactory;\n/**\n * A linear function, `f(t) = t`. Position correlates to elapsed time one to\n * one.\n *\n * http://cubic-bezier.com/#0,0,1,1\n */\nfunction linear(t: number): number {\n  'worklet';\n  return t;\n}\n\n/**\n * A simple inertial interaction, similar to an object slowly accelerating to\n * speed.\n *\n * http://cubic-bezier.com/#.42,0,1,1\n */\nfunction ease(t: number): number {\n  'worklet';\n  return Bezier(0.42, 0, 1, 1)(t);\n}\n\n/**\n * A quadratic function, `f(t) = t * t`. Position equals the square of elapsed\n * time.\n *\n * http://easings.net/#easeInQuad\n */\nfunction quad(t: number): number {\n  'worklet';\n  return t * t;\n}\n\n/**\n * A cubic function, `f(t) = t * t * t`. Position equals the cube of elapsed\n * time.\n *\n * http://easings.net/#easeInCubic\n */\nfunction cubic(t: number): number {\n  'worklet';\n  return t * t * t;\n}\n\n/**\n * A power function. Position is equal to the Nth power of elapsed time.\n *\n * n = 4: http://easings.net/#easeInQuart\n * n = 5: http://easings.net/#easeInQuint\n */\nfunction poly(n: number): EasingFunction {\n  'worklet';\n  return (t) => {\n    'worklet';\n    return Math.pow(t, n);\n  };\n}\n\n/**\n * A sinusoidal function.\n *\n * http://easings.net/#easeInSine\n */\nfunction sin(t: number): number {\n  'worklet';\n  return 1 - Math.cos((t * Math.PI) / 2);\n}\n\n/**\n * A circular function.\n *\n * http://easings.net/#easeInCirc\n */\nfunction circle(t: number): number {\n  'worklet';\n  return 1 - Math.sqrt(1 - t * t);\n}\n\n/**\n * An exponential function.\n *\n * http://easings.net/#easeInExpo\n */\nfunction exp(t: number): number {\n  'worklet';\n  return Math.pow(2, 10 * (t - 1));\n}\n\n/**\n * A simple elastic interaction, similar to a spring oscillating back and\n * forth.\n *\n * Default bounciness is 1, which overshoots a little bit once. 0 bounciness\n * doesn't overshoot at all, and bounciness of N \\> 1 will overshoot about N\n * times.\n *\n * http://easings.net/#easeInElastic\n */\nfunction elastic(bounciness = 1): EasingFunction {\n  'worklet';\n  const p = bounciness * Math.PI;\n  return (t) => {\n    'worklet';\n    return 1 - Math.pow(Math.cos((t * Math.PI) / 2), 3) * Math.cos(t * p);\n  };\n}\n\n/**\n * Use with `Animated.parallel()` to create a simple effect where the object\n * animates back slightly as the animation starts.\n *\n * Wolfram Plot:\n *\n * - http://tiny.cc/back_default (s = 1.70158, default)\n */\nfunction back(s = 1.70158): (t: number) => number {\n  'worklet';\n  return (t) => {\n    'worklet';\n    return t * t * ((s + 1) * t - s);\n  };\n}\n\n/**\n * Provides a simple bouncing effect.\n *\n * http://easings.net/#easeInBounce\n */\nfunction bounce(t: number): number {\n  'worklet';\n  if (t < 1 / 2.75) {\n    return 7.5625 * t * t;\n  }\n\n  if (t < 2 / 2.75) {\n    const t2 = t - 1.5 / 2.75;\n    return 7.5625 * t2 * t2 + 0.75;\n  }\n\n  if (t < 2.5 / 2.75) {\n    const t2 = t - 2.25 / 2.75;\n    return 7.5625 * t2 * t2 + 0.9375;\n  }\n\n  const t2 = t - 2.625 / 2.75;\n  return 7.5625 * t2 * t2 + 0.984375;\n}\n\n/**\n * Provides a cubic bezier curve, equivalent to CSS Transitions'\n * `transition-timing-function`.\n *\n * A useful tool to visualize cubic bezier curves can be found at\n * http://cubic-bezier.com/\n */\nfunction bezier(\n  x1: number,\n  y1: number,\n  x2: number,\n  y2: number\n): { factory: () => (x: number) => number } {\n  'worklet';\n  return {\n    factory: () => {\n      'worklet';\n      return Bezier(x1, y1, x2, y2);\n    },\n  };\n}\n\nfunction bezierFn(\n  x1: number,\n  y1: number,\n  x2: number,\n  y2: number\n): (x: number) => number {\n  'worklet';\n  return Bezier(x1, y1, x2, y2);\n}\n\n/**\n * Runs an easing function forwards.\n */\nfunction in_(easing: EasingFunction): EasingFunction {\n  'worklet';\n  return easing;\n}\n\n/**\n * Runs an easing function backwards.\n */\nfunction out(easing: EasingFunction): EasingFunction {\n  'worklet';\n  return (t) => {\n    'worklet';\n    return 1 - easing(1 - t);\n  };\n}\n\n/**\n * Makes any easing function symmetrical. The easing function will run\n * forwards for half of the duration, then backwards for the rest of the\n * duration.\n */\nfunction inOut(easing: EasingFunction): EasingFunction {\n  'worklet';\n  return (t) => {\n    'worklet';\n    if (t < 0.5) {\n      return easing(t * 2) / 2;\n    }\n    return 1 - easing((1 - t) * 2) / 2;\n  };\n}\n\n/**\n * The `steps` easing function jumps between discrete values at regular intervals,\n * creating a stepped animation effect. The `n` parameter determines the number of\n * steps in the animation, and the `roundToNextStep` parameter determines whether the animation\n * should start at the beginning or end of each step.\n */\nfunction steps(n = 10, roundToNextStep = true): EasingFunction {\n  'worklet';\n  return (t) => {\n    'worklet';\n    const value = Math.min(Math.max(t, 0), 1) * n;\n    if (roundToNextStep) {\n      return Math.ceil(value) / n;\n    }\n    return Math.floor(value) / n;\n  };\n}\n\nconst EasingObject = {\n  linear,\n  ease,\n  quad,\n  cubic,\n  poly,\n  sin,\n  circle,\n  exp,\n  elastic,\n  back,\n  bounce,\n  bezier,\n  bezierFn,\n  steps,\n  in: in_,\n  out,\n  inOut,\n};\n\nexport const Easing = EasingObject;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,MAAM,QAAQ,UAAU;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,CAAS,EAAU;EACjC,SAAS;;EACT,OAAOA,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACD,CAAS,EAAU;EAC/B,SAAS;;EACT,OAAOF,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACE,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAACF,CAAS,EAAU;EAC/B,SAAS;;EACT,OAAOA,CAAC,GAAGA,CAAC;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAACH,CAAS,EAAU;EAChC,SAAS;;EACT,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,IAAIA,CAACC,CAAS,EAAkB;EACvC,SAAS;;EACT,OAAQL,CAAC,IAAK;IACZ,SAAS;;IACT,OAAOM,IAAI,CAACC,GAAG,CAACP,CAAC,EAAEK,CAAC,CAAC;EACvB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,GAAGA,CAACR,CAAS,EAAU;EAC9B,SAAS;;EACT,OAAO,CAAC,GAAGM,IAAI,CAACG,GAAG,CAAET,CAAC,GAAGM,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACX,CAAS,EAAU;EACjC,SAAS;;EACT,OAAO,CAAC,GAAGM,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGZ,CAAC,GAAGA,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASa,GAAGA,CAACb,CAAS,EAAU;EAC9B,SAAS;;EACT,OAAOM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIP,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,OAAOA,CAACC,UAAU,GAAG,CAAC,EAAkB;EAC/C,SAAS;;EACT,MAAMC,CAAC,GAAGD,UAAU,GAAGT,IAAI,CAACI,EAAE;EAC9B,OAAQV,CAAC,IAAK;IACZ,SAAS;;IACT,OAAO,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG,CAAET,CAAC,GAAGM,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACT,CAAC,GAAGgB,CAAC,CAAC;EACvE,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,CAAC,GAAG,OAAO,EAAyB;EAChD,SAAS;;EACT,OAAQlB,CAAC,IAAK;IACZ,SAAS;;IACT,OAAOA,CAAC,GAAGA,CAAC,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAIlB,CAAC,GAAGkB,CAAC,CAAC;EAClC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACnB,CAAS,EAAU;EACjC,SAAS;;EACT,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;IAChB,OAAO,MAAM,GAAGA,CAAC,GAAGA,CAAC;EACvB;EAEA,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;IAChB,MAAMoB,EAAE,GAAGpB,CAAC,GAAG,GAAG,GAAG,IAAI;IACzB,OAAO,MAAM,GAAGoB,EAAE,GAAGA,EAAE,GAAG,IAAI;EAChC;EAEA,IAAIpB,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE;IAClB,MAAMoB,EAAE,GAAGpB,CAAC,GAAG,IAAI,GAAG,IAAI;IAC1B,OAAO,MAAM,GAAGoB,EAAE,GAAGA,EAAE,GAAG,MAAM;EAClC;EAEA,MAAMA,EAAE,GAAGpB,CAAC,GAAG,KAAK,GAAG,IAAI;EAC3B,OAAO,MAAM,GAAGoB,EAAE,GAAGA,EAAE,GAAG,QAAQ;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CACbC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACgC;EAC1C,SAAS;;EACT,OAAO;IACLC,OAAO,EAAEA,CAAA,KAAM;MACb,SAAS;;MACT,OAAO5B,MAAM,CAACwB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC/B;EACF,CAAC;AACH;AAEA,SAASE,QAAQA,CACfL,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACa;EACvB,SAAS;;EACT,OAAO3B,MAAM,CAACwB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC/B;;AAEA;AACA;AACA;AACA,SAASG,GAAGA,CAACC,MAAsB,EAAkB;EACnD,SAAS;;EACT,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA,SAASC,GAAGA,CAACD,MAAsB,EAAkB;EACnD,SAAS;;EACT,OAAQ7B,CAAC,IAAK;IACZ,SAAS;;IACT,OAAO,CAAC,GAAG6B,MAAM,CAAC,CAAC,GAAG7B,CAAC,CAAC;EAC1B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS+B,KAAKA,CAACF,MAAsB,EAAkB;EACrD,SAAS;;EACT,OAAQ7B,CAAC,IAAK;IACZ,SAAS;;IACT,IAAIA,CAAC,GAAG,GAAG,EAAE;MACX,OAAO6B,MAAM,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1B;IACA,OAAO,CAAC,GAAG6B,MAAM,CAAC,CAAC,CAAC,GAAG7B,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EACpC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgC,KAAKA,CAAC3B,CAAC,GAAG,EAAE,EAAE4B,eAAe,GAAG,IAAI,EAAkB;EAC7D,SAAS;;EACT,OAAQjC,CAAC,IAAK;IACZ,SAAS;;IACT,MAAMkC,KAAK,GAAG5B,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAAC8B,GAAG,CAACpC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGK,CAAC;IAC7C,IAAI4B,eAAe,EAAE;MACnB,OAAO3B,IAAI,CAAC+B,IAAI,CAACH,KAAK,CAAC,GAAG7B,CAAC;IAC7B;IACA,OAAOC,IAAI,CAACgC,KAAK,CAACJ,KAAK,CAAC,GAAG7B,CAAC;EAC9B,CAAC;AACH;AAEA,MAAMkC,YAAY,GAAG;EACnBxC,MAAM;EACNE,IAAI;EACJC,IAAI;EACJC,KAAK;EACLC,IAAI;EACJI,GAAG;EACHG,MAAM;EACNE,GAAG;EACHC,OAAO;EACPG,IAAI;EACJE,MAAM;EACNE,MAAM;EACNM,QAAQ;EACRK,KAAK;EACLQ,EAAE,EAAEZ,GAAG;EACPE,GAAG;EACHC;AACF,CAAC;AAED,OAAO,MAAMU,MAAM,GAAGF,YAAY", "ignoreList": []}