{"version": 3, "names": ["NativeReanimatedModule", "isJest", "shouldBeUseWeb", "makeShareableCloneOnUIRecursive", "makeShareableCloneRecursive", "isWorkletFunction", "ReanimatedError", "IS_JEST", "SHOULD_BE_USE_WEB", "_runOnUIQueue", "setupMicrotasks", "microtasksQueue", "isExecutingMicrotasksQueue", "global", "queueMicrotask", "callback", "push", "__callMicrotasks", "index", "length", "_maybeFlushUIUpdatesQueue", "callMicrotasksOnUIThread", "callMicrotasks", "runOnUI", "worklet", "__DEV__", "_WORKLET", "args", "scheduleOnUI", "queue", "for<PERSON>ach", "executeOnUIRuntimeSync", "result", "runOnUIImmediately", "runWorkletOnJS", "runOnJS", "fun", "__remoteFunction", "scheduleOnJS", "_scheduleHostFunctionOnJS", "_scheduleRemoteFunctionOnJS", "undefined"], "sourceRoot": "../../src", "sources": ["threads.ts"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,MAAM,EAAEC,cAAc,QAAQ,sBAAmB;AAE1D,SACEC,+BAA+B,EAC/BC,2BAA2B,QACtB,iBAAc;AACrB,SAASC,iBAAiB,QAAQ,kBAAe;AACjD,SAASC,eAAe,QAAQ,aAAU;AAE1C,MAAMC,OAAO,GAAGN,MAAM,CAAC,CAAC;AACxB,MAAMO,iBAAiB,GAAGN,cAAc,CAAC,CAAC;;AAE1C;AACA,IAAIO,aAAsE,GAAG,EAAE;AAE/E,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,SAAS;;EAET,IAAIC,eAAkC,GAAG,EAAE;EAC3C,IAAIC,0BAA0B,GAAG,KAAK;EACtCC,MAAM,CAACC,cAAc,GAAIC,QAAoB,IAAK;IAChDJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC;EAChC,CAAC;EAEDF,MAAM,CAACI,gBAAgB,GAAG,MAAM;IAC9B,IAAIL,0BAA0B,EAAE;MAC9B;IACF;IACA,IAAI;MACFA,0BAA0B,GAAG,IAAI;MACjC,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,eAAe,CAACQ,MAAM,EAAED,KAAK,IAAI,CAAC,EAAE;QAC9D;QACAP,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC;MAC1B;MACAP,eAAe,GAAG,EAAE;MACpBE,MAAM,CAACO,yBAAyB,CAAC,CAAC;IACpC,CAAC,SAAS;MACRR,0BAA0B,GAAG,KAAK;IACpC;EACF,CAAC;AACH;AAEA,SAASS,wBAAwBA,CAAA,EAAG;EAClC,SAAS;;EACTR,MAAM,CAACI,gBAAgB,CAAC,CAAC;AAC3B;AAEA,OAAO,MAAMK,cAAc,GAAGd,iBAAiB,GAC3C,MAAM;EACJ;AAAA,CACD,GACDa,wBAAwB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,OAAO,SAASE,OAAOA,CACrBC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACjB,iBAAiB,IAAIkB,QAAQ,EAAE;IAC7C,MAAM,IAAIpB,eAAe,CACvB,kJACF,CAAC;EACH;EACA,IAAImB,OAAO,IAAI,CAACjB,iBAAiB,IAAI,CAACH,iBAAiB,CAACmB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIlB,eAAe,CAAC,2CAA2C,CAAC;EACxE;EACA,OAAO,CAAC,GAAGqB,IAAI,KAAK;IAClB,IAAIpB,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAP,sBAAsB,CAAC4B,YAAY,CACjCxB,2BAA2B,CAAC,MAAM;QAChC,SAAS;;QACToB,OAAO,CAAC,GAAGG,IAAI,CAAC;MAClB,CAAC,CACH,CAAC;MACD;IACF;IACA,IAAIF,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACArB,2BAA2B,CAACoB,OAAO,CAAC;MACpCpB,2BAA2B,CAACuB,IAAI,CAAC;IACnC;IACAlB,aAAa,CAACO,IAAI,CAAC,CAACQ,OAAO,EAAqBG,IAAI,CAAC,CAAC;IACtD,IAAIlB,aAAa,CAACU,MAAM,KAAK,CAAC,EAAE;MAC9BL,cAAc,CAAC,MAAM;QACnB,MAAMe,KAAK,GAAGpB,aAAa;QAC3BA,aAAa,GAAG,EAAE;QAClBT,sBAAsB,CAAC4B,YAAY,CACjCxB,2BAA2B,CAAC,MAAM;UAChC,SAAS;;UACT;UACAyB,KAAK,CAACC,OAAO,CAAC,CAAC,CAACN,OAAO,EAAEG,IAAI,CAAC,KAAK;YACjCH,OAAO,CAAC,GAAGG,IAAI,CAAC;UAClB,CAAC,CAAC;UACFL,cAAc,CAAC,CAAC;QAClB,CAAC,CACH,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;;AAKA,OAAO,SAASS,sBAAsBA,CACpCP,OAA2C,EACX;EAChC,OAAO,CAAC,GAAGG,IAAI,KAAK;IAClB,OAAO3B,sBAAsB,CAAC+B,sBAAsB,CAClD3B,2BAA2B,CAAC,MAAM;MAChC,SAAS;;MACT,MAAM4B,MAAM,GAAGR,OAAO,CAAC,GAAGG,IAAI,CAAC;MAC/B,OAAOxB,+BAA+B,CAAC6B,MAAM,CAAC;IAChD,CAAC,CACH,CAAC;EACH,CAAC;AACH;;AAEA;;AAIA;AACA,OAAO,SAASC,kBAAkBA,CAChCT,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACjB,iBAAiB,IAAIkB,QAAQ,EAAE;IAC7C,MAAM,IAAIpB,eAAe,CACvB,6JACF,CAAC;EACH;EACA,IAAImB,OAAO,IAAI,CAACjB,iBAAiB,IAAI,CAACH,iBAAiB,CAACmB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIlB,eAAe,CACvB,sDACF,CAAC;EACH;EACA,OAAO,CAAC,GAAGqB,IAAI,KAAK;IAClB3B,sBAAsB,CAAC4B,YAAY,CACjCxB,2BAA2B,CAAC,MAAM;MAChC,SAAS;;MACToB,OAAO,CAAC,GAAGG,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACH,CAAC;AACH;AAcA,SAASO,cAAcA,CACrBV,OAA2C,EAC3C,GAAGG,IAAU,EACP;EACN;EACAH,OAAO,CAAC,GAAGG,IAAI,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,OAAOA,CACrBC,GAGsC,EACb;EACzB,SAAS;;EAET,IAAI5B,iBAAiB,IAAI,CAACkB,QAAQ,EAAE;IAClC;IACA,OAAO,CAAC,GAAGC,IAAI,KACbb,cAAc,CACZa,IAAI,CAACR,MAAM,GACP,MAAOiB,GAAG,CAAoC,GAAGT,IAAI,CAAC,GACrDS,GACP,CAAC;EACL;EACA,IAAI/B,iBAAiB,CAAoB+B,GAAG,CAAC,EAAE;IAC7C;IACA;;IAEA,OAAO,CAAC,GAAGT,IAAI,KACbQ,OAAO,CAACD,cAAiC,CAAC,CACxCE,GAAG,EACH,GAAGT,IACL,CAAC;EACL;EACA,IAAKS,GAAG,CAAkBC,gBAAgB,EAAE;IAC1C;IACA;IACA;IACA;IACAD,GAAG,GAAIA,GAAG,CAAkBC,gBAAgB;EAC9C;EAEA,MAAMC,YAAY,GAChB,OAAOF,GAAG,KAAK,UAAU,GACrBvB,MAAM,CAAC0B,yBAAyB,GAChC1B,MAAM,CAAC2B,2BAA2B;EAExC,OAAO,CAAC,GAAGb,IAAI,KAAK;IAClBW,YAAY,CACVF,GAAG,EAGHT,IAAI,CAACR,MAAM,GAAG,CAAC;IACX;IACChB,+BAA+B,CAACwB,IAAI,CAAC,GACtCc,SACN,CAAC;EACH,CAAC;AACH", "ignoreList": []}