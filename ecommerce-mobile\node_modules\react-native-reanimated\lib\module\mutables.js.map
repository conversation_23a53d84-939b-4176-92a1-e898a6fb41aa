{"version": 3, "names": ["shouldBeUseWeb", "ReanimatedError", "logger", "isFirstReactRender", "isReactRendering", "shareableMappingCache", "makeShareableCloneRecursive", "executeOnUIRuntimeSync", "runOnUI", "valueSetter", "SHOULD_BE_USE_WEB", "shouldWarnAboutAccessDuringRender", "__DEV__", "checkInvalidReadDuringRender", "warn", "strict", "checkInvalidWriteDuringRender", "addCompilerSafeGetAndSet", "mutable", "Object", "defineProperties", "get", "value", "configurable", "enumerable", "set", "newValue", "hideInternalValueProp", "defineProperty", "makeMutableUI", "initial", "listeners", "Map", "_value", "for<PERSON>ach", "listener", "modify", "modifier", "forceUpdate", "undefined", "addListener", "id", "removeListener", "delete", "_animation", "_isReanimatedSharedValue", "makeMutableNative", "handle", "__init", "uiValueGetter", "sv", "_newValue", "makeMutableWeb", "makeMutable"], "sourceRoot": "../../src", "sources": ["mutables.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,cAAc,QAAQ,sBAAmB;AAElD,SAASC,eAAe,QAAQ,aAAU;AAC1C,SAASC,MAAM,QAAQ,mBAAU;AACjC,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,iBAAc;AACnE,SAASC,qBAAqB,QAAQ,4BAAyB;AAC/D,SAASC,2BAA2B,QAAQ,iBAAc;AAC1D,SAASC,sBAAsB,EAAEC,OAAO,QAAQ,cAAW;AAC3D,SAASC,WAAW,QAAQ,kBAAe;AAE3C,MAAMC,iBAAiB,GAAGV,cAAc,CAAC,CAAC;AAE1C,SAASW,iCAAiCA,CAAA,EAAG;EAC3C,OAAOC,OAAO,IAAIR,gBAAgB,CAAC,CAAC,IAAI,CAACD,kBAAkB,CAAC,CAAC;AAC/D;AAEA,SAASU,4BAA4BA,CAAA,EAAG;EACtC,IAAIF,iCAAiC,CAAC,CAAC,EAAE;IACvCT,MAAM,CAACY,IAAI,CACT,qLAAqL,EACrL;MAAEC,MAAM,EAAE;IAAK,CACjB,CAAC;EACH;AACF;AAEA,SAASC,6BAA6BA,CAAA,EAAG;EACvC,IAAIL,iCAAiC,CAAC,CAAC,EAAE;IACvCT,MAAM,CAACY,IAAI,CACT,mLAAmL,EACnL;MAAEC,MAAM,EAAE;IAAK,CACjB,CAAC;EACH;AACF;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAAQC,OAA8B,EAAQ;EAC7E,SAAS;;EACTC,MAAM,CAACC,gBAAgB,CAACF,OAAO,EAAE;IAC/BG,GAAG,EAAE;MACHC,KAAKA,CAAA,EAAG;QACN,OAAOJ,OAAO,CAACI,KAAK;MACtB,CAAC;MACDC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;IACd,CAAC;IACDC,GAAG,EAAE;MACHH,KAAKA,CAACI,QAA2C,EAAE;QACjD,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClCR,OAAO,CAACI,KAAK,GAAII,QAAQ,CAA6BR,OAAO,CAACI,KAAK,CAAC;QACtE,CAAC,MAAM;UACLJ,OAAO,CAACI,KAAK,GAAGI,QAAQ;QAC1B;MACF,CAAC;MACDH,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,qBAAqBA,CAAQT,OAA8B,EAAE;EACpE,SAAS;;EACTC,MAAM,CAACS,cAAc,CAACV,OAAO,EAAE,QAAQ,EAAE;IACvCK,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AAEA,OAAO,SAASK,aAAaA,CAAQC,OAAc,EAAkB;EACnE,SAAS;;EACT,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EACpD,IAAIV,KAAK,GAAGQ,OAAO;EAEnB,MAAMZ,OAA8B,GAAG;IACrC,IAAII,KAAKA,CAAA,EAAG;MACV,OAAOA,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAACI,QAAQ,EAAE;MAClBjB,WAAW,CAACS,OAAO,EAAoBQ,QAAQ,CAAC;IAClD,CAAC;IACD,IAAIO,MAAMA,CAAA,EAAU;MAClB,OAAOX,KAAK;IACd,CAAC;IACD,IAAIW,MAAMA,CAACP,QAAe,EAAE;MAC1BJ,KAAK,GAAGI,QAAQ;MAChBK,SAAS,CAACG,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACT,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IACDU,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxC7B,WAAW,CACTS,OAAO,EACPmB,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,CAACf,KAAK,CAAC,GAAGA,KAAK,EAChDgB,WACF,CAAC;IACH,CAAC;IACDE,WAAW,EAAEA,CAACC,EAAU,EAAEN,QAAyB,KAAK;MACtDJ,SAAS,CAACN,GAAG,CAACgB,EAAE,EAAEN,QAAQ,CAAC;IAC7B,CAAC;IACDO,cAAc,EAAGD,EAAU,IAAK;MAC9BV,SAAS,CAACY,MAAM,CAACF,EAAE,CAAC;IACtB,CAAC;IAEDG,UAAU,EAAE,IAAI;IAChBC,wBAAwB,EAAE;EAC5B,CAAC;EAEDlB,qBAAqB,CAACT,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjC,OAAOA,OAAO;AAChB;AAEA,SAAS4B,iBAAiBA,CAAQhB,OAAc,EAAkB;EAChE,MAAMiB,MAAM,GAAGzC,2BAA2B,CAAC;IACzC0C,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOnB,aAAa,CAACC,OAAO,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,MAAMZ,OAA8B,GAAG;IACrC,IAAII,KAAKA,CAAA,EAAU;MACjBT,4BAA4B,CAAC,CAAC;MAC9B,MAAMoC,aAAa,GAAG1C,sBAAsB,CAAE2C,EAAkB,IAAK;QACnE,OAAOA,EAAE,CAAC5B,KAAK;MACjB,CAAC,CAAC;MACF,OAAO2B,aAAa,CAAC/B,OAAyB,CAAC;IACjD,CAAC;IACD,IAAII,KAAKA,CAACI,QAAQ,EAAE;MAClBV,6BAA6B,CAAC,CAAC;MAC/BR,OAAO,CAAC,MAAM;QACZU,OAAO,CAACI,KAAK,GAAGI,QAAQ;MAC1B,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAIO,MAAMA,CAAA,EAAU;MAClB,MAAM,IAAIhC,eAAe,CACvB,sIACF,CAAC;IACH,CAAC;IACD,IAAIgC,MAAMA,CAACkB,SAAgB,EAAE;MAC3B,MAAM,IAAIlD,eAAe,CACvB,8GACF,CAAC;IACH,CAAC;IAEDmC,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxC9B,OAAO,CAAC,MAAM;QACZU,OAAO,CAACkB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IACDE,WAAW,EAAEA,CAAA,KAAM;MACjB,MAAM,IAAIvC,eAAe,CACvB,sDACF,CAAC;IACH,CAAC;IACDyC,cAAc,EAAEA,CAAA,KAAM;MACpB,MAAM,IAAIzC,eAAe,CACvB,wDACF,CAAC;IACH,CAAC;IAED4C,wBAAwB,EAAE;EAC5B,CAAC;EAEDlB,qBAAqB,CAACT,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjCb,qBAAqB,CAACoB,GAAG,CAACP,OAAO,EAAE6B,MAAM,CAAC;EAC1C,OAAO7B,OAAO;AAChB;AAEA,SAASkC,cAAcA,CAAQtB,OAAc,EAAkB;EAC7D,IAAIR,KAAY,GAAGQ,OAAO;EAC1B,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EAEpD,MAAMd,OAA8B,GAAG;IACrC,IAAII,KAAKA,CAAA,EAAU;MACjBT,4BAA4B,CAAC,CAAC;MAC9B,OAAOS,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAACI,QAAQ,EAAE;MAClBV,6BAA6B,CAAC,CAAC;MAC/BP,WAAW,CAACS,OAAO,EAAoBQ,QAAQ,CAAC;IAClD,CAAC;IAED,IAAIO,MAAMA,CAAA,EAAU;MAClB,OAAOX,KAAK;IACd,CAAC;IACD,IAAIW,MAAMA,CAACP,QAAe,EAAE;MAC1BJ,KAAK,GAAGI,QAAQ;MAChBK,SAAS,CAACG,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACT,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IAEDU,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxC7B,WAAW,CACTS,OAAO,EACPmB,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,CAACnB,OAAO,CAACI,KAAK,CAAC,GAAGJ,OAAO,CAACI,KAAK,EAChEgB,WACF,CAAC;IACH,CAAC;IACDE,WAAW,EAAEA,CAACC,EAAU,EAAEN,QAAyB,KAAK;MACtDJ,SAAS,CAACN,GAAG,CAACgB,EAAE,EAAEN,QAAQ,CAAC;IAC7B,CAAC;IACDO,cAAc,EAAGD,EAAU,IAAK;MAC9BV,SAAS,CAACY,MAAM,CAACF,EAAE,CAAC;IACtB,CAAC;IAEDI,wBAAwB,EAAE;EAC5B,CAAC;EAEDlB,qBAAqB,CAACT,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjC,OAAOA,OAAO;AAChB;AAEA,OAAO,MAAMmC,WAAW,GAAG3C,iBAAiB,GACxC0C,cAAc,GACdN,iBAAiB", "ignoreList": []}