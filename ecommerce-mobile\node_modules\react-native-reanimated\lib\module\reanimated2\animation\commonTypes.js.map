{"version": 3, "names": [], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\nimport type {\n  StyleProps,\n  AnimatableValue,\n  AnimationObject,\n  Animation,\n  Timestamp,\n  AnimationCallback,\n} from '../commonTypes';\nimport type { AnimatedStyle } from '../helperTypes';\n\nexport interface HigherOrderAnimation {\n  isHigherOrder?: boolean;\n}\n\nexport type NextAnimation<T extends AnimationObject> = T | (() => T);\n\nexport interface ClampAnimation\n  extends Animation<ClampAnimation>,\n    HigherOrderAnimation {\n  current: AnimatableValue;\n}\n\nexport interface DelayAnimation\n  extends Animation<DelayAnimation>,\n    HigherOrderAnimation {\n  startTime: Timestamp;\n  started: boolean;\n  previousAnimation: DelayAnimation | null;\n  current: AnimatableValue;\n}\n\nexport interface RepeatAnimation\n  extends Animation<RepeatAnimation>,\n    HigherOrderAnimation {\n  reps: number;\n  startValue: AnimatableValue;\n  toValue?: AnimatableValue;\n  previousAnimation?: RepeatAnimation;\n}\n\nexport interface SequenceAnimation\n  extends Animation<SequenceAnimation>,\n    HigherOrderAnimation {\n  animationIndex: number;\n}\n\nexport interface StyleLayoutAnimation extends HigherOrderAnimation {\n  current: StyleProps;\n  styleAnimations: AnimatedStyle<any>;\n  onFrame: (animation: StyleLayoutAnimation, timestamp: Timestamp) => boolean;\n  onStart: (\n    nextAnimation: StyleLayoutAnimation,\n    current: AnimatedStyle<any>,\n    timestamp: Timestamp,\n    previousAnimation: StyleLayoutAnimation\n  ) => void;\n  callback?: AnimationCallback;\n}\n"], "mappings": "AAAA,YAAY;;AAAC", "ignoreList": []}