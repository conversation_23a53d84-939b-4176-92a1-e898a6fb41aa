{"version": 3, "names": ["makeShareable", "isAndroid", "isWeb", "NUMBER", "PERCENTAGE", "call", "args", "join", "MATCHERS", "rgb", "RegExp", "rgba", "hsl", "hsla", "hex3", "hex4", "hex6", "hex8", "hue2rgb", "p", "q", "t", "hslToRgb", "h", "s", "l", "r", "g", "b", "Math", "round", "parse255", "str", "int", "Number", "parseInt", "parse360", "parseFloat", "parse1", "num", "parsePercentage", "names", "transparent", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "<PERSON><PERSON><PERSON>", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "ColorProperties", "normalizeColor", "color", "match", "exec", "undefined", "opacity", "c", "IS_WEB", "IS_ANDROID", "rgbaColor", "alpha", "_WORKLET", "RGBtoHSV", "max", "min", "d", "v", "HSVtoRGB", "i", "floor", "f", "hsvToColor", "a", "processColorInitially", "normalizedColor", "isColor", "value", "processColor", "processColorsInProps", "props", "key", "includes", "convertToRGBA", "processedColor", "rgbaArrayToRGBAColor", "RGBA", "toLinearSpace", "gamma", "res", "push", "pow", "toGammaSpace"], "sources": ["Colors.ts"], "sourcesContent": ["'use strict';\n/**\n * Copied from:\n * react-native/Libraries/StyleSheet/normalizeColor.js\n * react-native/Libraries/StyleSheet/processColor.js\n * https://github.com/wcandillon/react-native-redash/blob/master/src/Colors.ts\n */\n\n/* eslint no-bitwise: 0 */\nimport type { StyleProps } from './commonTypes';\nimport { makeShareable } from './core';\nimport { isAndroid, isWeb } from './PlatformChecker';\n\ninterface RGB {\n  r: number;\n  g: number;\n  b: number;\n}\n\ninterface HSV {\n  h: number;\n  s: number;\n  v: number;\n}\n\n// var INTEGER = '[-+]?\\\\d+';\nconst NUMBER = '[-+]?(?:\\\\d+(?:\\\\.\\\\d*)?|\\\\.\\\\d+)';\nconst PERCENTAGE = NUMBER + '%';\n\nfunction call(...args: unknown[]): string {\n  'worklet';\n  return '\\\\(\\\\s*(' + args.join(')\\\\s*,\\\\s*(') + ')\\\\s*\\\\)';\n}\n\nconst MATCHERS = {\n  rgb: new RegExp('rgb' + call(NUMBER, NUMBER, NUMBER)),\n  rgba: new RegExp('rgba' + call(NUMBER, NUMBER, NUMBER, NUMBER)),\n  hsl: new RegExp('hsl' + call(NUMBER, PERCENTAGE, PERCENTAGE)),\n  hsla: new RegExp('hsla' + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)),\n  hex3: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex4: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex6: /^#([0-9a-fA-F]{6})$/,\n  hex8: /^#([0-9a-fA-F]{8})$/,\n};\n\nfunction hue2rgb(p: number, q: number, t: number): number {\n  'worklet';\n  if (t < 0) {\n    t += 1;\n  }\n  if (t > 1) {\n    t -= 1;\n  }\n  if (t < 1 / 6) {\n    return p + (q - p) * 6 * t;\n  }\n  if (t < 1 / 2) {\n    return q;\n  }\n  if (t < 2 / 3) {\n    return p + (q - p) * (2 / 3 - t) * 6;\n  }\n  return p;\n}\n\nfunction hslToRgb(h: number, s: number, l: number): number {\n  'worklet';\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n\n  return (\n    (Math.round(r * 255) << 24) |\n    (Math.round(g * 255) << 16) |\n    (Math.round(b * 255) << 8)\n  );\n}\n\nfunction parse255(str: string): number {\n  'worklet';\n  const int = Number.parseInt(str, 10);\n  if (int < 0) {\n    return 0;\n  }\n  if (int > 255) {\n    return 255;\n  }\n  return int;\n}\n\nfunction parse360(str: string): number {\n  'worklet';\n  const int = Number.parseFloat(str);\n  return (((int % 360) + 360) % 360) / 360;\n}\n\nfunction parse1(str: string): number {\n  'worklet';\n  const num = Number.parseFloat(str);\n  if (num < 0) {\n    return 0;\n  }\n  if (num > 1) {\n    return 255;\n  }\n  return Math.round(num * 255);\n}\n\nfunction parsePercentage(str: string): number {\n  'worklet';\n  // parseFloat conveniently ignores the final %\n  const int = Number.parseFloat(str);\n  if (int < 0) {\n    return 0;\n  }\n  if (int > 100) {\n    return 1;\n  }\n  return int / 100;\n}\n\nconst names: Record<string, number> = makeShareable({\n  transparent: 0x00000000,\n\n  // http://www.w3.org/TR/css3-color/#svg-color\n  aliceblue: 0xf0f8ffff,\n  antiquewhite: 0xfaebd7ff,\n  aqua: 0x00ffffff,\n  aquamarine: 0x7fffd4ff,\n  azure: 0xf0ffffff,\n  beige: 0xf5f5dcff,\n  bisque: 0xffe4c4ff,\n  black: 0x000000ff,\n  blanchedalmond: 0xffebcdff,\n  blue: 0x0000ffff,\n  blueviolet: 0x8a2be2ff,\n  brown: 0xa52a2aff,\n  burlywood: 0xdeb887ff,\n  burntsienna: 0xea7e5dff,\n  cadetblue: 0x5f9ea0ff,\n  chartreuse: 0x7fff00ff,\n  chocolate: 0xd2691eff,\n  coral: 0xff7f50ff,\n  cornflowerblue: 0x6495edff,\n  cornsilk: 0xfff8dcff,\n  crimson: 0xdc143cff,\n  cyan: 0x00ffffff,\n  darkblue: 0x00008bff,\n  darkcyan: 0x008b8bff,\n  darkgoldenrod: 0xb8860bff,\n  darkgray: 0xa9a9a9ff,\n  darkgreen: 0x006400ff,\n  darkgrey: 0xa9a9a9ff,\n  darkkhaki: 0xbdb76bff,\n  darkmagenta: 0x8b008bff,\n  darkolivegreen: 0x556b2fff,\n  darkorange: 0xff8c00ff,\n  darkorchid: 0x9932ccff,\n  darkred: 0x8b0000ff,\n  darksalmon: 0xe9967aff,\n  darkseagreen: 0x8fbc8fff,\n  darkslateblue: 0x483d8bff,\n  darkslategray: 0x2f4f4fff,\n  darkslategrey: 0x2f4f4fff,\n  darkturquoise: 0x00ced1ff,\n  darkviolet: 0x9400d3ff,\n  deeppink: 0xff1493ff,\n  deepskyblue: 0x00bfffff,\n  dimgray: 0x696969ff,\n  dimgrey: 0x696969ff,\n  dodgerblue: 0x1e90ffff,\n  firebrick: 0xb22222ff,\n  floralwhite: 0xfffaf0ff,\n  forestgreen: 0x228b22ff,\n  fuchsia: 0xff00ffff,\n  gainsboro: 0xdcdcdcff,\n  ghostwhite: 0xf8f8ffff,\n  gold: 0xffd700ff,\n  goldenrod: 0xdaa520ff,\n  gray: 0x808080ff,\n  green: 0x008000ff,\n  greenyellow: 0xadff2fff,\n  grey: 0x808080ff,\n  honeydew: 0xf0fff0ff,\n  hotpink: 0xff69b4ff,\n  indianred: 0xcd5c5cff,\n  indigo: 0x4b0082ff,\n  ivory: 0xfffff0ff,\n  khaki: 0xf0e68cff,\n  lavender: 0xe6e6faff,\n  lavenderblush: 0xfff0f5ff,\n  lawngreen: 0x7cfc00ff,\n  lemonchiffon: 0xfffacdff,\n  lightblue: 0xadd8e6ff,\n  lightcoral: 0xf08080ff,\n  lightcyan: 0xe0ffffff,\n  lightgoldenrodyellow: 0xfafad2ff,\n  lightgray: 0xd3d3d3ff,\n  lightgreen: 0x90ee90ff,\n  lightgrey: 0xd3d3d3ff,\n  lightpink: 0xffb6c1ff,\n  lightsalmon: 0xffa07aff,\n  lightseagreen: 0x20b2aaff,\n  lightskyblue: 0x87cefaff,\n  lightslategray: 0x778899ff,\n  lightslategrey: 0x778899ff,\n  lightsteelblue: 0xb0c4deff,\n  lightyellow: 0xffffe0ff,\n  lime: 0x00ff00ff,\n  limegreen: 0x32cd32ff,\n  linen: 0xfaf0e6ff,\n  magenta: 0xff00ffff,\n  maroon: 0x800000ff,\n  mediumaquamarine: 0x66cdaaff,\n  mediumblue: 0x0000cdff,\n  mediumorchid: 0xba55d3ff,\n  mediumpurple: 0x9370dbff,\n  mediumseagreen: 0x3cb371ff,\n  mediumslateblue: 0x7b68eeff,\n  mediumspringgreen: 0x00fa9aff,\n  mediumturquoise: 0x48d1ccff,\n  mediumvioletred: 0xc71585ff,\n  midnightblue: 0x191970ff,\n  mintcream: 0xf5fffaff,\n  mistyrose: 0xffe4e1ff,\n  moccasin: 0xffe4b5ff,\n  navajowhite: 0xffdeadff,\n  navy: 0x000080ff,\n  oldlace: 0xfdf5e6ff,\n  olive: 0x808000ff,\n  olivedrab: 0x6b8e23ff,\n  orange: 0xffa500ff,\n  orangered: 0xff4500ff,\n  orchid: 0xda70d6ff,\n  palegoldenrod: 0xeee8aaff,\n  palegreen: 0x98fb98ff,\n  paleturquoise: 0xafeeeeff,\n  palevioletred: 0xdb7093ff,\n  papayawhip: 0xffefd5ff,\n  peachpuff: 0xffdab9ff,\n  peru: 0xcd853fff,\n  pink: 0xffc0cbff,\n  plum: 0xdda0ddff,\n  powderblue: 0xb0e0e6ff,\n  purple: 0x800080ff,\n  rebeccapurple: 0x663399ff,\n  red: 0xff0000ff,\n  rosybrown: 0xbc8f8fff,\n  royalblue: 0x4169e1ff,\n  saddlebrown: 0x8b4513ff,\n  salmon: 0xfa8072ff,\n  sandybrown: 0xf4a460ff,\n  seagreen: 0x2e8b57ff,\n  seashell: 0xfff5eeff,\n  sienna: 0xa0522dff,\n  silver: 0xc0c0c0ff,\n  skyblue: 0x87ceebff,\n  slateblue: 0x6a5acdff,\n  slategray: 0x708090ff,\n  slategrey: 0x708090ff,\n  snow: 0xfffafaff,\n  springgreen: 0x00ff7fff,\n  steelblue: 0x4682b4ff,\n  tan: 0xd2b48cff,\n  teal: 0x008080ff,\n  thistle: 0xd8bfd8ff,\n  tomato: 0xff6347ff,\n  turquoise: 0x40e0d0ff,\n  violet: 0xee82eeff,\n  wheat: 0xf5deb3ff,\n  white: 0xffffffff,\n  whitesmoke: 0xf5f5f5ff,\n  yellow: 0xffff00ff,\n  yellowgreen: 0x9acd32ff,\n});\n\n// copied from react-native/Libraries/Components/View/ReactNativeStyleAttributes\nexport const ColorProperties = makeShareable([\n  'backgroundColor',\n  'borderBottomColor',\n  'borderColor',\n  'borderLeftColor',\n  'borderRightColor',\n  'borderTopColor',\n  'borderStartColor',\n  'borderEndColor',\n  'borderBlockColor',\n  'borderBlockEndColor',\n  'borderBlockStartColor',\n  'color',\n  'shadowColor',\n  'textDecorationColor',\n  'tintColor',\n  'textShadowColor',\n  'overlayColor',\n]);\n\nfunction normalizeColor(color: unknown): number | null {\n  'worklet';\n\n  if (typeof color === 'number') {\n    if (color >>> 0 === color && color >= 0 && color <= 0xffffffff) {\n      return color;\n    }\n    return null;\n  }\n\n  if (typeof color !== 'string') {\n    return null;\n  }\n\n  let match: RegExpExecArray | null | undefined;\n\n  // Ordered based on occurrences on Facebook codebase\n  if ((match = MATCHERS.hex6.exec(color))) {\n    return Number.parseInt(match[1] + 'ff', 16) >>> 0;\n  }\n\n  if (names[color] !== undefined) {\n    return names[color];\n  }\n\n  if ((match = MATCHERS.rgb.exec(color))) {\n    return (\n      // b\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) |\n        0x000000ff) >>> // a\n      0\n    );\n  }\n\n  if ((match = MATCHERS.rgba.exec(color))) {\n    return (\n      // b\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) |\n        parse1(match[4])) >>> // a\n      0\n    );\n  }\n\n  if ((match = MATCHERS.hex3.exec(color))) {\n    return (\n      Number.parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          'ff', // a\n        16\n      ) >>> 0\n    );\n  }\n\n  // https://drafts.csswg.org/css-color-4/#hex-notation\n  if ((match = MATCHERS.hex8.exec(color))) {\n    return Number.parseInt(match[1], 16) >>> 0;\n  }\n\n  if ((match = MATCHERS.hex4.exec(color))) {\n    return (\n      Number.parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          match[4] +\n          match[4], // a\n        16\n      ) >>> 0\n    );\n  }\n\n  if ((match = MATCHERS.hsl.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        0x000000ff) >>> // a\n      0\n    );\n  }\n\n  if ((match = MATCHERS.hsla.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        parse1(match[4])) >>> // a\n      0\n    );\n  }\n\n  return null;\n}\n\nexport const opacity = (c: number): number => {\n  'worklet';\n  return ((c >> 24) & 255) / 255;\n};\n\nexport const red = (c: number): number => {\n  'worklet';\n  return (c >> 16) & 255;\n};\n\nexport const green = (c: number): number => {\n  'worklet';\n  return (c >> 8) & 255;\n};\n\nexport const blue = (c: number): number => {\n  'worklet';\n  return c & 255;\n};\n\nconst IS_WEB = isWeb();\nconst IS_ANDROID = isAndroid();\n\nexport const rgbaColor = (\n  r: number,\n  g: number,\n  b: number,\n  alpha = 1\n): number | string => {\n  'worklet';\n  if (IS_WEB || !_WORKLET) {\n    return `rgba(${r}, ${g}, ${b}, ${alpha})`;\n  }\n\n  const c =\n    Math.round(alpha * 255) * (1 << 24) +\n    Math.round(r) * (1 << 16) +\n    Math.round(g) * (1 << 8) +\n    Math.round(b);\n  if (IS_ANDROID) {\n    // on Android color is represented as signed 32 bit int\n    return c < (1 << 31) >>> 0 ? c : c - 4294967296; // 4294967296 == Math.pow(2, 32);\n  }\n  return c;\n};\n\n/**\n *\n * @param r - red value (0-255)\n * @param g - green value (0-255)\n * @param b - blue value (0-255)\n * @returns \\{h: hue (0-1), s: saturation (0-1), v: value (0-1)\\}\n */\nexport function RGBtoHSV(r: number, g: number, b: number): HSV {\n  'worklet';\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const d = max - min;\n  const s = max === 0 ? 0 : d / max;\n  const v = max / 255;\n\n  let h = 0;\n\n  switch (max) {\n    case min:\n      break;\n    case r:\n      h = g - b + d * (g < b ? 6 : 0);\n      h /= 6 * d;\n      break;\n    case g:\n      h = b - r + d * 2;\n      h /= 6 * d;\n      break;\n    case b:\n      h = r - g + d * 4;\n      h /= 6 * d;\n      break;\n  }\n\n  return { h, s, v };\n}\n\n/**\n *\n * @param h - hue (0-1)\n * @param s - saturation (0-1)\n * @param v - value (0-1)\n * @returns \\{r: red (0-255), g: green (0-255), b: blue (0-255)\\}\n */\nfunction HSVtoRGB(h: number, s: number, v: number): RGB {\n  'worklet';\n  let r, g, b;\n\n  const i = Math.floor(h * 6);\n  const f = h * 6 - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n  switch ((i % 6) as 0 | 1 | 2 | 3 | 4 | 5) {\n    case 0:\n      [r, g, b] = [v, t, p];\n      break;\n    case 1:\n      [r, g, b] = [q, v, p];\n      break;\n    case 2:\n      [r, g, b] = [p, v, t];\n      break;\n    case 3:\n      [r, g, b] = [p, q, v];\n      break;\n    case 4:\n      [r, g, b] = [t, p, v];\n      break;\n    case 5:\n      [r, g, b] = [v, p, q];\n      break;\n  }\n  return {\n    r: Math.round(r * 255),\n    g: Math.round(g * 255),\n    b: Math.round(b * 255),\n  };\n}\n\nexport const hsvToColor = (\n  h: number,\n  s: number,\n  v: number,\n  a: number\n): number | string => {\n  'worklet';\n  const { r, g, b } = HSVtoRGB(h, s, v);\n  return rgbaColor(r, g, b, a);\n};\n\nfunction processColorInitially(color: unknown): number | null | undefined {\n  'worklet';\n  if (color === null || color === undefined || typeof color === 'number') {\n    return color;\n  }\n\n  let normalizedColor = normalizeColor(color);\n\n  if (normalizedColor === null || normalizedColor === undefined) {\n    return undefined;\n  }\n\n  if (typeof normalizedColor !== 'number') {\n    return null;\n  }\n\n  normalizedColor = ((normalizedColor << 24) | (normalizedColor >>> 8)) >>> 0; // argb\n  return normalizedColor;\n}\n\nexport function isColor(value: unknown): boolean {\n  'worklet';\n  if (typeof value !== 'string') {\n    return false;\n  }\n  return processColorInitially(value) != null;\n}\n\nexport function processColor(color: unknown): number | null | undefined {\n  'worklet';\n  let normalizedColor = processColorInitially(color);\n  if (normalizedColor === null || normalizedColor === undefined) {\n    return undefined;\n  }\n\n  if (typeof normalizedColor !== 'number') {\n    return null;\n  }\n\n  if (IS_ANDROID) {\n    // Android use 32 bit *signed* integer to represent the color\n    // We utilize the fact that bitwise operations in JS also operates on\n    // signed 32 bit integers, so that we can use those to convert from\n    // *unsigned* to *signed* 32bit int that way.\n    normalizedColor = normalizedColor | 0x0;\n  }\n\n  return normalizedColor;\n}\n\nexport function processColorsInProps(props: StyleProps) {\n  'worklet';\n  for (const key in props) {\n    if (ColorProperties.includes(key)) {\n      props[key] = processColor(props[key]);\n    }\n  }\n}\n\nexport type ParsedColorArray = [number, number, number, number];\n\nexport function convertToRGBA(color: unknown): ParsedColorArray {\n  'worklet';\n  const processedColor = processColorInitially(color)!; // argb;\n  const a = (processedColor >>> 24) / 255;\n  const r = ((processedColor << 8) >>> 24) / 255;\n  const g = ((processedColor << 16) >>> 24) / 255;\n  const b = ((processedColor << 24) >>> 24) / 255;\n  return [r, g, b, a];\n}\n\nexport function rgbaArrayToRGBAColor(RGBA: ParsedColorArray): string {\n  'worklet';\n  return `rgba(${Math.round(RGBA[0] * 255)}, ${Math.round(\n    RGBA[1] * 255\n  )}, ${Math.round(RGBA[2] * 255)}, ${RGBA[3]})`;\n}\n\nexport function toLinearSpace(\n  RGBA: ParsedColorArray,\n  gamma = 2.2\n): ParsedColorArray {\n  'worklet';\n  const res = [];\n  for (let i = 0; i < 3; ++i) {\n    res.push(Math.pow(RGBA[i], gamma));\n  }\n  res.push(RGBA[3]);\n  return res as ParsedColorArray;\n}\n\nexport function toGammaSpace(\n  RGBA: ParsedColorArray,\n  gamma = 2.2\n): ParsedColorArray {\n  'worklet';\n  const res = [];\n  for (let i = 0; i < 3; ++i) {\n    res.push(Math.pow(RGBA[i], 1 / gamma));\n  }\n  res.push(RGBA[3]);\n  return res as ParsedColorArray;\n}\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA,SAASA,aAAa,QAAQ,QAAQ;AACtC,SAASC,SAAS,EAAEC,KAAK,QAAQ,mBAAmB;AAcpD;AACA,MAAMC,MAAM,GAAG,mCAAmC;AAClD,MAAMC,UAAU,GAAGD,MAAM,GAAG,GAAG;AAE/B,SAASE,IAAIA,CAAC,GAAGC,IAAe,EAAU;EACxC,SAAS;;EACT,OAAO,UAAU,GAAGA,IAAI,CAACC,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU;AAC3D;AAEA,MAAMC,QAAQ,GAAG;EACfC,GAAG,EAAE,IAAIC,MAAM,CAAC,KAAK,GAAGL,IAAI,CAACF,MAAM,EAAEA,MAAM,EAAEA,MAAM,CAAC,CAAC;EACrDQ,IAAI,EAAE,IAAID,MAAM,CAAC,MAAM,GAAGL,IAAI,CAACF,MAAM,EAAEA,MAAM,EAAEA,MAAM,EAAEA,MAAM,CAAC,CAAC;EAC/DS,GAAG,EAAE,IAAIF,MAAM,CAAC,KAAK,GAAGL,IAAI,CAACF,MAAM,EAAEC,UAAU,EAAEA,UAAU,CAAC,CAAC;EAC7DS,IAAI,EAAE,IAAIH,MAAM,CAAC,MAAM,GAAGL,IAAI,CAACF,MAAM,EAAEC,UAAU,EAAEA,UAAU,EAAED,MAAM,CAAC,CAAC;EACvEW,IAAI,EAAE,qDAAqD;EAC3DC,IAAI,EAAE,qEAAqE;EAC3EC,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE;AACR,CAAC;AAED,SAASC,OAAOA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAU;EACxD,SAAS;;EACT,IAAIA,CAAC,GAAG,CAAC,EAAE;IACTA,CAAC,IAAI,CAAC;EACR;EACA,IAAIA,CAAC,GAAG,CAAC,EAAE;IACTA,CAAC,IAAI,CAAC;EACR;EACA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACb,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGE,CAAC;EAC5B;EACA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACb,OAAOD,CAAC;EACV;EACA,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACb,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;EACtC;EACA,OAAOF,CAAC;AACV;AAEA,SAASG,QAAQA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAU;EACzD,SAAS;;EACT,MAAML,CAAC,GAAGK,CAAC,GAAG,GAAG,GAAGA,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGD,CAAC;EAC/C,MAAML,CAAC,GAAG,CAAC,GAAGM,CAAC,GAAGL,CAAC;EACnB,MAAMM,CAAC,GAAGR,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClC,MAAMI,CAAC,GAAGT,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEG,CAAC,CAAC;EAC1B,MAAMK,CAAC,GAAGV,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAElC,OACGM,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,GACzBG,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,GAAG,CAAC,IAAI,EAAG,GAC1BE,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAI,CAAE;AAE9B;AAEA,SAASG,QAAQA,CAACC,GAAW,EAAU;EACrC,SAAS;;EACT,MAAMC,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACH,GAAG,EAAE,EAAE,CAAC;EACpC,IAAIC,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,CAAC;EACV;EACA,IAAIA,GAAG,GAAG,GAAG,EAAE;IACb,OAAO,GAAG;EACZ;EACA,OAAOA,GAAG;AACZ;AAEA,SAASG,QAAQA,CAACJ,GAAW,EAAU;EACrC,SAAS;;EACT,MAAMC,GAAG,GAAGC,MAAM,CAACG,UAAU,CAACL,GAAG,CAAC;EAClC,OAAQ,CAAEC,GAAG,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,GAAI,GAAG;AAC1C;AAEA,SAASK,MAAMA,CAACN,GAAW,EAAU;EACnC,SAAS;;EACT,MAAMO,GAAG,GAAGL,MAAM,CAACG,UAAU,CAACL,GAAG,CAAC;EAClC,IAAIO,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,CAAC;EACV;EACA,IAAIA,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,GAAG;EACZ;EACA,OAAOV,IAAI,CAACC,KAAK,CAACS,GAAG,GAAG,GAAG,CAAC;AAC9B;AAEA,SAASC,eAAeA,CAACR,GAAW,EAAU;EAC5C,SAAS;;EACT;EACA,MAAMC,GAAG,GAAGC,MAAM,CAACG,UAAU,CAACL,GAAG,CAAC;EAClC,IAAIC,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,CAAC;EACV;EACA,IAAIA,GAAG,GAAG,GAAG,EAAE;IACb,OAAO,CAAC;EACV;EACA,OAAOA,GAAG,GAAG,GAAG;AAClB;AAEA,MAAMQ,KAA6B,GAAGzC,aAAa,CAAC;EAClD0C,WAAW,EAAE,UAAU;EAEvB;EACAC,SAAS,EAAE,UAAU;EACrBC,YAAY,EAAE,UAAU;EACxBC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,UAAU;EACtBC,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,UAAU;EACjBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE,UAAU;EACjBC,cAAc,EAAE,UAAU;EAC1BC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,UAAU;EACtBC,KAAK,EAAE,UAAU;EACjBC,SAAS,EAAE,UAAU;EACrBC,WAAW,EAAE,UAAU;EACvBC,SAAS,EAAE,UAAU;EACrBC,UAAU,EAAE,UAAU;EACtBC,SAAS,EAAE,UAAU;EACrBC,KAAK,EAAE,UAAU;EACjBC,cAAc,EAAE,UAAU;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,UAAU;EACnBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,aAAa,EAAE,UAAU;EACzBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,WAAW,EAAE,UAAU;EACvBC,cAAc,EAAE,UAAU;EAC1BC,UAAU,EAAE,UAAU;EACtBC,UAAU,EAAE,UAAU;EACtBC,OAAO,EAAE,UAAU;EACnBC,UAAU,EAAE,UAAU;EACtBC,YAAY,EAAE,UAAU;EACxBC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAE,UAAU;EACzBC,UAAU,EAAE,UAAU;EACtBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,UAAU;EACvBC,OAAO,EAAE,UAAU;EACnBC,OAAO,EAAE,UAAU;EACnBC,UAAU,EAAE,UAAU;EACtBC,SAAS,EAAE,UAAU;EACrBC,WAAW,EAAE,UAAU;EACvBC,WAAW,EAAE,UAAU;EACvBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,UAAU;EACrBC,UAAU,EAAE,UAAU;EACtBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAE,UAAU;EACrBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,UAAU;EACjBC,WAAW,EAAE,UAAU;EACvBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,UAAU;EACpBC,aAAa,EAAE,UAAU;EACzBC,SAAS,EAAE,UAAU;EACrBC,YAAY,EAAE,UAAU;EACxBC,SAAS,EAAE,UAAU;EACrBC,UAAU,EAAE,UAAU;EACtBC,SAAS,EAAE,UAAU;EACrBC,oBAAoB,EAAE,UAAU;EAChCC,SAAS,EAAE,UAAU;EACrBC,UAAU,EAAE,UAAU;EACtBC,SAAS,EAAE,UAAU;EACrBC,SAAS,EAAE,UAAU;EACrBC,WAAW,EAAE,UAAU;EACvBC,aAAa,EAAE,UAAU;EACzBC,YAAY,EAAE,UAAU;EACxBC,cAAc,EAAE,UAAU;EAC1BC,cAAc,EAAE,UAAU;EAC1BC,cAAc,EAAE,UAAU;EAC1BC,WAAW,EAAE,UAAU;EACvBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAE,UAAU;EACrBC,KAAK,EAAE,UAAU;EACjBC,OAAO,EAAE,UAAU;EACnBC,MAAM,EAAE,UAAU;EAClBC,gBAAgB,EAAE,UAAU;EAC5BC,UAAU,EAAE,UAAU;EACtBC,YAAY,EAAE,UAAU;EACxBC,YAAY,EAAE,UAAU;EACxBC,cAAc,EAAE,UAAU;EAC1BC,eAAe,EAAE,UAAU;EAC3BC,iBAAiB,EAAE,UAAU;EAC7BC,eAAe,EAAE,UAAU;EAC3BC,eAAe,EAAE,UAAU;EAC3BC,YAAY,EAAE,UAAU;EACxBC,SAAS,EAAE,UAAU;EACrBC,SAAS,EAAE,UAAU;EACrBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,UAAU;EACvBC,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAE,UAAU;EACnBC,KAAK,EAAE,UAAU;EACjBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,UAAU;EAClBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,UAAU;EAClBC,aAAa,EAAE,UAAU;EACzBC,SAAS,EAAE,UAAU;EACrBC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAE,UAAU;EACzBC,UAAU,EAAE,UAAU;EACtBC,SAAS,EAAE,UAAU;EACrBC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,UAAU;EACtBC,MAAM,EAAE,UAAU;EAClBC,aAAa,EAAE,UAAU;EACzBC,GAAG,EAAE,UAAU;EACfC,SAAS,EAAE,UAAU;EACrBC,SAAS,EAAE,UAAU;EACrBC,WAAW,EAAE,UAAU;EACvBC,MAAM,EAAE,UAAU;EAClBC,UAAU,EAAE,UAAU;EACtBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,UAAU;EAClBC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,UAAU;EACrBC,SAAS,EAAE,UAAU;EACrBC,SAAS,EAAE,UAAU;EACrBC,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,UAAU;EACvBC,SAAS,EAAE,UAAU;EACrBC,GAAG,EAAE,UAAU;EACfC,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAE,UAAU;EACnBC,MAAM,EAAE,UAAU;EAClBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,UAAU;EACjBC,UAAU,EAAE,UAAU;EACtBC,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE;AACf,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,eAAe,GAAGhM,aAAa,CAAC,CAC3C,iBAAiB,EACjB,mBAAmB,EACnB,aAAa,EACb,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,kBAAkB,EAClB,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EACrB,uBAAuB,EACvB,OAAO,EACP,aAAa,EACb,qBAAqB,EACrB,WAAW,EACX,iBAAiB,EACjB,cAAc,CACf,CAAC;AAEF,SAASiM,cAAcA,CAACC,KAAc,EAAiB;EACrD,SAAS;;EAET,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIA,KAAK,KAAK,CAAC,KAAKA,KAAK,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,UAAU,EAAE;MAC9D,OAAOA,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,IAAI;EACb;EAEA,IAAIC,KAAyC;;EAE7C;EACA,IAAKA,KAAK,GAAG3L,QAAQ,CAACQ,IAAI,CAACoL,IAAI,CAACF,KAAK,CAAC,EAAG;IACvC,OAAOhK,MAAM,CAACC,QAAQ,CAACgK,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC;EACnD;EAEA,IAAI1J,KAAK,CAACyJ,KAAK,CAAC,KAAKG,SAAS,EAAE;IAC9B,OAAO5J,KAAK,CAACyJ,KAAK,CAAC;EACrB;EAEA,IAAKC,KAAK,GAAG3L,QAAQ,CAACC,GAAG,CAAC2L,IAAI,CAACF,KAAK,CAAC,EAAG;IACtC;MACE;MACA,CAAEnK,QAAQ,CAACoK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;MAAI;MAC3BpK,QAAQ,CAACoK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG;MAAG;MAC5BpK,QAAQ,CAACoK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,GACzB,UAAU;MAAM;MAClB;IAAC;EAEL;EAEA,IAAKA,KAAK,GAAG3L,QAAQ,CAACG,IAAI,CAACyL,IAAI,CAACF,KAAK,CAAC,EAAG;IACvC;MACE;MACA,CAAEnK,QAAQ,CAACoK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;MAAI;MAC3BpK,QAAQ,CAACoK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG;MAAG;MAC5BpK,QAAQ,CAACoK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,GACzB7J,MAAM,CAAC6J,KAAK,CAAC,CAAC,CAAC,CAAC;MAAM;MACxB;IAAC;EAEL;EAEA,IAAKA,KAAK,GAAG3L,QAAQ,CAACM,IAAI,CAACsL,IAAI,CAACF,KAAK,CAAC,EAAG;IACvC,OACEhK,MAAM,CAACC,QAAQ,CACbgK,KAAK,CAAC,CAAC,CAAC,GACNA,KAAK,CAAC,CAAC,CAAC;IAAG;IACXA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;IAAG;IACXA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;IAAG;IACX,IAAI;IAAE;IACR,EACF,CAAC,KAAK,CAAC;EAEX;;EAEA;EACA,IAAKA,KAAK,GAAG3L,QAAQ,CAACS,IAAI,CAACmL,IAAI,CAACF,KAAK,CAAC,EAAG;IACvC,OAAOhK,MAAM,CAACC,QAAQ,CAACgK,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;EAC5C;EAEA,IAAKA,KAAK,GAAG3L,QAAQ,CAACO,IAAI,CAACqL,IAAI,CAACF,KAAK,CAAC,EAAG;IACvC,OACEhK,MAAM,CAACC,QAAQ,CACbgK,KAAK,CAAC,CAAC,CAAC,GACNA,KAAK,CAAC,CAAC,CAAC;IAAG;IACXA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;IAAG;IACXA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;IAAG;IACXA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;IAAE;IACZ,EACF,CAAC,KAAK,CAAC;EAEX;EAEA,IAAKA,KAAK,GAAG3L,QAAQ,CAACI,GAAG,CAACwL,IAAI,CAACF,KAAK,CAAC,EAAG;IACtC,OACE,CAAC5K,QAAQ,CACPc,QAAQ,CAAC+J,KAAK,CAAC,CAAC,CAAC,CAAC;IAAE;IACpB3J,eAAe,CAAC2J,KAAK,CAAC,CAAC,CAAC,CAAC;IAAE;IAC3B3J,eAAe,CAAC2J,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,GACC,UAAU;IAAM;IAClB,CAAC;EAEL;EAEA,IAAKA,KAAK,GAAG3L,QAAQ,CAACK,IAAI,CAACuL,IAAI,CAACF,KAAK,CAAC,EAAG;IACvC,OACE,CAAC5K,QAAQ,CACPc,QAAQ,CAAC+J,KAAK,CAAC,CAAC,CAAC,CAAC;IAAE;IACpB3J,eAAe,CAAC2J,KAAK,CAAC,CAAC,CAAC,CAAC;IAAE;IAC3B3J,eAAe,CAAC2J,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,GACC7J,MAAM,CAAC6J,KAAK,CAAC,CAAC,CAAC,CAAC;IAAM;IACxB,CAAC;EAEL;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,MAAMG,OAAO,GAAIC,CAAS,IAAa;EAC5C,SAAS;;EACT,OAAO,CAAEA,CAAC,IAAI,EAAE,GAAI,GAAG,IAAI,GAAG;AAChC,CAAC;AAED,OAAO,MAAMnC,GAAG,GAAImC,CAAS,IAAa;EACxC,SAAS;;EACT,OAAQA,CAAC,IAAI,EAAE,GAAI,GAAG;AACxB,CAAC;AAED,OAAO,MAAMrG,KAAK,GAAIqG,CAAS,IAAa;EAC1C,SAAS;;EACT,OAAQA,CAAC,IAAI,CAAC,GAAI,GAAG;AACvB,CAAC;AAED,OAAO,MAAMnJ,IAAI,GAAImJ,CAAS,IAAa;EACzC,SAAS;;EACT,OAAOA,CAAC,GAAG,GAAG;AAChB,CAAC;AAED,MAAMC,MAAM,GAAGtM,KAAK,CAAC,CAAC;AACtB,MAAMuM,UAAU,GAAGxM,SAAS,CAAC,CAAC;AAE9B,OAAO,MAAMyM,SAAS,GAAGA,CACvBhL,CAAS,EACTC,CAAS,EACTC,CAAS,EACT+K,KAAK,GAAG,CAAC,KACW;EACpB,SAAS;;EACT,IAAIH,MAAM,IAAI,CAACI,QAAQ,EAAE;IACvB,OAAQ,QAAOlL,CAAE,KAAIC,CAAE,KAAIC,CAAE,KAAI+K,KAAM,GAAE;EAC3C;EAEA,MAAMJ,CAAC,GACL1K,IAAI,CAACC,KAAK,CAAC6K,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GACnC9K,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GACzBG,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GACxBE,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;EACf,IAAI6K,UAAU,EAAE;IACd;IACA,OAAOF,CAAC,GAAI,CAAC,IAAI,EAAE,KAAM,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,UAAU,CAAC,CAAC;EACnD;EACA,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,QAAQA,CAACnL,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAO;EAC7D,SAAS;;EACT,MAAMkL,GAAG,GAAGjL,IAAI,CAACiL,GAAG,CAACpL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC7B,MAAMmL,GAAG,GAAGlL,IAAI,CAACkL,GAAG,CAACrL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC7B,MAAMoL,CAAC,GAAGF,GAAG,GAAGC,GAAG;EACnB,MAAMvL,CAAC,GAAGsL,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,GAAGF,GAAG;EACjC,MAAMG,CAAC,GAAGH,GAAG,GAAG,GAAG;EAEnB,IAAIvL,CAAC,GAAG,CAAC;EAET,QAAQuL,GAAG;IACT,KAAKC,GAAG;MACN;IACF,KAAKrL,CAAC;MACJH,CAAC,GAAGI,CAAC,GAAGC,CAAC,GAAGoL,CAAC,IAAIrL,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC/BL,CAAC,IAAI,CAAC,GAAGyL,CAAC;MACV;IACF,KAAKrL,CAAC;MACJJ,CAAC,GAAGK,CAAC,GAAGF,CAAC,GAAGsL,CAAC,GAAG,CAAC;MACjBzL,CAAC,IAAI,CAAC,GAAGyL,CAAC;MACV;IACF,KAAKpL,CAAC;MACJL,CAAC,GAAGG,CAAC,GAAGC,CAAC,GAAGqL,CAAC,GAAG,CAAC;MACjBzL,CAAC,IAAI,CAAC,GAAGyL,CAAC;MACV;EACJ;EAEA,OAAO;IAAEzL,CAAC;IAAEC,CAAC;IAAEyL;EAAE,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC3L,CAAS,EAAEC,CAAS,EAAEyL,CAAS,EAAO;EACtD,SAAS;;EACT,IAAIvL,CAAC,EAAEC,CAAC,EAAEC,CAAC;EAEX,MAAMuL,CAAC,GAAGtL,IAAI,CAACuL,KAAK,CAAC7L,CAAC,GAAG,CAAC,CAAC;EAC3B,MAAM8L,CAAC,GAAG9L,CAAC,GAAG,CAAC,GAAG4L,CAAC;EACnB,MAAMhM,CAAC,GAAG8L,CAAC,IAAI,CAAC,GAAGzL,CAAC,CAAC;EACrB,MAAMJ,CAAC,GAAG6L,CAAC,IAAI,CAAC,GAAGI,CAAC,GAAG7L,CAAC,CAAC;EACzB,MAAMH,CAAC,GAAG4L,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGI,CAAC,IAAI7L,CAAC,CAAC;EAC/B,QAAS2L,CAAC,GAAG,CAAC;IACZ,KAAK,CAAC;MACJ,CAACzL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACqL,CAAC,EAAE5L,CAAC,EAAEF,CAAC,CAAC;MACrB;IACF,KAAK,CAAC;MACJ,CAACO,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACR,CAAC,EAAE6L,CAAC,EAAE9L,CAAC,CAAC;MACrB;IACF,KAAK,CAAC;MACJ,CAACO,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACT,CAAC,EAAE8L,CAAC,EAAE5L,CAAC,CAAC;MACrB;IACF,KAAK,CAAC;MACJ,CAACK,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACT,CAAC,EAAEC,CAAC,EAAE6L,CAAC,CAAC;MACrB;IACF,KAAK,CAAC;MACJ,CAACvL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACP,CAAC,EAAEF,CAAC,EAAE8L,CAAC,CAAC;MACrB;IACF,KAAK,CAAC;MACJ,CAACvL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACqL,CAAC,EAAE9L,CAAC,EAAEC,CAAC,CAAC;MACrB;EACJ;EACA,OAAO;IACLM,CAAC,EAAEG,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAG,GAAG,CAAC;IACtBC,CAAC,EAAEE,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,GAAG,CAAC;IACtBC,CAAC,EAAEC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG;EACvB,CAAC;AACH;AAEA,OAAO,MAAM0L,UAAU,GAAGA,CACxB/L,CAAS,EACTC,CAAS,EACTyL,CAAS,EACTM,CAAS,KACW;EACpB,SAAS;;EACT,MAAM;IAAE7L,CAAC;IAAEC,CAAC;IAAEC;EAAE,CAAC,GAAGsL,QAAQ,CAAC3L,CAAC,EAAEC,CAAC,EAAEyL,CAAC,CAAC;EACrC,OAAOP,SAAS,CAAChL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE2L,CAAC,CAAC;AAC9B,CAAC;AAED,SAASC,qBAAqBA,CAACtB,KAAc,EAA6B;EACxE,SAAS;;EACT,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKG,SAAS,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IACtE,OAAOA,KAAK;EACd;EAEA,IAAIuB,eAAe,GAAGxB,cAAc,CAACC,KAAK,CAAC;EAE3C,IAAIuB,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKpB,SAAS,EAAE;IAC7D,OAAOA,SAAS;EAClB;EAEA,IAAI,OAAOoB,eAAe,KAAK,QAAQ,EAAE;IACvC,OAAO,IAAI;EACb;EAEAA,eAAe,GAAG,CAAEA,eAAe,IAAI,EAAE,GAAKA,eAAe,KAAK,CAAE,MAAM,CAAC,CAAC,CAAC;EAC7E,OAAOA,eAAe;AACxB;AAEA,OAAO,SAASC,OAAOA,CAACC,KAAc,EAAW;EAC/C,SAAS;;EACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,KAAK;EACd;EACA,OAAOH,qBAAqB,CAACG,KAAK,CAAC,IAAI,IAAI;AAC7C;AAEA,OAAO,SAASC,YAAYA,CAAC1B,KAAc,EAA6B;EACtE,SAAS;;EACT,IAAIuB,eAAe,GAAGD,qBAAqB,CAACtB,KAAK,CAAC;EAClD,IAAIuB,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKpB,SAAS,EAAE;IAC7D,OAAOA,SAAS;EAClB;EAEA,IAAI,OAAOoB,eAAe,KAAK,QAAQ,EAAE;IACvC,OAAO,IAAI;EACb;EAEA,IAAIhB,UAAU,EAAE;IACd;IACA;IACA;IACA;IACAgB,eAAe,GAAGA,eAAe,GAAG,GAAG;EACzC;EAEA,OAAOA,eAAe;AACxB;AAEA,OAAO,SAASI,oBAAoBA,CAACC,KAAiB,EAAE;EACtD,SAAS;;EACT,KAAK,MAAMC,GAAG,IAAID,KAAK,EAAE;IACvB,IAAI9B,eAAe,CAACgC,QAAQ,CAACD,GAAG,CAAC,EAAE;MACjCD,KAAK,CAACC,GAAG,CAAC,GAAGH,YAAY,CAACE,KAAK,CAACC,GAAG,CAAC,CAAC;IACvC;EACF;AACF;AAIA,OAAO,SAASE,aAAaA,CAAC/B,KAAc,EAAoB;EAC9D,SAAS;;EACT,MAAMgC,cAAc,GAAGV,qBAAqB,CAACtB,KAAK,CAAE,CAAC,CAAC;EACtD,MAAMqB,CAAC,GAAG,CAACW,cAAc,KAAK,EAAE,IAAI,GAAG;EACvC,MAAMxM,CAAC,GAAG,CAAEwM,cAAc,IAAI,CAAC,KAAM,EAAE,IAAI,GAAG;EAC9C,MAAMvM,CAAC,GAAG,CAAEuM,cAAc,IAAI,EAAE,KAAM,EAAE,IAAI,GAAG;EAC/C,MAAMtM,CAAC,GAAG,CAAEsM,cAAc,IAAI,EAAE,KAAM,EAAE,IAAI,GAAG;EAC/C,OAAO,CAACxM,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE2L,CAAC,CAAC;AACrB;AAEA,OAAO,SAASY,oBAAoBA,CAACC,IAAsB,EAAU;EACnE,SAAS;;EACT,OAAQ,QAAOvM,IAAI,CAACC,KAAK,CAACsM,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAE,KAAIvM,IAAI,CAACC,KAAK,CACrDsM,IAAI,CAAC,CAAC,CAAC,GAAG,GACZ,CAAE,KAAIvM,IAAI,CAACC,KAAK,CAACsM,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAE,KAAIA,IAAI,CAAC,CAAC,CAAE,GAAE;AAChD;AAEA,OAAO,SAASC,aAAaA,CAC3BD,IAAsB,EACtBE,KAAK,GAAG,GAAG,EACO;EAClB,SAAS;;EACT,MAAMC,GAAG,GAAG,EAAE;EACd,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1BoB,GAAG,CAACC,IAAI,CAAC3M,IAAI,CAAC4M,GAAG,CAACL,IAAI,CAACjB,CAAC,CAAC,EAAEmB,KAAK,CAAC,CAAC;EACpC;EACAC,GAAG,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;EACjB,OAAOG,GAAG;AACZ;AAEA,OAAO,SAASG,YAAYA,CAC1BN,IAAsB,EACtBE,KAAK,GAAG,GAAG,EACO;EAClB,SAAS;;EACT,MAAMC,GAAG,GAAG,EAAE;EACd,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1BoB,GAAG,CAACC,IAAI,CAAC3M,IAAI,CAAC4M,GAAG,CAACL,IAAI,CAACjB,CAAC,CAAC,EAAE,CAAC,GAAGmB,KAAK,CAAC,CAAC;EACxC;EACAC,GAAG,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;EACjB,OAAOG,GAAG;AACZ", "ignoreList": []}