{"version": 3, "names": ["NativeEventEmitter", "Platform", "findNodeHandle", "shouldBeUseWeb", "runOnJS", "runOnUIImmediately", "NativeReanimatedModule", "SHOULD_BE_USE_WEB", "JSPropsUpdaterPaper", "_tagToComponentMapping", "Map", "constructor", "_reanimatedEventEmitter", "OS", "undefined", "addOnJSPropsChangeListener", "animatedComponent", "viewTag", "set", "size", "listener", "data", "component", "get", "_updateFromNative", "props", "addListener", "removeOnJSPropsChangeListener", "delete", "removeAllListeners", "JSPropsUpdaterFabric", "isInitialized", "updater", "global", "updateJSProps", "JSPropsUpdaterWeb", "_animatedComponent", "JSPropsUpdater", "_IS_FABRIC"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/JSPropsUpdater.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,kBAAkB,EAAEC,QAAQ,QAAQ,cAAc;AAC3D,SAASC,cAAc,QAAQ,qCAAqC;AAEpE,SAASC,cAAc,QAAQ,uBAAoB;AAEnD,SAASC,OAAO,EAAEC,kBAAkB,QAAQ,eAAY;AAOxD,OAAOC,sBAAsB,MAAM,oCAAiC;AAOpE,MAAMC,iBAAiB,GAAGJ,cAAc,CAAC,CAAC;AAE1C,MAAMK,mBAAmB,CAA4B;EACnD,OAAeC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAGjDC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,uBAAuB,GAAG,IAAIZ,kBAAkB;IACnD;IACAC,QAAQ,CAACY,EAAE,KAAK,KAAK,IAAIZ,QAAQ,CAACY,EAAE,KAAK,OAAO,GAC3CP,sBAAsB,GACvBQ,SACN,CAAC;EACH;EAEOC,0BAA0BA,CAC/BC,iBAG4B,EAC5B;IACA,MAAMC,OAAO,GAAGf,cAAc,CAACc,iBAAiB,CAAC;IACjDR,mBAAmB,CAACC,sBAAsB,CAACS,GAAG,CAACD,OAAO,EAAED,iBAAiB,CAAC;IAC1E,IAAIR,mBAAmB,CAACC,sBAAsB,CAACU,IAAI,KAAK,CAAC,EAAE;MACzD,MAAMC,QAAQ,GAAIC,IAAkB,IAAK;QACvC,MAAMC,SAAS,GAAGd,mBAAmB,CAACC,sBAAsB,CAACc,GAAG,CAC9DF,IAAI,CAACJ,OACP,CAAC;QACDK,SAAS,EAAEE,iBAAiB,CAACH,IAAI,CAACI,KAAK,CAAC;MAC1C,CAAC;MACD,IAAI,CAACb,uBAAuB,CAACc,WAAW,CACtC,yBAAyB,EACzBN,QACF,CAAC;IACH;EACF;EAEOO,6BAA6BA,CAClCX,iBAG4B,EAC5B;IACA,MAAMC,OAAO,GAAGf,cAAc,CAACc,iBAAiB,CAAC;IACjDR,mBAAmB,CAACC,sBAAsB,CAACmB,MAAM,CAACX,OAAO,CAAC;IAC1D,IAAIT,mBAAmB,CAACC,sBAAsB,CAACU,IAAI,KAAK,CAAC,EAAE;MACzD,IAAI,CAACP,uBAAuB,CAACiB,kBAAkB,CAC7C,yBACF,CAAC;IACH;EACF;AACF;AAEA,MAAMC,oBAAoB,CAA4B;EACpD,OAAerB,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjD,OAAeqB,aAAa,GAAG,KAAK;EAEpCpB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACmB,oBAAoB,CAACC,aAAa,EAAE;MACvC,MAAMC,OAAO,GAAGA,CAACf,OAAe,EAAEQ,KAAc,KAAK;QACnD,MAAMH,SAAS,GACbQ,oBAAoB,CAACrB,sBAAsB,CAACc,GAAG,CAACN,OAAO,CAAC;QAC1DK,SAAS,EAAEE,iBAAiB,CAACC,KAAK,CAAC;MACrC,CAAC;MACDpB,kBAAkB,CAAC,MAAM;QACvB,SAAS;;QACT4B,MAAM,CAACC,aAAa,GAAG,CAACjB,OAAe,EAAEQ,KAAc,KAAK;UAC1DrB,OAAO,CAAC4B,OAAO,CAAC,CAACf,OAAO,EAAEQ,KAAK,CAAC;QAClC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;MACJK,oBAAoB,CAACC,aAAa,GAAG,IAAI;IAC3C;EACF;EAEOhB,0BAA0BA,CAC/BC,iBAG4B,EAC5B;IACA,IAAI,CAACc,oBAAoB,CAACC,aAAa,EAAE;MACvC;IACF;IACA,MAAMd,OAAO,GAAGf,cAAc,CAACc,iBAAiB,CAAC;IACjDc,oBAAoB,CAACrB,sBAAsB,CAACS,GAAG,CAACD,OAAO,EAAED,iBAAiB,CAAC;EAC7E;EAEOW,6BAA6BA,CAClCX,iBAG4B,EAC5B;IACA,IAAI,CAACc,oBAAoB,CAACC,aAAa,EAAE;MACvC;IACF;IACA,MAAMd,OAAO,GAAGf,cAAc,CAACc,iBAAiB,CAAC;IACjDc,oBAAoB,CAACrB,sBAAsB,CAACmB,MAAM,CAACX,OAAO,CAAC;EAC7D;AACF;AAEA,MAAMkB,iBAAiB,CAA4B;EAC1CpB,0BAA0BA,CAC/BqB,kBAG4B,EAC5B;IACA;EAAA;EAGKT,6BAA6BA,CAClCS,kBAG4B,EAC5B;IACA;EAAA;AAEJ;AAOA,IAAIC,cAAqC;AACzC,IAAI9B,iBAAiB,EAAE;EACrB8B,cAAc,GAAGF,iBAAiB;AACpC,CAAC,MAAM,IAAIF,MAAM,CAACK,UAAU,EAAE;EAC5BD,cAAc,GAAGP,oBAAoB;AACvC,CAAC,MAAM;EACLO,cAAc,GAAG7B,mBAAmB;AACtC;AAEA,eAAe6B,cAAc", "ignoreList": []}