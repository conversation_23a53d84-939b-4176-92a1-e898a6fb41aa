{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "NativeEventEmitter", "Platform", "findNodeHandle", "shouldBeUseWeb", "runOnJS", "runOnUIImmediately", "NativeReanimatedModule", "SHOULD_BE_USE_WEB", "JSPropsUpdaterPaper", "constructor", "_reanimatedEventEmitter", "OS", "undefined", "addOnJSPropsChangeListener", "animatedComponent", "viewTag", "_tagToComponentMapping", "set", "size", "listener", "data", "component", "get", "_updateFromNative", "props", "addListener", "removeOnJSPropsChangeListener", "delete", "removeAllListeners", "Map", "JSPropsUpdaterFabric", "isInitialized", "updater", "global", "updateJSProps", "JSPropsUpdaterWeb", "_animatedComponent", "JSPropsUpdater", "_IS_FABRIC"], "sources": ["JSPropsUpdater.ts"], "sourcesContent": ["'use strict';\nimport { NativeEventEmitter, Platform, findNodeHandle } from 'react-native';\nimport type { NativeModule } from 'react-native';\nimport { shouldBeUseWeb } from '../reanimated2/PlatformChecker';\nimport type { StyleProps } from '../reanimated2';\nimport { runOnJS, runOnUIImmediately } from '../reanimated2/threads';\nimport type {\n  AnimatedComponentProps,\n  IAnimatedComponentInternal,\n  IJSPropsUpdater,\n  InitialComponentProps,\n} from './commonTypes';\nimport NativeReanimatedModule from '../specs/NativeReanimatedModule';\n\ninterface ListenerData {\n  viewTag: number;\n  props: StyleProps;\n}\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\nclass JSPropsUpdaterPaper implements IJSPropsUpdater {\n  private static _tagToComponentMapping = new Map();\n  private _reanimatedEventEmitter: NativeEventEmitter;\n\n  constructor() {\n    this._reanimatedEventEmitter = new NativeEventEmitter(\n      // NativeEventEmitter only uses this parameter on iOS.\n      Platform.OS === 'ios'\n        ? (NativeReanimatedModule as unknown as NativeModule)\n        : undefined\n    );\n  }\n\n  public addOnJSPropsChangeListener(\n    animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    const viewTag = findNodeHandle(animatedComponent);\n    JSPropsUpdaterPaper._tagToComponentMapping.set(viewTag, animatedComponent);\n    if (JSPropsUpdaterPaper._tagToComponentMapping.size === 1) {\n      const listener = (data: ListenerData) => {\n        const component = JSPropsUpdaterPaper._tagToComponentMapping.get(\n          data.viewTag\n        );\n        component?._updateFromNative(data.props);\n      };\n      this._reanimatedEventEmitter.addListener(\n        'onReanimatedPropsChange',\n        listener\n      );\n    }\n  }\n\n  public removeOnJSPropsChangeListener(\n    animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    const viewTag = findNodeHandle(animatedComponent);\n    JSPropsUpdaterPaper._tagToComponentMapping.delete(viewTag);\n    if (JSPropsUpdaterPaper._tagToComponentMapping.size === 0) {\n      this._reanimatedEventEmitter.removeAllListeners(\n        'onReanimatedPropsChange'\n      );\n    }\n  }\n}\n\nclass JSPropsUpdaterFabric implements IJSPropsUpdater {\n  private static _tagToComponentMapping = new Map();\n  private static isInitialized = false;\n\n  constructor() {\n    if (!JSPropsUpdaterFabric.isInitialized) {\n      const updater = (viewTag: number, props: unknown) => {\n        const component =\n          JSPropsUpdaterFabric._tagToComponentMapping.get(viewTag);\n        component?._updateFromNative(props);\n      };\n      runOnUIImmediately(() => {\n        'worklet';\n        global.updateJSProps = (viewTag: number, props: unknown) => {\n          runOnJS(updater)(viewTag, props);\n        };\n      })();\n      JSPropsUpdaterFabric.isInitialized = true;\n    }\n  }\n\n  public addOnJSPropsChangeListener(\n    animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    if (!JSPropsUpdaterFabric.isInitialized) {\n      return;\n    }\n    const viewTag = findNodeHandle(animatedComponent);\n    JSPropsUpdaterFabric._tagToComponentMapping.set(viewTag, animatedComponent);\n  }\n\n  public removeOnJSPropsChangeListener(\n    animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    if (!JSPropsUpdaterFabric.isInitialized) {\n      return;\n    }\n    const viewTag = findNodeHandle(animatedComponent);\n    JSPropsUpdaterFabric._tagToComponentMapping.delete(viewTag);\n  }\n}\n\nclass JSPropsUpdaterWeb implements IJSPropsUpdater {\n  public addOnJSPropsChangeListener(\n    _animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    // noop\n  }\n\n  public removeOnJSPropsChangeListener(\n    _animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    // noop\n  }\n}\n\ntype JSPropsUpdaterOptions =\n  | typeof JSPropsUpdaterWeb\n  | typeof JSPropsUpdaterFabric\n  | typeof JSPropsUpdaterPaper;\n\nlet JSPropsUpdater: JSPropsUpdaterOptions;\nif (SHOULD_BE_USE_WEB) {\n  JSPropsUpdater = JSPropsUpdaterWeb;\n} else if (global._IS_FABRIC) {\n  JSPropsUpdater = JSPropsUpdaterFabric;\n} else {\n  JSPropsUpdater = JSPropsUpdaterPaper;\n}\n\nexport default JSPropsUpdater;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,kBAAkB,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,cAAc;AAE3E,SAASC,cAAc,QAAQ,gCAAgC;AAE/D,SAASC,OAAO,EAAEC,kBAAkB,QAAQ,wBAAwB;AAOpE,OAAOC,sBAAsB,MAAM,iCAAiC;AAOpE,MAAMC,iBAAiB,GAAGJ,cAAc,CAAC,CAAC;AAE1C,MAAMK,mBAAmB,CAA4B;EAInDC,WAAWA,CAAA,EAAG;IAAA9B,eAAA;IACZ,IAAI,CAAC+B,uBAAuB,GAAG,IAAIV,kBAAkB;IACnD;IACAC,QAAQ,CAACU,EAAE,KAAK,KAAK,GAChBL,sBAAsB,GACvBM,SACN,CAAC;EACH;EAEOC,0BAA0BA,CAC/BC,iBAG4B,EAC5B;IACA,MAAMC,OAAO,GAAGb,cAAc,CAACY,iBAAiB,CAAC;IACjDN,mBAAmB,CAACQ,sBAAsB,CAACC,GAAG,CAACF,OAAO,EAAED,iBAAiB,CAAC;IAC1E,IAAIN,mBAAmB,CAACQ,sBAAsB,CAACE,IAAI,KAAK,CAAC,EAAE;MACzD,MAAMC,QAAQ,GAAIC,IAAkB,IAAK;QACvC,MAAMC,SAAS,GAAGb,mBAAmB,CAACQ,sBAAsB,CAACM,GAAG,CAC9DF,IAAI,CAACL,OACP,CAAC;QACDM,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEE,iBAAiB,CAACH,IAAI,CAACI,KAAK,CAAC;MAC1C,CAAC;MACD,IAAI,CAACd,uBAAuB,CAACe,WAAW,CACtC,yBAAyB,EACzBN,QACF,CAAC;IACH;EACF;EAEOO,6BAA6BA,CAClCZ,iBAG4B,EAC5B;IACA,MAAMC,OAAO,GAAGb,cAAc,CAACY,iBAAiB,CAAC;IACjDN,mBAAmB,CAACQ,sBAAsB,CAACW,MAAM,CAACZ,OAAO,CAAC;IAC1D,IAAIP,mBAAmB,CAACQ,sBAAsB,CAACE,IAAI,KAAK,CAAC,EAAE;MACzD,IAAI,CAACR,uBAAuB,CAACkB,kBAAkB,CAC7C,yBACF,CAAC;IACH;EACF;AACF;AAACjD,eAAA,CAjDK6B,mBAAmB,4BACiB,IAAIqB,GAAG,CAAC,CAAC;AAkDnD,MAAMC,oBAAoB,CAA4B;EAIpDrB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACqB,oBAAoB,CAACC,aAAa,EAAE;MACvC,MAAMC,OAAO,GAAGA,CAACjB,OAAe,EAAES,KAAc,KAAK;QACnD,MAAMH,SAAS,GACbS,oBAAoB,CAACd,sBAAsB,CAACM,GAAG,CAACP,OAAO,CAAC;QAC1DM,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEE,iBAAiB,CAACC,KAAK,CAAC;MACrC,CAAC;MACDnB,kBAAkB,CAAC,MAAM;QACvB,SAAS;;QACT4B,MAAM,CAACC,aAAa,GAAG,CAACnB,OAAe,EAAES,KAAc,KAAK;UAC1DpB,OAAO,CAAC4B,OAAO,CAAC,CAACjB,OAAO,EAAES,KAAK,CAAC;QAClC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;MACJM,oBAAoB,CAACC,aAAa,GAAG,IAAI;IAC3C;EACF;EAEOlB,0BAA0BA,CAC/BC,iBAG4B,EAC5B;IACA,IAAI,CAACgB,oBAAoB,CAACC,aAAa,EAAE;MACvC;IACF;IACA,MAAMhB,OAAO,GAAGb,cAAc,CAACY,iBAAiB,CAAC;IACjDgB,oBAAoB,CAACd,sBAAsB,CAACC,GAAG,CAACF,OAAO,EAAED,iBAAiB,CAAC;EAC7E;EAEOY,6BAA6BA,CAClCZ,iBAG4B,EAC5B;IACA,IAAI,CAACgB,oBAAoB,CAACC,aAAa,EAAE;MACvC;IACF;IACA,MAAMhB,OAAO,GAAGb,cAAc,CAACY,iBAAiB,CAAC;IACjDgB,oBAAoB,CAACd,sBAAsB,CAACW,MAAM,CAACZ,OAAO,CAAC;EAC7D;AACF;AAACpC,eAAA,CA9CKmD,oBAAoB,4BACgB,IAAID,GAAG,CAAC,CAAC;AAAAlD,eAAA,CAD7CmD,oBAAoB,mBAEO,KAAK;AA8CtC,MAAMK,iBAAiB,CAA4B;EAC1CtB,0BAA0BA,CAC/BuB,kBAG4B,EAC5B;IACA;EAAA;EAGKV,6BAA6BA,CAClCU,kBAG4B,EAC5B;IACA;EAAA;AAEJ;AAOA,IAAIC,cAAqC;AACzC,IAAI9B,iBAAiB,EAAE;EACrB8B,cAAc,GAAGF,iBAAiB;AACpC,CAAC,MAAM,IAAIF,MAAM,CAACK,UAAU,EAAE;EAC5BD,cAAc,GAAGP,oBAAoB;AACvC,CAAC,MAAM;EACLO,cAAc,GAAG7B,mBAAmB;AACtC;AAEA,eAAe6B,cAAc", "ignoreList": []}