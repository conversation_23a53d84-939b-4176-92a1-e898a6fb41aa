{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_LIGHTSPEED_TIME", "LightSpeedInData", "LightSpeedInRight", "name", "style", "transform", "translateX", "skewX", "opacity", "duration", "LightSpeedInLeft", "LightSpeedOutData", "LightSpeedOutRight", "LightSpeedOutLeft", "skew", "LightSpeedIn", "LightSpeedOut"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/animation/Lightspeed.web.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,uBAAoB;AAEtE,MAAMC,uBAAuB,GAAG,GAAG;AAEnC,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,iBAAiB,EAAE;IACjBC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAQ,CAAC;MAAE,CAAC;MACvC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAQ,CAAC;MAAE,CAAC;MACvC,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE;IACxC,CAAC;IACDE,QAAQ,EAAET;EACZ,CAAC;EAEDU,gBAAgB,EAAE;IAChBP,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAS,CAAC;MAAE,CAAC;MACxC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE,CAAC;MACtC,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE;IACxC,CAAC;IACDE,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMW,iBAAiB,GAAG;EAC/BC,kBAAkB,EAAE;IAClBT,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAO,CAAC,CAAC;QACjDC,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDa,iBAAiB,EAAE;IACjBV,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEQ,IAAI,EAAE;QAAO,CAAC,CAAC;QAChDN,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEQ,IAAI,EAAE;QAAQ,CAAC,CAAC;QACpDN,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMe,YAAY,GAAG;EAC1Bb,iBAAiB,EAAE;IACjBE,KAAK,EAAEL,iCAAiC,CACtCE,gBAAgB,CAACC,iBACnB,CAAC;IACDO,QAAQ,EAAER,gBAAgB,CAACC,iBAAiB,CAACO;EAC/C,CAAC;EACDC,gBAAgB,EAAE;IAChBN,KAAK,EAAEL,iCAAiC,CAACE,gBAAgB,CAACS,gBAAgB,CAAC;IAC3ED,QAAQ,EAAER,gBAAgB,CAACS,gBAAgB,CAACD;EAC9C;AACF,CAAC;AAED,OAAO,MAAMO,aAAa,GAAG;EAC3BJ,kBAAkB,EAAE;IAClBR,KAAK,EAAEL,iCAAiC,CACtCY,iBAAiB,CAACC,kBACpB,CAAC;IACDH,QAAQ,EAAEE,iBAAiB,CAACC,kBAAkB,CAACH;EACjD,CAAC;EACDI,iBAAiB,EAAE;IACjBT,KAAK,EAAEL,iCAAiC,CACtCY,iBAAiB,CAACE,iBACpB,CAAC;IACDJ,QAAQ,EAAEE,iBAAiB,CAACE,iBAAiB,CAACJ;EAChD;AACF,CAAC", "ignoreList": []}