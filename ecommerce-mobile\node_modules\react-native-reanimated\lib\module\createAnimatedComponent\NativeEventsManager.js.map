{"version": 3, "names": ["has", "WorkletEventHandler", "findNodeHandle", "NativeEventsManager", "managedComponent", "componentOptions", "eventViewTag", "constructor", "component", "options", "getEventViewTag", "attachEvents", "executeForEachEventHandler", "props", "key", "handler", "registerForEvents", "detachEvents", "_key", "unregisterFromEvents", "updateEvents", "prevProps", "computedEventTag", "prev<PERSON><PERSON><PERSON>", "newProp", "isWorkletEventHandler", "workletEventHandler", "componentAnimatedRef", "_component", "newTag", "getScrollableNode", "scrollableNode", "setNativeProps", "prop", "callback"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/NativeEventsManager.ts"], "mappings": "AAAA,YAAY;;AAQZ,SAASA,GAAG,QAAQ,YAAS;AAC7B,SAASC,mBAAmB,QAAQ,2BAAwB;AAC5D,SAASC,cAAc,QAAQ,qCAAqC;AAEpE,OAAO,MAAMC,mBAAmB,CAAiC;EACtD,CAACC,gBAAgB;EACjB,CAACC,gBAAgB;EAC1B,CAACC,YAAY,GAAG,CAAC,CAAC;EAElBC,WAAWA,CAACC,SAAmC,EAAEC,OAA0B,EAAE;IAC3E,IAAI,CAAC,CAACL,gBAAgB,GAAGI,SAAS;IAClC,IAAI,CAAC,CAACH,gBAAgB,GAAGI,OAAO;IAChC,IAAI,CAAC,CAACH,YAAY,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC;EAC7C;EAEOC,YAAYA,CAAA,EAAG;IACpBC,0BAA0B,CAAC,IAAI,CAAC,CAACR,gBAAgB,CAACS,KAAK,EAAE,CAACC,GAAG,EAAEC,OAAO,KAAK;MACzEA,OAAO,CAACC,iBAAiB,CAAC,IAAI,CAAC,CAACV,YAAY,EAAEQ,GAAG,CAAC;IACpD,CAAC,CAAC;EACJ;EAEOG,YAAYA,CAAA,EAAG;IACpBL,0BAA0B,CACxB,IAAI,CAAC,CAACR,gBAAgB,CAACS,KAAK,EAC5B,CAACK,IAAI,EAAEH,OAAO,KAAK;MACjBA,OAAO,CAACI,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;IAClD,CACF,CAAC;EACH;EAEOc,YAAYA,CACjBC,SAAwD,EACxD;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACZ,eAAe,CAAC,CAAC;IAC/C;IACA,IAAI,IAAI,CAAC,CAACJ,YAAY,KAAKgB,gBAAgB,EAAE;MAC3C;MACAV,0BAA0B,CAACS,SAAS,EAAE,CAACH,IAAI,EAAEH,OAAO,KAAK;QACvDA,OAAO,CAACI,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;MAClD,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC,CAACA,YAAY,GAAGgB,gBAAgB;MACrC;MACA,IAAI,CAACX,YAAY,CAAC,CAAC;MACnB;IACF;IAEAC,0BAA0B,CAACS,SAAS,EAAE,CAACP,GAAG,EAAES,WAAW,KAAK;MAC1D,MAAMC,OAAO,GAAG,IAAI,CAAC,CAACpB,gBAAgB,CAACS,KAAK,CAACC,GAAG,CAAC;MACjD,IAAI,CAACU,OAAO,EAAE;QACZ;QACAD,WAAW,CAACJ,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;MACtD,CAAC,MAAM,IACLmB,qBAAqB,CAACD,OAAO,CAAC,IAC9BA,OAAO,CAACE,mBAAmB,KAAKH,WAAW,EAC3C;QACA;QACAA,WAAW,CAACJ,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;QACpDkB,OAAO,CAACE,mBAAmB,CAACV,iBAAiB,CAAC,IAAI,CAAC,CAACV,YAAY,CAAC;MACnE;IACF,CAAC,CAAC;IAEFM,0BAA0B,CAAC,IAAI,CAAC,CAACR,gBAAgB,CAACS,KAAK,EAAE,CAACC,GAAG,EAAEC,OAAO,KAAK;MACzE,IAAI,CAACM,SAAS,CAACP,GAAG,CAAC,EAAE;QACnB;QACAC,OAAO,CAACC,iBAAiB,CAAC,IAAI,CAAC,CAACV,YAAY,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEQI,eAAeA,CAAA,EAAG;IACxB;IACA,MAAMiB,oBAAoB,GAAG,IAAI,CAAC,CAACvB,gBAAgB,CAChDwB,UAAkC;IACrC,IAAIC,MAAc;IAClB,IAAIF,oBAAoB,CAACG,iBAAiB,EAAE;MAC1C,MAAMC,cAAc,GAAGJ,oBAAoB,CAACG,iBAAiB,CAAC,CAAC;MAC/DD,MAAM,GAAG3B,cAAc,CAAC6B,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLF,MAAM,GACJ3B,cAAc,CACZ,IAAI,CAAC,CAACG,gBAAgB,EAAE2B,cAAc,GAClC,IAAI,CAAC,CAAC5B,gBAAgB,GACtBuB,oBACN,CAAC,IAAI,CAAC,CAAC;IACX;IACA,OAAOE,MAAM;EACf;AACF;AAEA,SAASJ,qBAAqBA,CAC5BQ,IAAa,EACsB;EACnC,OACEjC,GAAG,CAAC,qBAAqB,EAAEiC,IAAI,CAAC,IAChCA,IAAI,CAACP,mBAAmB,YAAYzB,mBAAmB;AAE3D;AAEA,SAASW,0BAA0BA,CACjCC,KAAoD,EACpDqB,QAGS,EACT;EACA,KAAK,MAAMpB,GAAG,IAAID,KAAK,EAAE;IACvB,MAAMoB,IAAI,GAAGpB,KAAK,CAACC,GAAG,CAAC;IACvB,IAAIW,qBAAqB,CAACQ,IAAI,CAAC,EAAE;MAC/BC,QAAQ,CAACpB,GAAG,EAAEmB,IAAI,CAACP,mBAAmB,CAAC;IACzC;EACF;AACF", "ignoreList": []}