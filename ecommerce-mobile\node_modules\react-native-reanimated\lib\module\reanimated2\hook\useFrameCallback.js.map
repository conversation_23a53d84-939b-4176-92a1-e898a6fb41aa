{"version": 3, "names": ["useEffect", "useRef", "FrameCallbackRegistryJS", "frameCallbackRegistry", "useFrameCallback", "callback", "autostart", "ref", "setActive", "isActive", "manageStateFrameCallback", "current", "callbackId", "registerFrameCallback", "unregisterFrameCallback"], "sources": ["useFrameCallback.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useRef } from 'react';\nimport FrameCallbackRegistryJS from '../frameCallback/FrameCallbackRegistryJS';\nimport type { FrameInfo } from '../frameCallback/FrameCallbackRegistryUI';\n\n/**\n * @param setActive - A function that lets you start the frame callback or stop it from running.\n * @param isActive - A boolean indicating whether a callback is running.\n * @param callbackId - A number indicating a unique identifier of the frame callback.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useFrameCallback#returns\n */\nexport type FrameCallback = {\n  setActive: (isActive: boolean) => void;\n  isActive: boolean;\n  callbackId: number;\n};\nconst frameCallbackRegistry = new FrameCallbackRegistryJS();\n\n/**\n * Lets you run a function on every frame update.\n *\n * @param callback - A function executed on every frame update.\n * @param autostart - Whether the callback should start automatically. Defaults to `true`.\n * @returns A frame callback object - {@link FrameCallback}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useFrameCallback\n */\nexport function useFrameCallback(\n  callback: (frameInfo: FrameInfo) => void,\n  autostart = true\n): FrameCallback {\n  const ref = useRef<FrameCallback>({\n    setActive: (isActive: boolean) => {\n      frameCallbackRegistry.manageStateFrameCallback(\n        ref.current.callbackId,\n        isActive\n      );\n      ref.current.isActive = isActive;\n    },\n    isActive: autostart,\n    callbackId: -1,\n  });\n\n  useEffect(() => {\n    ref.current.callbackId =\n      frameCallbackRegistry.registerFrameCallback(callback);\n    ref.current.setActive(ref.current.isActive);\n\n    return () => {\n      frameCallbackRegistry.unregisterFrameCallback(ref.current.callbackId);\n      ref.current.callbackId = -1;\n    };\n  }, [callback, autostart]);\n\n  return ref.current;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,uBAAuB,MAAM,0CAA0C;;AAG9E;AACA;AACA;AACA;AACA;AACA;;AAMA,MAAMC,qBAAqB,GAAG,IAAID,uBAAuB,CAAC,CAAC;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAC9BC,QAAwC,EACxCC,SAAS,GAAG,IAAI,EACD;EACf,MAAMC,GAAG,GAAGN,MAAM,CAAgB;IAChCO,SAAS,EAAGC,QAAiB,IAAK;MAChCN,qBAAqB,CAACO,wBAAwB,CAC5CH,GAAG,CAACI,OAAO,CAACC,UAAU,EACtBH,QACF,CAAC;MACDF,GAAG,CAACI,OAAO,CAACF,QAAQ,GAAGA,QAAQ;IACjC,CAAC;IACDA,QAAQ,EAAEH,SAAS;IACnBM,UAAU,EAAE,CAAC;EACf,CAAC,CAAC;EAEFZ,SAAS,CAAC,MAAM;IACdO,GAAG,CAACI,OAAO,CAACC,UAAU,GACpBT,qBAAqB,CAACU,qBAAqB,CAACR,QAAQ,CAAC;IACvDE,GAAG,CAACI,OAAO,CAACH,SAAS,CAACD,GAAG,CAACI,OAAO,CAACF,QAAQ,CAAC;IAE3C,OAAO,MAAM;MACXN,qBAAqB,CAACW,uBAAuB,CAACP,GAAG,CAACI,OAAO,CAACC,UAAU,CAAC;MACrEL,GAAG,CAACI,OAAO,CAACC,UAAU,GAAG,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAACP,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAEzB,OAAOC,GAAG,CAACI,OAAO;AACpB", "ignoreList": []}