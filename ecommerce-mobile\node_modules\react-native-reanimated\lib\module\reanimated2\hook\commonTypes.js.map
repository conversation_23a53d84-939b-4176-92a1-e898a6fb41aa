{"version": 3, "names": [], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\nimport type { Component, MutableRefObject } from 'react';\nimport type {\n  AnimatedPropsAdapterFunction,\n  ShadowNodeWrapper,\n  SharedValue,\n  WorkletFunction,\n} from '../commonTypes';\nimport type {\n  ImageStyle,\n  NativeSyntheticEvent,\n  TextStyle,\n  ViewStyle,\n  NativeScrollEvent,\n} from 'react-native';\nimport type { ViewDescriptorsSet, ViewRefSet } from '../ViewDescriptorsSet';\nimport type { AnimatedStyle } from '../helperTypes';\n\nexport type DependencyList = Array<unknown> | undefined;\n\nexport interface Descriptor {\n  tag: number;\n  name: string;\n  shadowNodeWrapper: ShadowNodeWrapper;\n}\n\nexport interface AnimatedRef<T extends Component> {\n  (component?: T):\n    | number // Paper\n    | ShadowNodeWrapper // Fabric\n    | HTMLElement; // web\n  current: T | null;\n  getTag: () => number;\n}\n\n// Might make that type generic if it's ever needed.\nexport type AnimatedRefOnJS = AnimatedRef<Component>;\n\n/**\n * `AnimatedRef` is mapped to this type on the UI thread via a shareable handle.\n */\nexport type AnimatedRefOnUI = {\n  (): number | ShadowNodeWrapper | null;\n  /**\n   * @remarks `viewName` is required only on iOS with Paper and it's value is null on other platforms.\n   */\n  viewName: SharedValue<string | null>;\n};\n\ntype ReanimatedPayload = {\n  eventName: string;\n};\n\n/**\n * This utility type is to convert type of events that would normally be\n * sent by React Native (they have `nativeEvent` field) to the type\n * that is sent by Reanimated.\n */\nexport type ReanimatedEvent<Event extends object> = ReanimatedPayload &\n  (Event extends {\n    nativeEvent: infer NativeEvent extends object;\n  }\n    ? NativeEvent\n    : Event);\n\nexport type EventPayload<Event extends object> = Event extends {\n  nativeEvent: infer NativeEvent extends object;\n}\n  ? NativeEvent\n  : Omit<Event, 'eventName'>;\n\nexport type NativeEventWrapper<Event extends object> = {\n  nativeEvent: Event;\n};\n\nexport type DefaultStyle = ViewStyle | ImageStyle | TextStyle;\n\nexport type RNNativeScrollEvent = NativeSyntheticEvent<NativeScrollEvent>;\n\nexport type ReanimatedScrollEvent = ReanimatedEvent<RNNativeScrollEvent>;\n\nexport interface IWorkletEventHandler<Event extends object> {\n  updateEventHandler: (\n    newWorklet: (event: ReanimatedEvent<Event>) => void,\n    newEvents: string[]\n  ) => void;\n  registerForEvents: (viewTag: number, fallbackEventName?: string) => void;\n  unregisterFromEvents: (viewTag: number) => void;\n}\n\nexport interface AnimatedStyleHandle<\n  Style extends DefaultStyle = DefaultStyle\n> {\n  viewDescriptors: ViewDescriptorsSet;\n  initial: {\n    value: AnimatedStyle<Style>;\n    updater: () => AnimatedStyle<Style>;\n  };\n  /**\n   * @remarks `viewsRef` is only defined in Web implementation.\n   */\n  viewsRef: ViewRefSet<unknown> | undefined;\n}\n\nexport interface JestAnimatedStyleHandle<\n  Style extends DefaultStyle = DefaultStyle\n> extends AnimatedStyleHandle<Style> {\n  jestAnimatedStyle: MutableRefObject<AnimatedStyle<Style>>;\n}\n\nexport type UseAnimatedStyleInternal<Style extends DefaultStyle> = (\n  updater: WorkletFunction<[], Style> | (() => Style),\n  dependencies?: DependencyList | null,\n  adapters?:\n    | AnimatedPropsAdapterFunction\n    | AnimatedPropsAdapterFunction[]\n    | null,\n  isAnimatedProps?: boolean\n) => AnimatedStyleHandle<Style> | JestAnimatedStyleHandle<Style>;\n"], "mappings": "AAAA,YAAY;;AAAC", "ignoreList": []}