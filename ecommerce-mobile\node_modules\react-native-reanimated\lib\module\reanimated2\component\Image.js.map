{"version": 3, "names": ["Image", "createAnimatedComponent", "AnimatedImage"], "sources": ["Image.ts"], "sourcesContent": ["'use strict';\nimport { Image } from 'react-native';\nimport { createAnimatedComponent } from '../../createAnimatedComponent';\n\n// Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n// but not things like NativeMethods, etc. we need to add them manually by extending the type.\ninterface AnimatedImageComplement extends Image {\n  getNode(): Image;\n}\n\nexport const AnimatedImage = createAnimatedComponent(Image);\n\nexport type AnimatedImage = typeof AnimatedImage & AnimatedImageComplement;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,KAAK,QAAQ,cAAc;AACpC,SAASC,uBAAuB,QAAQ,+BAA+B;;AAEvE;AACA;;AAKA,OAAO,MAAMC,aAAa,GAAGD,uBAAuB,CAACD,KAAK,CAAC", "ignoreList": []}