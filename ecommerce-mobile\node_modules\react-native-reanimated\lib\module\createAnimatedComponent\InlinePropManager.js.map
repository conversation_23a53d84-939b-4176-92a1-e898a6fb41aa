{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "flattenArray", "makeViewDescriptorsSet", "adaptViewConfig", "updateProps", "stopMapper", "startMapper", "isSharedValue", "shouldBeUseWeb", "SHOULD_BE_USE_WEB", "isInlineStyleTransform", "transform", "Array", "isArray", "some", "hasInlineStyles", "inlinePropsHasChanged", "styles1", "styles2", "keys", "length", "getInlinePropsUpdate", "inlineProps", "update", "styleValue", "entries", "map", "item", "extractSharedValuesMapFromProps", "props", "styles", "style", "for<PERSON>ach", "styleKey", "getInlineStyle", "shouldGetInitialStyle", "newStyle", "InlinePropManager", "constructor", "attachInlineProps", "animatedComponent", "viewInfo", "newInlineProps", "has<PERSON><PERSON>ed", "_inlineProps", "_inlinePropsViewDescriptors", "viewTag", "viewName", "shadowNodeWrapper", "viewConfig", "add", "tag", "name", "shareableViewDescriptors", "maybeViewRef", "items", "Set", "undefined", "updaterFunction", "_inlinePropsMapperId", "values", "detachInlineProps"], "sources": ["InlinePropManager.ts"], "sourcesContent": ["'use strict';\nimport type { StyleProps } from '../reanimated2';\nimport type {\n  IAnimatedComponentInternal,\n  AnimatedComponentProps,\n  IInlinePropManager,\n  ViewInfo,\n} from './commonTypes';\nimport { flattenArray } from './utils';\nimport { makeViewDescriptorsSet } from '../reanimated2/ViewDescriptorsSet';\nimport type {\n  ViewDescriptorsSet,\n  ViewRefSet,\n} from '../reanimated2/ViewDescriptorsSet';\nimport { adaptViewConfig } from '../ConfigHelper';\nimport updateProps from '../reanimated2/UpdateProps';\nimport { stopMapper, startMapper } from '../reanimated2/mappers';\nimport { isSharedValue } from '../reanimated2/isSharedValue';\nimport { shouldBeUseWeb } from '../reanimated2/PlatformChecker';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\nfunction isInlineStyleTransform(transform: unknown): boolean {\n  if (!Array.isArray(transform)) {\n    return false;\n  }\n\n  return transform.some((t: Record<string, unknown>) => hasInlineStyles(t));\n}\n\nfunction inlinePropsHasChanged(\n  styles1: StyleProps,\n  styles2: StyleProps\n): boolean {\n  if (Object.keys(styles1).length !== Object.keys(styles2).length) {\n    return true;\n  }\n\n  for (const key of Object.keys(styles1)) {\n    if (styles1[key] !== styles2[key]) return true;\n  }\n\n  return false;\n}\n\nfunction getInlinePropsUpdate(inlineProps: Record<string, unknown>) {\n  'worklet';\n  const update: Record<string, unknown> = {};\n  for (const [key, styleValue] of Object.entries(inlineProps)) {\n    if (isSharedValue(styleValue)) {\n      update[key] = styleValue.value;\n    } else if (Array.isArray(styleValue)) {\n      update[key] = styleValue.map((item) => {\n        return getInlinePropsUpdate(item);\n      });\n    } else if (typeof styleValue === 'object') {\n      update[key] = getInlinePropsUpdate(styleValue as Record<string, unknown>);\n    } else {\n      update[key] = styleValue;\n    }\n  }\n  return update;\n}\n\nfunction extractSharedValuesMapFromProps(\n  props: AnimatedComponentProps<\n    Record<string, unknown> /* Initial component props */\n  >\n): Record<string, unknown> {\n  const inlineProps: Record<string, unknown> = {};\n\n  for (const key in props) {\n    const value = props[key];\n    if (key === 'style') {\n      const styles = flattenArray<StyleProps>(props.style ?? []);\n      styles.forEach((style) => {\n        if (!style) {\n          return;\n        }\n        for (const [styleKey, styleValue] of Object.entries(style)) {\n          if (isSharedValue(styleValue)) {\n            inlineProps[styleKey] = styleValue;\n          } else if (\n            styleKey === 'transform' &&\n            isInlineStyleTransform(styleValue)\n          ) {\n            inlineProps[styleKey] = styleValue;\n          }\n        }\n      });\n    } else if (isSharedValue(value)) {\n      inlineProps[key] = value;\n    }\n  }\n\n  return inlineProps;\n}\n\nexport function hasInlineStyles(style: StyleProps): boolean {\n  if (!style) {\n    return false;\n  }\n  return Object.keys(style).some((key) => {\n    const styleValue = style[key];\n    return (\n      isSharedValue(styleValue) ||\n      (key === 'transform' && isInlineStyleTransform(styleValue))\n    );\n  });\n}\n\nexport function getInlineStyle(\n  style: Record<string, unknown>,\n  shouldGetInitialStyle: boolean\n) {\n  if (shouldGetInitialStyle) {\n    return getInlinePropsUpdate(style);\n  }\n  const newStyle: StyleProps = {};\n  for (const [key, styleValue] of Object.entries(style)) {\n    if (\n      !isSharedValue(styleValue) &&\n      !(key === 'transform' && isInlineStyleTransform(styleValue))\n    ) {\n      newStyle[key] = styleValue;\n    }\n  }\n  return newStyle;\n}\n\nexport class InlinePropManager implements IInlinePropManager {\n  _inlinePropsViewDescriptors: ViewDescriptorsSet | null = null;\n  _inlinePropsMapperId: number | null = null;\n  _inlineProps: StyleProps = {};\n\n  public attachInlineProps(\n    animatedComponent: React.Component<unknown, unknown> &\n      IAnimatedComponentInternal,\n    viewInfo: ViewInfo\n  ) {\n    const newInlineProps: Record<string, unknown> =\n      extractSharedValuesMapFromProps(animatedComponent.props);\n    const hasChanged = inlinePropsHasChanged(newInlineProps, this._inlineProps);\n\n    if (hasChanged) {\n      if (!this._inlinePropsViewDescriptors) {\n        this._inlinePropsViewDescriptors = makeViewDescriptorsSet();\n\n        const { viewTag, viewName, shadowNodeWrapper, viewConfig } = viewInfo;\n\n        if (Object.keys(newInlineProps).length && viewConfig) {\n          adaptViewConfig(viewConfig);\n        }\n\n        this._inlinePropsViewDescriptors.add({\n          tag: viewTag as number,\n          name: viewName!,\n          shadowNodeWrapper: shadowNodeWrapper!,\n        });\n      }\n      const shareableViewDescriptors =\n        this._inlinePropsViewDescriptors.shareableViewDescriptors;\n\n      const maybeViewRef = SHOULD_BE_USE_WEB\n        ? ({ items: new Set([animatedComponent]) } as ViewRefSet<unknown>) // see makeViewsRefSet\n        : undefined;\n      const updaterFunction = () => {\n        'worklet';\n        const update = getInlinePropsUpdate(newInlineProps);\n        updateProps(shareableViewDescriptors, update, maybeViewRef);\n      };\n      this._inlineProps = newInlineProps;\n      if (this._inlinePropsMapperId) {\n        stopMapper(this._inlinePropsMapperId);\n      }\n      this._inlinePropsMapperId = null;\n      if (Object.keys(newInlineProps).length) {\n        this._inlinePropsMapperId = startMapper(\n          updaterFunction,\n          Object.values(newInlineProps)\n        );\n      }\n    }\n  }\n\n  public detachInlineProps() {\n    if (this._inlinePropsMapperId) {\n      stopMapper(this._inlinePropsMapperId);\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAQb,SAASW,YAAY,QAAQ,SAAS;AACtC,SAASC,sBAAsB,QAAQ,mCAAmC;AAK1E,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,UAAU,EAAEC,WAAW,QAAQ,wBAAwB;AAChE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,cAAc,QAAQ,gCAAgC;AAE/D,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,CAAC;AAE1C,SAASE,sBAAsBA,CAACC,SAAkB,EAAW;EAC3D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOA,SAAS,CAACG,IAAI,CAAExB,CAA0B,IAAKyB,eAAe,CAACzB,CAAC,CAAC,CAAC;AAC3E;AAEA,SAAS0B,qBAAqBA,CAC5BC,OAAmB,EACnBC,OAAmB,EACV;EACT,IAAIjC,MAAM,CAACkC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,KAAKnC,MAAM,CAACkC,IAAI,CAACD,OAAO,CAAC,CAACE,MAAM,EAAE;IAC/D,OAAO,IAAI;EACb;EAEA,KAAK,MAAMtC,GAAG,IAAIG,MAAM,CAACkC,IAAI,CAACF,OAAO,CAAC,EAAE;IACtC,IAAIA,OAAO,CAACnC,GAAG,CAAC,KAAKoC,OAAO,CAACpC,GAAG,CAAC,EAAE,OAAO,IAAI;EAChD;EAEA,OAAO,KAAK;AACd;AAEA,SAASuC,oBAAoBA,CAACC,WAAoC,EAAE;EAClE,SAAS;;EACT,MAAMC,MAA+B,GAAG,CAAC,CAAC;EAC1C,KAAK,MAAM,CAACzC,GAAG,EAAE0C,UAAU,CAAC,IAAIvC,MAAM,CAACwC,OAAO,CAACH,WAAW,CAAC,EAAE;IAC3D,IAAIf,aAAa,CAACiB,UAAU,CAAC,EAAE;MAC7BD,MAAM,CAACzC,GAAG,CAAC,GAAG0C,UAAU,CAACzC,KAAK;IAChC,CAAC,MAAM,IAAI6B,KAAK,CAACC,OAAO,CAACW,UAAU,CAAC,EAAE;MACpCD,MAAM,CAACzC,GAAG,CAAC,GAAG0C,UAAU,CAACE,GAAG,CAAEC,IAAI,IAAK;QACrC,OAAON,oBAAoB,CAACM,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;MACzCD,MAAM,CAACzC,GAAG,CAAC,GAAGuC,oBAAoB,CAACG,UAAqC,CAAC;IAC3E,CAAC,MAAM;MACLD,MAAM,CAACzC,GAAG,CAAC,GAAG0C,UAAU;IAC1B;EACF;EACA,OAAOD,MAAM;AACf;AAEA,SAASK,+BAA+BA,CACtCC,KAEC,EACwB;EACzB,MAAMP,WAAoC,GAAG,CAAC,CAAC;EAE/C,KAAK,MAAMxC,GAAG,IAAI+C,KAAK,EAAE;IACvB,MAAM9C,KAAK,GAAG8C,KAAK,CAAC/C,GAAG,CAAC;IACxB,IAAIA,GAAG,KAAK,OAAO,EAAE;MACnB,MAAMgD,MAAM,GAAG7B,YAAY,CAAa4B,KAAK,CAACE,KAAK,IAAI,EAAE,CAAC;MAC1DD,MAAM,CAACE,OAAO,CAAED,KAAK,IAAK;QACxB,IAAI,CAACA,KAAK,EAAE;UACV;QACF;QACA,KAAK,MAAM,CAACE,QAAQ,EAAET,UAAU,CAAC,IAAIvC,MAAM,CAACwC,OAAO,CAACM,KAAK,CAAC,EAAE;UAC1D,IAAIxB,aAAa,CAACiB,UAAU,CAAC,EAAE;YAC7BF,WAAW,CAACW,QAAQ,CAAC,GAAGT,UAAU;UACpC,CAAC,MAAM,IACLS,QAAQ,KAAK,WAAW,IACxBvB,sBAAsB,CAACc,UAAU,CAAC,EAClC;YACAF,WAAW,CAACW,QAAQ,CAAC,GAAGT,UAAU;UACpC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIjB,aAAa,CAACxB,KAAK,CAAC,EAAE;MAC/BuC,WAAW,CAACxC,GAAG,CAAC,GAAGC,KAAK;IAC1B;EACF;EAEA,OAAOuC,WAAW;AACpB;AAEA,OAAO,SAASP,eAAeA,CAACgB,KAAiB,EAAW;EAC1D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAO9C,MAAM,CAACkC,IAAI,CAACY,KAAK,CAAC,CAACjB,IAAI,CAAEhC,GAAG,IAAK;IACtC,MAAM0C,UAAU,GAAGO,KAAK,CAACjD,GAAG,CAAC;IAC7B,OACEyB,aAAa,CAACiB,UAAU,CAAC,IACxB1C,GAAG,KAAK,WAAW,IAAI4B,sBAAsB,CAACc,UAAU,CAAE;EAE/D,CAAC,CAAC;AACJ;AAEA,OAAO,SAASU,cAAcA,CAC5BH,KAA8B,EAC9BI,qBAA8B,EAC9B;EACA,IAAIA,qBAAqB,EAAE;IACzB,OAAOd,oBAAoB,CAACU,KAAK,CAAC;EACpC;EACA,MAAMK,QAAoB,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAM,CAACtD,GAAG,EAAE0C,UAAU,CAAC,IAAIvC,MAAM,CAACwC,OAAO,CAACM,KAAK,CAAC,EAAE;IACrD,IACE,CAACxB,aAAa,CAACiB,UAAU,CAAC,IAC1B,EAAE1C,GAAG,KAAK,WAAW,IAAI4B,sBAAsB,CAACc,UAAU,CAAC,CAAC,EAC5D;MACAY,QAAQ,CAACtD,GAAG,CAAC,GAAG0C,UAAU;IAC5B;EACF;EACA,OAAOY,QAAQ;AACjB;AAEA,OAAO,MAAMC,iBAAiB,CAA+B;EAAAC,YAAA;IAAA1D,eAAA,sCACF,IAAI;IAAAA,eAAA,+BACvB,IAAI;IAAAA,eAAA,uBACf,CAAC,CAAC;EAAA;EAEtB2D,iBAAiBA,CACtBC,iBAC4B,EAC5BC,QAAkB,EAClB;IACA,MAAMC,cAAuC,GAC3Cd,+BAA+B,CAACY,iBAAiB,CAACX,KAAK,CAAC;IAC1D,MAAMc,UAAU,GAAG3B,qBAAqB,CAAC0B,cAAc,EAAE,IAAI,CAACE,YAAY,CAAC;IAE3E,IAAID,UAAU,EAAE;MACd,IAAI,CAAC,IAAI,CAACE,2BAA2B,EAAE;QACrC,IAAI,CAACA,2BAA2B,GAAG3C,sBAAsB,CAAC,CAAC;QAE3D,MAAM;UAAE4C,OAAO;UAAEC,QAAQ;UAAEC,iBAAiB;UAAEC;QAAW,CAAC,GAAGR,QAAQ;QAErE,IAAIxD,MAAM,CAACkC,IAAI,CAACuB,cAAc,CAAC,CAACtB,MAAM,IAAI6B,UAAU,EAAE;UACpD9C,eAAe,CAAC8C,UAAU,CAAC;QAC7B;QAEA,IAAI,CAACJ,2BAA2B,CAACK,GAAG,CAAC;UACnCC,GAAG,EAAEL,OAAiB;UACtBM,IAAI,EAAEL,QAAS;UACfC,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;MACA,MAAMK,wBAAwB,GAC5B,IAAI,CAACR,2BAA2B,CAACQ,wBAAwB;MAE3D,MAAMC,YAAY,GAAG7C,iBAAiB,GACjC;QAAE8C,KAAK,EAAE,IAAIC,GAAG,CAAC,CAAChB,iBAAiB,CAAC;MAAE,CAAC,CAAyB;MAAA,EACjEiB,SAAS;MACb,MAAMC,eAAe,GAAGA,CAAA,KAAM;QAC5B,SAAS;;QACT,MAAMnC,MAAM,GAAGF,oBAAoB,CAACqB,cAAc,CAAC;QACnDtC,WAAW,CAACiD,wBAAwB,EAAE9B,MAAM,EAAE+B,YAAY,CAAC;MAC7D,CAAC;MACD,IAAI,CAACV,YAAY,GAAGF,cAAc;MAClC,IAAI,IAAI,CAACiB,oBAAoB,EAAE;QAC7BtD,UAAU,CAAC,IAAI,CAACsD,oBAAoB,CAAC;MACvC;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAI1E,MAAM,CAACkC,IAAI,CAACuB,cAAc,CAAC,CAACtB,MAAM,EAAE;QACtC,IAAI,CAACuC,oBAAoB,GAAGrD,WAAW,CACrCoD,eAAe,EACfzE,MAAM,CAAC2E,MAAM,CAAClB,cAAc,CAC9B,CAAC;MACH;IACF;EACF;EAEOmB,iBAAiBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACF,oBAAoB,EAAE;MAC7BtD,UAAU,CAAC,IAAI,CAACsD,oBAAoB,CAAC;IACvC;EACF;AACF", "ignoreList": []}