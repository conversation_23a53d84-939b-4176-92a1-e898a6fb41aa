{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "isChromeDebugger", "isJest", "isWeb", "isWindowAvailable", "SensorType", "mockedRequestAnimationFrame", "requestAnimationFrameImpl", "globalThis", "requestAnimationFrame", "JSReanimated", "constructor", "Map", "undefined", "sensor", "sensorType", "<PERSON><PERSON><PERSON><PERSON>", "ACCELEROMETER", "GRAVITY", "x", "y", "z", "platform", "Platform", "WEB_ANDROID", "interfaceOrientation", "GYROSCOPE", "MAGNETIC_FIELD", "ROTATION", "qw", "qx", "qy", "qz", "quaternion", "yaw", "Math", "atan2", "pitch", "sin", "roll", "makeShareableClone", "Error", "scheduleOnUI", "worklet", "createWorkletRuntime", "_name", "_initializer", "scheduleOnRuntime", "registerEventHandler", "_event<PERSON><PERSON><PERSON>", "_eventName", "_emitterReactTag", "unregisterEventHandler", "_", "enableLayoutAnimations", "console", "warn", "configureLayoutAnimationBatch", "setShouldAnimateExitingForTag", "registerSensor", "interval", "_iosReferenceFrame", "detectPlatform", "getSensorName", "window", "location", "protocol", "WEB_IOS", "initializeSensor", "addEventListener", "getSensorCallback", "start", "sensors", "set", "nextSensorId", "unregisterSensor", "id", "get", "stop", "delete", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "config", "referenceFrame", "frequency", "Accelerometer", "Gyroscope", "GravitySensor", "Magnetometer", "AbsoluteOrientationSensor", "userAgent", "navigator", "vendor", "opera", "UNKNOWN", "test", "WEB", "getViewProp", "_viewTag", "_propName", "_component", "_callback", "configureProps", "executeOnUIRuntimeSync", "_shareable"], "sources": ["JSReanimated.ts"], "sourcesContent": ["'use strict';\nimport {\n  isChromeDebugger,\n  isJest,\n  isWeb,\n  isWindowAvailable,\n} from '../PlatformChecker';\nimport type { ShareableRef, Value3D, ValueRotation } from '../commonTypes';\nimport { SensorType } from '../commonTypes';\nimport type { WebSensor } from './WebSensor';\nimport { mockedRequestAnimationFrame } from '../mockedRequestAnimationFrame';\nimport type { WorkletRuntime } from '../runtimes';\n\n// In Node.js environments (like when static rendering with Expo Router)\n// requestAnimationFrame is unavailable, so we use our mock.\n// It also has to be mocked for Jest purposes (see `initializeUIRuntime`).\nconst requestAnimationFrameImpl =\n  isJest() || !globalThis.requestAnimationFrame\n    ? mockedRequestAnimationFrame\n    : globalThis.requestAnimationFrame;\n\nexport default class JSReanimated {\n  nextSensorId = 0;\n  sensors = new Map<number, WebSensor>();\n  platform?: Platform = undefined;\n\n  makeShareableClone<T>(): ShareableRef<T> {\n    throw new Error(\n      '[Reanimated] makeShareableClone should never be called in JSReanimated.'\n    );\n  }\n\n  scheduleOnUI<T>(worklet: ShareableRef<T>) {\n    // @ts-ignore web implementation has still not been updated after the rewrite, this will be addressed once the web implementation updates are ready\n    requestAnimationFrameImpl(worklet);\n  }\n\n  createWorkletRuntime(\n    _name: string,\n    _initializer: ShareableRef<() => void>\n  ): WorkletRuntime {\n    throw new Error(\n      '[Reanimated] createWorkletRuntime is not available in JSReanimated.'\n    );\n  }\n\n  scheduleOnRuntime() {\n    throw new Error(\n      '[Reanimated] scheduleOnRuntime is not available in JSReanimated.'\n    );\n  }\n\n  registerEventHandler<T>(\n    _eventHandler: ShareableRef<T>,\n    _eventName: string,\n    _emitterReactTag: number\n  ): number {\n    throw new Error(\n      '[Reanimated] registerEventHandler is not available in JSReanimated.'\n    );\n  }\n\n  unregisterEventHandler(_: number): void {\n    throw new Error(\n      '[Reanimated] unregisterEventHandler is not available in JSReanimated.'\n    );\n  }\n\n  enableLayoutAnimations() {\n    if (isWeb()) {\n      console.warn(\n        '[Reanimated] Layout Animations are not supported on web yet.'\n      );\n    } else if (isJest()) {\n      console.warn(\n        '[Reanimated] Layout Animations are no-ops when using Jest.'\n      );\n    } else if (isChromeDebugger()) {\n      console.warn(\n        '[Reanimated] Layout Animations are no-ops when using Chrome Debugger.'\n      );\n    } else {\n      console.warn(\n        '[Reanimated] Layout Animations are not supported on this configuration.'\n      );\n    }\n  }\n\n  configureLayoutAnimationBatch() {\n    // no-op\n  }\n\n  setShouldAnimateExitingForTag() {\n    // no-op\n  }\n\n  registerSensor(\n    sensorType: SensorType,\n    interval: number,\n    _iosReferenceFrame: number,\n    eventHandler: ShareableRef<(data: Value3D | ValueRotation) => void>\n  ): number {\n    if (!isWindowAvailable()) {\n      // the window object is unavailable when building the server portion of a site that uses SSG\n      // this check is here to ensure that the server build won't fail\n      return -1;\n    }\n\n    if (this.platform === undefined) {\n      this.detectPlatform();\n    }\n\n    if (!(this.getSensorName(sensorType) in window)) {\n      // https://w3c.github.io/sensors/#secure-context\n      console.warn(\n        '[Reanimated] Sensor is not available.' +\n          (isWeb() && location.protocol !== 'https:'\n            ? ' Make sure you use secure origin with `npx expo start --web --https`.'\n            : '') +\n          (this.platform === Platform.WEB_IOS\n            ? ' For iOS web, you will also have to also grant permission in the browser: https://dev.to/li/how-to-requestpermission-for-devicemotion-and-deviceorientation-events-in-ios-13-46g2.'\n            : '')\n      );\n      return -1;\n    }\n\n    if (this.platform === undefined) {\n      this.detectPlatform();\n    }\n\n    const sensor: WebSensor = this.initializeSensor(sensorType, interval);\n    sensor.addEventListener(\n      'reading',\n      this.getSensorCallback(sensor, sensorType, eventHandler)\n    );\n    sensor.start();\n\n    this.sensors.set(this.nextSensorId, sensor);\n    return this.nextSensorId++;\n  }\n\n  getSensorCallback = (\n    sensor: WebSensor,\n    sensorType: SensorType,\n    eventHandler: ShareableRef<(data: Value3D | ValueRotation) => void>\n  ) => {\n    switch (sensorType) {\n      case SensorType.ACCELEROMETER:\n      case SensorType.GRAVITY:\n        return () => {\n          let { x, y, z } = sensor;\n\n          // Web Android sensors have a different coordinate system than iOS\n          if (this.platform === Platform.WEB_ANDROID) {\n            [x, y, z] = [-x, -y, -z];\n          }\n          // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n          (eventHandler as any)({ x, y, z, interfaceOrientation: 0 });\n        };\n      case SensorType.GYROSCOPE:\n      case SensorType.MAGNETIC_FIELD:\n        return () => {\n          const { x, y, z } = sensor;\n          // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n          (eventHandler as any)({ x, y, z, interfaceOrientation: 0 });\n        };\n      case SensorType.ROTATION:\n        return () => {\n          let [qw, qx, qy, qz] = sensor.quaternion;\n\n          // Android sensors have a different coordinate system than iOS\n          if (this.platform === Platform.WEB_ANDROID) {\n            [qy, qz] = [qz, -qy];\n          }\n\n          // reference: https://stackoverflow.com/questions/5782658/extracting-yaw-from-a-quaternion\n          const yaw = -Math.atan2(\n            2.0 * (qy * qz + qw * qx),\n            qw * qw - qx * qx - qy * qy + qz * qz\n          );\n          const pitch = Math.sin(-2.0 * (qx * qz - qw * qy));\n          const roll = -Math.atan2(\n            2.0 * (qx * qy + qw * qz),\n            qw * qw + qx * qx - qy * qy - qz * qz\n          );\n          // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n          (eventHandler as any)({\n            qw,\n            qx,\n            qy,\n            qz,\n            yaw,\n            pitch,\n            roll,\n            interfaceOrientation: 0,\n          });\n        };\n    }\n  };\n\n  unregisterSensor(id: number): void {\n    const sensor: WebSensor | undefined = this.sensors.get(id);\n    if (sensor !== undefined) {\n      sensor.stop();\n      this.sensors.delete(id);\n    }\n  }\n\n  subscribeForKeyboardEvents(_: ShareableRef<number>): number {\n    if (isWeb()) {\n      console.warn(\n        '[Reanimated] useAnimatedKeyboard is not available on web yet.'\n      );\n    } else if (isJest()) {\n      console.warn(\n        '[Reanimated] useAnimatedKeyboard is not available when using Jest.'\n      );\n    } else if (isChromeDebugger()) {\n      console.warn(\n        '[Reanimated] useAnimatedKeyboard is not available when using Chrome Debugger.'\n      );\n    } else {\n      console.warn(\n        '[Reanimated] useAnimatedKeyboard is not available on this configuration.'\n      );\n    }\n    return -1;\n  }\n\n  unsubscribeFromKeyboardEvents(_: number): void {\n    // noop\n  }\n\n  initializeSensor(sensorType: SensorType, interval: number): WebSensor {\n    const config =\n      interval <= 0\n        ? { referenceFrame: 'device' }\n        : { frequency: 1000 / interval };\n    switch (sensorType) {\n      case SensorType.ACCELEROMETER:\n        return new window.Accelerometer(config);\n      case SensorType.GYROSCOPE:\n        return new window.Gyroscope(config);\n      case SensorType.GRAVITY:\n        return new window.GravitySensor(config);\n      case SensorType.MAGNETIC_FIELD:\n        return new window.Magnetometer(config);\n      case SensorType.ROTATION:\n        return new window.AbsoluteOrientationSensor(config);\n    }\n  }\n\n  getSensorName(sensorType: SensorType): string {\n    switch (sensorType) {\n      case SensorType.ACCELEROMETER:\n        return 'Accelerometer';\n      case SensorType.GRAVITY:\n        return 'GravitySensor';\n      case SensorType.GYROSCOPE:\n        return 'Gyroscope';\n      case SensorType.MAGNETIC_FIELD:\n        return 'Magnetometer';\n      case SensorType.ROTATION:\n        return 'AbsoluteOrientationSensor';\n    }\n  }\n\n  detectPlatform() {\n    const userAgent = navigator.userAgent || navigator.vendor || window.opera;\n    if (userAgent === undefined) {\n      this.platform = Platform.UNKNOWN;\n    } else if (/iPad|iPhone|iPod/.test(userAgent)) {\n      this.platform = Platform.WEB_IOS;\n    } else if (/android/i.test(userAgent)) {\n      this.platform = Platform.WEB_ANDROID;\n    } else {\n      this.platform = Platform.WEB;\n    }\n  }\n\n  getViewProp<T>(\n    _viewTag: number,\n    _propName: string,\n    _component?: React.Component,\n    _callback?: (result: T) => void\n  ): Promise<T> {\n    throw new Error(\n      '[Reanimated] getViewProp is not available in JSReanimated.'\n    );\n  }\n\n  configureProps() {\n    throw new Error(\n      '[Reanimated] configureProps is not available in JSReanimated.'\n    );\n  }\n\n  executeOnUIRuntimeSync<T, R>(_shareable: ShareableRef<T>): R {\n    throw new Error(\n      '[Reanimated] `executeOnUIRuntimeSync` is not available in JSReanimated.'\n    );\n  }\n}\n\nenum Platform {\n  WEB_IOS = 'web iOS',\n  WEB_ANDROID = 'web Android',\n  WEB = 'web',\n  UNKNOWN = 'unknown',\n}\n\ndeclare global {\n  interface Navigator {\n    userAgent?: string;\n    vendor?: string;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SACEW,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,iBAAiB,QACZ,oBAAoB;AAE3B,SAASC,UAAU,QAAQ,gBAAgB;AAE3C,SAASC,2BAA2B,QAAQ,gCAAgC;AAG5E;AACA;AACA;AACA,MAAMC,yBAAyB,GAC7BL,MAAM,CAAC,CAAC,IAAI,CAACM,UAAU,CAACC,qBAAqB,GACzCH,2BAA2B,GAC3BE,UAAU,CAACC,qBAAqB;AAEtC,eAAe,MAAMC,YAAY,CAAC;EAAAC,YAAA;IAAA/B,eAAA,uBACjB,CAAC;IAAAA,eAAA,kBACN,IAAIgC,GAAG,CAAoB,CAAC;IAAAhC,eAAA,mBAChBiC,SAAS;IAAAjC,eAAA,4BAqHX,CAClBkC,MAAiB,EACjBC,UAAsB,EACtBC,YAAmE,KAChE;MACH,QAAQD,UAAU;QAChB,KAAKV,UAAU,CAACY,aAAa;QAC7B,KAAKZ,UAAU,CAACa,OAAO;UACrB,OAAO,MAAM;YACX,IAAI;cAAEC,CAAC;cAAEC,CAAC;cAAEC;YAAE,CAAC,GAAGP,MAAM;;YAExB;YACA,IAAI,IAAI,CAACQ,QAAQ,KAAKC,QAAQ,CAACC,WAAW,EAAE;cAC1C,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,CAACF,CAAC,EAAE,CAACC,CAAC,EAAE,CAACC,CAAC,CAAC;YAC1B;YACA;YACCL,YAAY,CAAS;cAAEG,CAAC;cAAEC,CAAC;cAAEC,CAAC;cAAEI,oBAAoB,EAAE;YAAE,CAAC,CAAC;UAC7D,CAAC;QACH,KAAKpB,UAAU,CAACqB,SAAS;QACzB,KAAKrB,UAAU,CAACsB,cAAc;UAC5B,OAAO,MAAM;YACX,MAAM;cAAER,CAAC;cAAEC,CAAC;cAAEC;YAAE,CAAC,GAAGP,MAAM;YAC1B;YACCE,YAAY,CAAS;cAAEG,CAAC;cAAEC,CAAC;cAAEC,CAAC;cAAEI,oBAAoB,EAAE;YAAE,CAAC,CAAC;UAC7D,CAAC;QACH,KAAKpB,UAAU,CAACuB,QAAQ;UACtB,OAAO,MAAM;YACX,IAAI,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGlB,MAAM,CAACmB,UAAU;;YAExC;YACA,IAAI,IAAI,CAACX,QAAQ,KAAKC,QAAQ,CAACC,WAAW,EAAE;cAC1C,CAACO,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAE,CAACD,EAAE,CAAC;YACtB;;YAEA;YACA,MAAMG,GAAG,GAAG,CAACC,IAAI,CAACC,KAAK,CACrB,GAAG,IAAIL,EAAE,GAAGC,EAAE,GAAGH,EAAE,GAAGC,EAAE,CAAC,EACzBD,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EACrC,CAAC;YACD,MAAMK,KAAK,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,IAAIR,EAAE,GAAGE,EAAE,GAAGH,EAAE,GAAGE,EAAE,CAAC,CAAC;YAClD,MAAMQ,IAAI,GAAG,CAACJ,IAAI,CAACC,KAAK,CACtB,GAAG,IAAIN,EAAE,GAAGC,EAAE,GAAGF,EAAE,GAAGG,EAAE,CAAC,EACzBH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EACrC,CAAC;YACD;YACChB,YAAY,CAAS;cACpBa,EAAE;cACFC,EAAE;cACFC,EAAE;cACFC,EAAE;cACFE,GAAG;cACHG,KAAK;cACLE,IAAI;cACJd,oBAAoB,EAAE;YACxB,CAAC,CAAC;UACJ,CAAC;MACL;IACF,CAAC;EAAA;EA5KDe,kBAAkBA,CAAA,EAAuB;IACvC,MAAM,IAAIC,KAAK,CACb,yEACF,CAAC;EACH;EAEAC,YAAYA,CAAIC,OAAwB,EAAE;IACxC;IACApC,yBAAyB,CAACoC,OAAO,CAAC;EACpC;EAEAC,oBAAoBA,CAClBC,KAAa,EACbC,YAAsC,EACtB;IAChB,MAAM,IAAIL,KAAK,CACb,qEACF,CAAC;EACH;EAEAM,iBAAiBA,CAAA,EAAG;IAClB,MAAM,IAAIN,KAAK,CACb,kEACF,CAAC;EACH;EAEAO,oBAAoBA,CAClBC,aAA8B,EAC9BC,UAAkB,EAClBC,gBAAwB,EAChB;IACR,MAAM,IAAIV,KAAK,CACb,qEACF,CAAC;EACH;EAEAW,sBAAsBA,CAACC,CAAS,EAAQ;IACtC,MAAM,IAAIZ,KAAK,CACb,uEACF,CAAC;EACH;EAEAa,sBAAsBA,CAAA,EAAG;IACvB,IAAInD,KAAK,CAAC,CAAC,EAAE;MACXoD,OAAO,CAACC,IAAI,CACV,8DACF,CAAC;IACH,CAAC,MAAM,IAAItD,MAAM,CAAC,CAAC,EAAE;MACnBqD,OAAO,CAACC,IAAI,CACV,4DACF,CAAC;IACH,CAAC,MAAM,IAAIvD,gBAAgB,CAAC,CAAC,EAAE;MAC7BsD,OAAO,CAACC,IAAI,CACV,uEACF,CAAC;IACH,CAAC,MAAM;MACLD,OAAO,CAACC,IAAI,CACV,yEACF,CAAC;IACH;EACF;EAEAC,6BAA6BA,CAAA,EAAG;IAC9B;EAAA;EAGFC,6BAA6BA,CAAA,EAAG;IAC9B;EAAA;EAGFC,cAAcA,CACZ5C,UAAsB,EACtB6C,QAAgB,EAChBC,kBAA0B,EAC1B7C,YAAmE,EAC3D;IACR,IAAI,CAACZ,iBAAiB,CAAC,CAAC,EAAE;MACxB;MACA;MACA,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,IAAI,CAACkB,QAAQ,KAAKT,SAAS,EAAE;MAC/B,IAAI,CAACiD,cAAc,CAAC,CAAC;IACvB;IAEA,IAAI,EAAE,IAAI,CAACC,aAAa,CAAChD,UAAU,CAAC,IAAIiD,MAAM,CAAC,EAAE;MAC/C;MACAT,OAAO,CAACC,IAAI,CACV,uCAAuC,IACpCrD,KAAK,CAAC,CAAC,IAAI8D,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GACtC,uEAAuE,GACvE,EAAE,CAAC,IACN,IAAI,CAAC5C,QAAQ,KAAKC,QAAQ,CAAC4C,OAAO,GAC/B,oLAAoL,GACpL,EAAE,CACV,CAAC;MACD,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,IAAI,CAAC7C,QAAQ,KAAKT,SAAS,EAAE;MAC/B,IAAI,CAACiD,cAAc,CAAC,CAAC;IACvB;IAEA,MAAMhD,MAAiB,GAAG,IAAI,CAACsD,gBAAgB,CAACrD,UAAU,EAAE6C,QAAQ,CAAC;IACrE9C,MAAM,CAACuD,gBAAgB,CACrB,SAAS,EACT,IAAI,CAACC,iBAAiB,CAACxD,MAAM,EAAEC,UAAU,EAAEC,YAAY,CACzD,CAAC;IACDF,MAAM,CAACyD,KAAK,CAAC,CAAC;IAEd,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,YAAY,EAAE5D,MAAM,CAAC;IAC3C,OAAO,IAAI,CAAC4D,YAAY,EAAE;EAC5B;EA6DAC,gBAAgBA,CAACC,EAAU,EAAQ;IACjC,MAAM9D,MAA6B,GAAG,IAAI,CAAC0D,OAAO,CAACK,GAAG,CAACD,EAAE,CAAC;IAC1D,IAAI9D,MAAM,KAAKD,SAAS,EAAE;MACxBC,MAAM,CAACgE,IAAI,CAAC,CAAC;MACb,IAAI,CAACN,OAAO,CAACO,MAAM,CAACH,EAAE,CAAC;IACzB;EACF;EAEAI,0BAA0BA,CAAC3B,CAAuB,EAAU;IAC1D,IAAIlD,KAAK,CAAC,CAAC,EAAE;MACXoD,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACH,CAAC,MAAM,IAAItD,MAAM,CAAC,CAAC,EAAE;MACnBqD,OAAO,CAACC,IAAI,CACV,oEACF,CAAC;IACH,CAAC,MAAM,IAAIvD,gBAAgB,CAAC,CAAC,EAAE;MAC7BsD,OAAO,CAACC,IAAI,CACV,+EACF,CAAC;IACH,CAAC,MAAM;MACLD,OAAO,CAACC,IAAI,CACV,0EACF,CAAC;IACH;IACA,OAAO,CAAC,CAAC;EACX;EAEAyB,6BAA6BA,CAAC5B,CAAS,EAAQ;IAC7C;EAAA;EAGFe,gBAAgBA,CAACrD,UAAsB,EAAE6C,QAAgB,EAAa;IACpE,MAAMsB,MAAM,GACVtB,QAAQ,IAAI,CAAC,GACT;MAAEuB,cAAc,EAAE;IAAS,CAAC,GAC5B;MAAEC,SAAS,EAAE,IAAI,GAAGxB;IAAS,CAAC;IACpC,QAAQ7C,UAAU;MAChB,KAAKV,UAAU,CAACY,aAAa;QAC3B,OAAO,IAAI+C,MAAM,CAACqB,aAAa,CAACH,MAAM,CAAC;MACzC,KAAK7E,UAAU,CAACqB,SAAS;QACvB,OAAO,IAAIsC,MAAM,CAACsB,SAAS,CAACJ,MAAM,CAAC;MACrC,KAAK7E,UAAU,CAACa,OAAO;QACrB,OAAO,IAAI8C,MAAM,CAACuB,aAAa,CAACL,MAAM,CAAC;MACzC,KAAK7E,UAAU,CAACsB,cAAc;QAC5B,OAAO,IAAIqC,MAAM,CAACwB,YAAY,CAACN,MAAM,CAAC;MACxC,KAAK7E,UAAU,CAACuB,QAAQ;QACtB,OAAO,IAAIoC,MAAM,CAACyB,yBAAyB,CAACP,MAAM,CAAC;IACvD;EACF;EAEAnB,aAAaA,CAAChD,UAAsB,EAAU;IAC5C,QAAQA,UAAU;MAChB,KAAKV,UAAU,CAACY,aAAa;QAC3B,OAAO,eAAe;MACxB,KAAKZ,UAAU,CAACa,OAAO;QACrB,OAAO,eAAe;MACxB,KAAKb,UAAU,CAACqB,SAAS;QACvB,OAAO,WAAW;MACpB,KAAKrB,UAAU,CAACsB,cAAc;QAC5B,OAAO,cAAc;MACvB,KAAKtB,UAAU,CAACuB,QAAQ;QACtB,OAAO,2BAA2B;IACtC;EACF;EAEAkC,cAAcA,CAAA,EAAG;IACf,MAAM4B,SAAS,GAAGC,SAAS,CAACD,SAAS,IAAIC,SAAS,CAACC,MAAM,IAAI5B,MAAM,CAAC6B,KAAK;IACzE,IAAIH,SAAS,KAAK7E,SAAS,EAAE;MAC3B,IAAI,CAACS,QAAQ,GAAGC,QAAQ,CAACuE,OAAO;IAClC,CAAC,MAAM,IAAI,kBAAkB,CAACC,IAAI,CAACL,SAAS,CAAC,EAAE;MAC7C,IAAI,CAACpE,QAAQ,GAAGC,QAAQ,CAAC4C,OAAO;IAClC,CAAC,MAAM,IAAI,UAAU,CAAC4B,IAAI,CAACL,SAAS,CAAC,EAAE;MACrC,IAAI,CAACpE,QAAQ,GAAGC,QAAQ,CAACC,WAAW;IACtC,CAAC,MAAM;MACL,IAAI,CAACF,QAAQ,GAAGC,QAAQ,CAACyE,GAAG;IAC9B;EACF;EAEAC,WAAWA,CACTC,QAAgB,EAChBC,SAAiB,EACjBC,UAA4B,EAC5BC,SAA+B,EACnB;IACZ,MAAM,IAAI5D,KAAK,CACb,4DACF,CAAC;EACH;EAEA6D,cAAcA,CAAA,EAAG;IACf,MAAM,IAAI7D,KAAK,CACb,+DACF,CAAC;EACH;EAEA8D,sBAAsBA,CAAOC,UAA2B,EAAK;IAC3D,MAAM,IAAI/D,KAAK,CACb,yEACF,CAAC;EACH;AACF;AAAC,IAEIlB,QAAQ,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA,EAARA,QAAQ", "ignoreList": []}