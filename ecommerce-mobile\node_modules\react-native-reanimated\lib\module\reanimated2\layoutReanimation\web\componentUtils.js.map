{"version": 3, "names": ["Animations", "TransitionType", "WebEasings", "convertTransformToString", "TransitionGenerator", "scheduleAnimationCleanup", "_updatePropsJS", "ReduceMotion", "isReducedMotion", "LayoutAnimationType", "setDummyPosition", "snapshots", "getEasingFromConfig", "config", "easingName", "easingV", "name", "toString", "getRandomDelay", "max<PERSON><PERSON><PERSON>", "Math", "floor", "random", "getDelayFromConfig", "shouldRandomizeDelay", "randomizeDelay", "delay", "delayV", "getReducedMotionFromConfig", "reduceMotionV", "Never", "Always", "getDurationFromConfig", "isLayoutTransition", "animationName", "defaultDuration", "duration", "durationV", "undefined", "getCallbackFromConfig", "callbackV", "getReversedFromConfig", "reversed", "extractTransformFromStyle", "style", "transform", "Error", "Array", "isArray", "i", "length", "_style$i", "getProcessedConfig", "animationType", "initialAnimationName", "LAYOUT", "easing", "callback", "saveSnapshot", "element", "rect", "getBoundingClientRect", "snapshot", "top", "left", "width", "height", "scrollOffsets", "getElementScrollValue", "set", "setElementAnimation", "animationConfig", "existingTransform", "animationDuration", "animationDelay", "animationTimingFunction", "onanimationend", "_animationConfig$call", "call", "removeEventListener", "animationCancelHandler", "_animationConfig$call2", "onanimationstart", "ENTERING", "visibility", "_component", "addEventListener", "handleLayoutTransition", "transitionData", "LINEAR", "SEQUENCED", "FADING", "transformCopy", "structuredClone", "push", "current", "scrollTopOffset", "scrollLeftOffset", "scrollTop", "scrollLeft", "parentElement", "handleExitingAnimation", "parent", "offsetParent", "dummy", "cloneNode", "reanimatedDummy", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "get", "currentScrollTopOffset", "lastScrollTopOffset", "currentScrollLeftOffset", "lastScrollLeftOffset", "originalOnAnimationEnd", "event", "contains", "removedAfterAnimation", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["componentUtils.ts"], "sourcesContent": ["'use strict';\n\nimport type { TransformsStyle } from 'react-native';\nimport { Animations, TransitionType, WebEasings } from './config';\nimport type {\n  AnimationCallback,\n  AnimationConfig,\n  AnimationNames,\n  CustomConfig,\n  WebEasingsNames,\n} from './config';\nimport { convertTransformToString } from './animationParser';\nimport type { TransitionData } from './animationParser';\nimport { TransitionGenerator } from './createAnimation';\nimport { scheduleAnimationCleanup } from './domUtils';\nimport { _updatePropsJS } from '../../js-reanimated';\nimport type { ReanimatedHTMLElement } from '../../js-reanimated';\nimport { ReduceMotion } from '../../commonTypes';\nimport type { StyleProps } from '../../commonTypes';\nimport { isReducedMotion } from '../../PlatformChecker';\nimport { LayoutAnimationType } from '../animationBuilder/commonTypes';\nimport type { ReanimatedSnapshot, ScrollOffsets } from './componentStyle';\nimport { setDummyPosition, snapshots } from './componentStyle';\n\nfunction getEasingFromConfig(config: CustomConfig): string {\n  const easingName =\n    config.easingV && config.easingV.name in WebEasings\n      ? (config.easingV.name as WebEasingsNames)\n      : 'linear';\n\n  return `cubic-bezier(${WebEasings[easingName].toString()})`;\n}\n\nfunction getRandomDelay(maxDelay = 1000) {\n  return Math.floor(Math.random() * (maxDelay + 1)) / 1000;\n}\n\nfunction getDelayFromConfig(config: CustomConfig): number {\n  const shouldRandomizeDelay = config.randomizeDelay;\n\n  const delay = shouldRandomizeDelay ? getRandomDelay() : 0;\n\n  if (!config.delayV) {\n    return delay;\n  }\n\n  return shouldRandomizeDelay\n    ? getRandomDelay(config.delayV)\n    : config.delayV / 1000;\n}\n\nexport function getReducedMotionFromConfig(config: CustomConfig) {\n  if (!config.reduceMotionV) {\n    return isReducedMotion();\n  }\n\n  switch (config.reduceMotionV) {\n    case ReduceMotion.Never:\n      return false;\n    case ReduceMotion.Always:\n      return true;\n    default:\n      return isReducedMotion();\n  }\n}\n\nfunction getDurationFromConfig(\n  config: CustomConfig,\n  isLayoutTransition: boolean,\n  animationName: AnimationNames\n): number {\n  const defaultDuration = isLayoutTransition\n    ? 0.3\n    : Animations[animationName].duration;\n\n  return config.durationV !== undefined\n    ? config.durationV / 1000\n    : defaultDuration;\n}\n\nfunction getCallbackFromConfig(config: CustomConfig): AnimationCallback {\n  return config.callbackV !== undefined ? config.callbackV : null;\n}\n\nfunction getReversedFromConfig(config: CustomConfig) {\n  return !!config.reversed;\n}\n\nexport function extractTransformFromStyle(style: StyleProps) {\n  if (!style) {\n    return;\n  }\n\n  if (typeof style.transform === 'string') {\n    throw new Error('[Reanimated] String transform is currently unsupported.');\n  }\n\n  if (!Array.isArray(style)) {\n    return style.transform;\n  }\n\n  // Only last transform should be considered\n  for (let i = style.length - 1; i >= 0; --i) {\n    if (style[i]?.transform) {\n      return style[i].transform;\n    }\n  }\n}\n\nexport function getProcessedConfig(\n  animationName: string,\n  animationType: LayoutAnimationType,\n  config: CustomConfig,\n  initialAnimationName: AnimationNames\n): AnimationConfig {\n  return {\n    animationName,\n    animationType,\n    duration: getDurationFromConfig(\n      config,\n      animationType === LayoutAnimationType.LAYOUT,\n      initialAnimationName\n    ),\n    delay: getDelayFromConfig(config),\n    easing: getEasingFromConfig(config),\n    callback: getCallbackFromConfig(config),\n    reversed: getReversedFromConfig(config),\n  };\n}\n\nexport function saveSnapshot(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n\n  const snapshot: ReanimatedSnapshot = {\n    top: rect.top,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    scrollOffsets: getElementScrollValue(element),\n  };\n\n  snapshots.set(element, snapshot);\n}\n\nexport function setElementAnimation(\n  element: HTMLElement,\n  animationConfig: AnimationConfig,\n  existingTransform?: TransformsStyle['transform']\n) {\n  const { animationName, duration, delay, easing } = animationConfig;\n\n  element.style.animationName = animationName;\n  element.style.animationDuration = `${duration}s`;\n  element.style.animationDelay = `${delay}s`;\n  element.style.animationTimingFunction = easing;\n\n  element.onanimationend = () => {\n    animationConfig.callback?.(true);\n    element.removeEventListener('animationcancel', animationCancelHandler);\n  };\n\n  const animationCancelHandler = () => {\n    animationConfig.callback?.(false);\n    element.removeEventListener('animationcancel', animationCancelHandler);\n  };\n\n  // Here we have to use `addEventListener` since element.onanimationcancel doesn't work on chrome\n  element.onanimationstart = () => {\n    if (animationConfig.animationType === LayoutAnimationType.ENTERING) {\n      _updatePropsJS(\n        { visibility: 'initial' },\n        { _component: element as ReanimatedHTMLElement }\n      );\n    }\n\n    element.addEventListener('animationcancel', animationCancelHandler);\n    element.style.transform = convertTransformToString(existingTransform);\n  };\n\n  if (!(animationName in Animations)) {\n    scheduleAnimationCleanup(animationName, duration + delay);\n  }\n}\n\nexport function handleLayoutTransition(\n  element: HTMLElement,\n  animationConfig: AnimationConfig,\n  transitionData: TransitionData,\n  existingTransform: TransformsStyle['transform'] | undefined\n) {\n  const { animationName } = animationConfig;\n\n  let animationType;\n\n  switch (animationName) {\n    case 'LinearTransition':\n      animationType = TransitionType.LINEAR;\n      break;\n    case 'SequencedTransition':\n      animationType = TransitionType.SEQUENCED;\n      break;\n    case 'FadingTransition':\n      animationType = TransitionType.FADING;\n      break;\n    default:\n      animationType = TransitionType.LINEAR;\n      break;\n  }\n\n  animationConfig.animationName = TransitionGenerator(\n    animationType,\n    transitionData,\n    existingTransform\n  );\n\n  const transformCopy = existingTransform\n    ? structuredClone(existingTransform)\n    : [];\n\n  // @ts-ignore `existingTransform` cannot be string because in that case\n  // we throw error in `extractTransformFromStyle`\n  transformCopy.push(transitionData);\n  element.style.transform = convertTransformToString(transformCopy);\n\n  setElementAnimation(element, animationConfig, existingTransform);\n}\n\nfunction getElementScrollValue(element: HTMLElement): ScrollOffsets {\n  let current: HTMLElement | null = element;\n\n  const scrollOffsets: ScrollOffsets = {\n    scrollTopOffset: 0,\n    scrollLeftOffset: 0,\n  };\n\n  while (current) {\n    if (current.scrollTop !== 0 && scrollOffsets.scrollTopOffset === 0) {\n      scrollOffsets.scrollTopOffset = current.scrollTop;\n    }\n\n    if (current.scrollLeft !== 0 && scrollOffsets.scrollLeftOffset === 0) {\n      scrollOffsets.scrollLeftOffset = current.scrollLeft;\n    }\n\n    current = current.parentElement;\n  }\n\n  return scrollOffsets;\n}\n\nexport function handleExitingAnimation(\n  element: HTMLElement,\n  animationConfig: AnimationConfig\n) {\n  const parent = element.offsetParent;\n  const dummy = element.cloneNode() as ReanimatedHTMLElement;\n  dummy.reanimatedDummy = true;\n\n  element.style.animationName = '';\n  // We hide current element so only its copy with proper animation will be displayed\n  element.style.visibility = 'hidden';\n\n  // After cloning the element, we want to move all children from original element to its clone. This is because original element\n  // will be unmounted, therefore when this code executes in child component, parent will be either empty or removed soon.\n  // Using element.cloneNode(true) doesn't solve the problem, because it creates copy of children and we won't be able to set their animations\n  //\n  // This loop works because appendChild() moves element into its new parent instead of copying it\n  while (element.firstChild) {\n    dummy.appendChild(element.firstChild);\n  }\n\n  setElementAnimation(dummy, animationConfig);\n  parent?.appendChild(dummy);\n\n  const snapshot = snapshots.get(element)!;\n\n  const scrollOffsets = getElementScrollValue(element);\n\n  // Scroll does not trigger snapshotting, therefore if we start exiting animation after\n  // scrolling through parent component, dummy will end up in wrong place. In order to fix that\n  // we keep last known scroll position in snapshot and then adjust dummy position based on\n  // last known scroll offset and current scroll offset\n\n  const currentScrollTopOffset = scrollOffsets.scrollTopOffset;\n  const lastScrollTopOffset = snapshot.scrollOffsets.scrollTopOffset;\n\n  if (currentScrollTopOffset !== lastScrollTopOffset) {\n    snapshot.top += lastScrollTopOffset - currentScrollTopOffset;\n  }\n\n  const currentScrollLeftOffset = scrollOffsets.scrollLeftOffset;\n  const lastScrollLeftOffset = snapshot.scrollOffsets.scrollLeftOffset;\n\n  if (currentScrollLeftOffset !== lastScrollLeftOffset) {\n    snapshot.left += lastScrollLeftOffset - currentScrollLeftOffset;\n  }\n\n  snapshots.set(dummy, snapshot);\n\n  setDummyPosition(dummy, snapshot);\n\n  const originalOnAnimationEnd = dummy.onanimationend;\n\n  dummy.onanimationend = function (event: AnimationEvent) {\n    if (parent?.contains(dummy)) {\n      dummy.removedAfterAnimation = true;\n      parent.removeChild(dummy);\n    }\n\n    // Given that this function overrides onAnimationEnd, it won't be null\n    originalOnAnimationEnd?.call(this, event);\n  };\n\n  dummy.addEventListener('animationcancel', () => {\n    if (parent?.contains(dummy)) {\n      dummy.removedAfterAnimation = true;\n      parent.removeChild(dummy);\n    }\n  });\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,UAAU,EAAEC,cAAc,EAAEC,UAAU,QAAQ,UAAU;AAQjE,SAASC,wBAAwB,QAAQ,mBAAmB;AAE5D,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,wBAAwB,QAAQ,YAAY;AACrD,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,mBAAmB,QAAQ,iCAAiC;AAErE,SAASC,gBAAgB,EAAEC,SAAS,QAAQ,kBAAkB;AAE9D,SAASC,mBAAmBA,CAACC,MAAoB,EAAU;EACzD,MAAMC,UAAU,GACdD,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAId,UAAU,GAC9CW,MAAM,CAACE,OAAO,CAACC,IAAI,GACpB,QAAQ;EAEd,OAAQ,gBAAed,UAAU,CAACY,UAAU,CAAC,CAACG,QAAQ,CAAC,CAAE,GAAE;AAC7D;AAEA,SAASC,cAAcA,CAACC,QAAQ,GAAG,IAAI,EAAE;EACvC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAIH,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;AAC1D;AAEA,SAASI,kBAAkBA,CAACV,MAAoB,EAAU;EACxD,MAAMW,oBAAoB,GAAGX,MAAM,CAACY,cAAc;EAElD,MAAMC,KAAK,GAAGF,oBAAoB,GAAGN,cAAc,CAAC,CAAC,GAAG,CAAC;EAEzD,IAAI,CAACL,MAAM,CAACc,MAAM,EAAE;IAClB,OAAOD,KAAK;EACd;EAEA,OAAOF,oBAAoB,GACvBN,cAAc,CAACL,MAAM,CAACc,MAAM,CAAC,GAC7Bd,MAAM,CAACc,MAAM,GAAG,IAAI;AAC1B;AAEA,OAAO,SAASC,0BAA0BA,CAACf,MAAoB,EAAE;EAC/D,IAAI,CAACA,MAAM,CAACgB,aAAa,EAAE;IACzB,OAAOrB,eAAe,CAAC,CAAC;EAC1B;EAEA,QAAQK,MAAM,CAACgB,aAAa;IAC1B,KAAKtB,YAAY,CAACuB,KAAK;MACrB,OAAO,KAAK;IACd,KAAKvB,YAAY,CAACwB,MAAM;MACtB,OAAO,IAAI;IACb;MACE,OAAOvB,eAAe,CAAC,CAAC;EAC5B;AACF;AAEA,SAASwB,qBAAqBA,CAC5BnB,MAAoB,EACpBoB,kBAA2B,EAC3BC,aAA6B,EACrB;EACR,MAAMC,eAAe,GAAGF,kBAAkB,GACtC,GAAG,GACHjC,UAAU,CAACkC,aAAa,CAAC,CAACE,QAAQ;EAEtC,OAAOvB,MAAM,CAACwB,SAAS,KAAKC,SAAS,GACjCzB,MAAM,CAACwB,SAAS,GAAG,IAAI,GACvBF,eAAe;AACrB;AAEA,SAASI,qBAAqBA,CAAC1B,MAAoB,EAAqB;EACtE,OAAOA,MAAM,CAAC2B,SAAS,KAAKF,SAAS,GAAGzB,MAAM,CAAC2B,SAAS,GAAG,IAAI;AACjE;AAEA,SAASC,qBAAqBA,CAAC5B,MAAoB,EAAE;EACnD,OAAO,CAAC,CAACA,MAAM,CAAC6B,QAAQ;AAC1B;AAEA,OAAO,SAASC,yBAAyBA,CAACC,KAAiB,EAAE;EAC3D,IAAI,CAACA,KAAK,EAAE;IACV;EACF;EAEA,IAAI,OAAOA,KAAK,CAACC,SAAS,KAAK,QAAQ,EAAE;IACvC,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;IACzB,OAAOA,KAAK,CAACC,SAAS;EACxB;;EAEA;EACA,KAAK,IAAII,CAAC,GAAGL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAAA,IAAAE,QAAA;IAC1C,KAAAA,QAAA,GAAIP,KAAK,CAACK,CAAC,CAAC,cAAAE,QAAA,eAARA,QAAA,CAAUN,SAAS,EAAE;MACvB,OAAOD,KAAK,CAACK,CAAC,CAAC,CAACJ,SAAS;IAC3B;EACF;AACF;AAEA,OAAO,SAASO,kBAAkBA,CAChClB,aAAqB,EACrBmB,aAAkC,EAClCxC,MAAoB,EACpByC,oBAAoC,EACnB;EACjB,OAAO;IACLpB,aAAa;IACbmB,aAAa;IACbjB,QAAQ,EAAEJ,qBAAqB,CAC7BnB,MAAM,EACNwC,aAAa,KAAK5C,mBAAmB,CAAC8C,MAAM,EAC5CD,oBACF,CAAC;IACD5B,KAAK,EAAEH,kBAAkB,CAACV,MAAM,CAAC;IACjC2C,MAAM,EAAE5C,mBAAmB,CAACC,MAAM,CAAC;IACnC4C,QAAQ,EAAElB,qBAAqB,CAAC1B,MAAM,CAAC;IACvC6B,QAAQ,EAAED,qBAAqB,CAAC5B,MAAM;EACxC,CAAC;AACH;AAEA,OAAO,SAAS6C,YAAYA,CAACC,OAAoB,EAAE;EACjD,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;EAE5C,MAAMC,QAA4B,GAAG;IACnCC,GAAG,EAAEH,IAAI,CAACG,GAAG;IACbC,IAAI,EAAEJ,IAAI,CAACI,IAAI;IACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;IACjBC,MAAM,EAAEN,IAAI,CAACM,MAAM;IACnBC,aAAa,EAAEC,qBAAqB,CAACT,OAAO;EAC9C,CAAC;EAEDhD,SAAS,CAAC0D,GAAG,CAACV,OAAO,EAAEG,QAAQ,CAAC;AAClC;AAEA,OAAO,SAASQ,mBAAmBA,CACjCX,OAAoB,EACpBY,eAAgC,EAChCC,iBAAgD,EAChD;EACA,MAAM;IAAEtC,aAAa;IAAEE,QAAQ;IAAEV,KAAK;IAAE8B;EAAO,CAAC,GAAGe,eAAe;EAElEZ,OAAO,CAACf,KAAK,CAACV,aAAa,GAAGA,aAAa;EAC3CyB,OAAO,CAACf,KAAK,CAAC6B,iBAAiB,GAAI,GAAErC,QAAS,GAAE;EAChDuB,OAAO,CAACf,KAAK,CAAC8B,cAAc,GAAI,GAAEhD,KAAM,GAAE;EAC1CiC,OAAO,CAACf,KAAK,CAAC+B,uBAAuB,GAAGnB,MAAM;EAE9CG,OAAO,CAACiB,cAAc,GAAG,MAAM;IAAA,IAAAC,qBAAA;IAC7B,CAAAA,qBAAA,GAAAN,eAAe,CAACd,QAAQ,cAAAoB,qBAAA,eAAxBA,qBAAA,CAAAC,IAAA,CAAAP,eAAe,EAAY,IAAI,CAAC;IAChCZ,OAAO,CAACoB,mBAAmB,CAAC,iBAAiB,EAAEC,sBAAsB,CAAC;EACxE,CAAC;EAED,MAAMA,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA;IACnC,CAAAA,sBAAA,GAAAV,eAAe,CAACd,QAAQ,cAAAwB,sBAAA,eAAxBA,sBAAA,CAAAH,IAAA,CAAAP,eAAe,EAAY,KAAK,CAAC;IACjCZ,OAAO,CAACoB,mBAAmB,CAAC,iBAAiB,EAAEC,sBAAsB,CAAC;EACxE,CAAC;;EAED;EACArB,OAAO,CAACuB,gBAAgB,GAAG,MAAM;IAC/B,IAAIX,eAAe,CAAClB,aAAa,KAAK5C,mBAAmB,CAAC0E,QAAQ,EAAE;MAClE7E,cAAc,CACZ;QAAE8E,UAAU,EAAE;MAAU,CAAC,EACzB;QAAEC,UAAU,EAAE1B;MAAiC,CACjD,CAAC;IACH;IAEAA,OAAO,CAAC2B,gBAAgB,CAAC,iBAAiB,EAAEN,sBAAsB,CAAC;IACnErB,OAAO,CAACf,KAAK,CAACC,SAAS,GAAG1C,wBAAwB,CAACqE,iBAAiB,CAAC;EACvE,CAAC;EAED,IAAI,EAAEtC,aAAa,IAAIlC,UAAU,CAAC,EAAE;IAClCK,wBAAwB,CAAC6B,aAAa,EAAEE,QAAQ,GAAGV,KAAK,CAAC;EAC3D;AACF;AAEA,OAAO,SAAS6D,sBAAsBA,CACpC5B,OAAoB,EACpBY,eAAgC,EAChCiB,cAA8B,EAC9BhB,iBAA2D,EAC3D;EACA,MAAM;IAAEtC;EAAc,CAAC,GAAGqC,eAAe;EAEzC,IAAIlB,aAAa;EAEjB,QAAQnB,aAAa;IACnB,KAAK,kBAAkB;MACrBmB,aAAa,GAAGpD,cAAc,CAACwF,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxBpC,aAAa,GAAGpD,cAAc,CAACyF,SAAS;MACxC;IACF,KAAK,kBAAkB;MACrBrC,aAAa,GAAGpD,cAAc,CAAC0F,MAAM;MACrC;IACF;MACEtC,aAAa,GAAGpD,cAAc,CAACwF,MAAM;MACrC;EACJ;EAEAlB,eAAe,CAACrC,aAAa,GAAG9B,mBAAmB,CACjDiD,aAAa,EACbmC,cAAc,EACdhB,iBACF,CAAC;EAED,MAAMoB,aAAa,GAAGpB,iBAAiB,GACnCqB,eAAe,CAACrB,iBAAiB,CAAC,GAClC,EAAE;;EAEN;EACA;EACAoB,aAAa,CAACE,IAAI,CAACN,cAAc,CAAC;EAClC7B,OAAO,CAACf,KAAK,CAACC,SAAS,GAAG1C,wBAAwB,CAACyF,aAAa,CAAC;EAEjEtB,mBAAmB,CAACX,OAAO,EAAEY,eAAe,EAAEC,iBAAiB,CAAC;AAClE;AAEA,SAASJ,qBAAqBA,CAACT,OAAoB,EAAiB;EAClE,IAAIoC,OAA2B,GAAGpC,OAAO;EAEzC,MAAMQ,aAA4B,GAAG;IACnC6B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAED,OAAOF,OAAO,EAAE;IACd,IAAIA,OAAO,CAACG,SAAS,KAAK,CAAC,IAAI/B,aAAa,CAAC6B,eAAe,KAAK,CAAC,EAAE;MAClE7B,aAAa,CAAC6B,eAAe,GAAGD,OAAO,CAACG,SAAS;IACnD;IAEA,IAAIH,OAAO,CAACI,UAAU,KAAK,CAAC,IAAIhC,aAAa,CAAC8B,gBAAgB,KAAK,CAAC,EAAE;MACpE9B,aAAa,CAAC8B,gBAAgB,GAAGF,OAAO,CAACI,UAAU;IACrD;IAEAJ,OAAO,GAAGA,OAAO,CAACK,aAAa;EACjC;EAEA,OAAOjC,aAAa;AACtB;AAEA,OAAO,SAASkC,sBAAsBA,CACpC1C,OAAoB,EACpBY,eAAgC,EAChC;EACA,MAAM+B,MAAM,GAAG3C,OAAO,CAAC4C,YAAY;EACnC,MAAMC,KAAK,GAAG7C,OAAO,CAAC8C,SAAS,CAAC,CAA0B;EAC1DD,KAAK,CAACE,eAAe,GAAG,IAAI;EAE5B/C,OAAO,CAACf,KAAK,CAACV,aAAa,GAAG,EAAE;EAChC;EACAyB,OAAO,CAACf,KAAK,CAACwC,UAAU,GAAG,QAAQ;;EAEnC;EACA;EACA;EACA;EACA;EACA,OAAOzB,OAAO,CAACgD,UAAU,EAAE;IACzBH,KAAK,CAACI,WAAW,CAACjD,OAAO,CAACgD,UAAU,CAAC;EACvC;EAEArC,mBAAmB,CAACkC,KAAK,EAAEjC,eAAe,CAAC;EAC3C+B,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEM,WAAW,CAACJ,KAAK,CAAC;EAE1B,MAAM1C,QAAQ,GAAGnD,SAAS,CAACkG,GAAG,CAAClD,OAAO,CAAE;EAExC,MAAMQ,aAAa,GAAGC,qBAAqB,CAACT,OAAO,CAAC;;EAEpD;EACA;EACA;EACA;;EAEA,MAAMmD,sBAAsB,GAAG3C,aAAa,CAAC6B,eAAe;EAC5D,MAAMe,mBAAmB,GAAGjD,QAAQ,CAACK,aAAa,CAAC6B,eAAe;EAElE,IAAIc,sBAAsB,KAAKC,mBAAmB,EAAE;IAClDjD,QAAQ,CAACC,GAAG,IAAIgD,mBAAmB,GAAGD,sBAAsB;EAC9D;EAEA,MAAME,uBAAuB,GAAG7C,aAAa,CAAC8B,gBAAgB;EAC9D,MAAMgB,oBAAoB,GAAGnD,QAAQ,CAACK,aAAa,CAAC8B,gBAAgB;EAEpE,IAAIe,uBAAuB,KAAKC,oBAAoB,EAAE;IACpDnD,QAAQ,CAACE,IAAI,IAAIiD,oBAAoB,GAAGD,uBAAuB;EACjE;EAEArG,SAAS,CAAC0D,GAAG,CAACmC,KAAK,EAAE1C,QAAQ,CAAC;EAE9BpD,gBAAgB,CAAC8F,KAAK,EAAE1C,QAAQ,CAAC;EAEjC,MAAMoD,sBAAsB,GAAGV,KAAK,CAAC5B,cAAc;EAEnD4B,KAAK,CAAC5B,cAAc,GAAG,UAAUuC,KAAqB,EAAE;IACtD,IAAIb,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEc,QAAQ,CAACZ,KAAK,CAAC,EAAE;MAC3BA,KAAK,CAACa,qBAAqB,GAAG,IAAI;MAClCf,MAAM,CAACgB,WAAW,CAACd,KAAK,CAAC;IAC3B;;IAEA;IACAU,sBAAsB,aAAtBA,sBAAsB,eAAtBA,sBAAsB,CAAEpC,IAAI,CAAC,IAAI,EAAEqC,KAAK,CAAC;EAC3C,CAAC;EAEDX,KAAK,CAAClB,gBAAgB,CAAC,iBAAiB,EAAE,MAAM;IAC9C,IAAIgB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEc,QAAQ,CAACZ,KAAK,CAAC,EAAE;MAC3BA,KAAK,CAACa,qBAAqB,GAAG,IAAI;MAClCf,MAAM,CAACgB,WAAW,CAACd,KAAK,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ", "ignoreList": []}