{"version": 3, "names": ["useEffect", "ReduceMotion", "ReducedMotionManager", "isReducedMotionEnabledInSystem", "logger", "ReducedMotionConfig", "mode", "__DEV__", "warn", "wasEnabled", "jsValue", "System", "setEnabled", "Always", "Never"], "sourceRoot": "../../../src", "sources": ["component/ReducedMotionConfig.tsx"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,YAAY,QAAQ,mBAAgB;AAC7C,SACEC,oBAAoB,EACpBC,8BAA8B,QACzB,qBAAkB;AACzB,SAASC,MAAM,QAAQ,oBAAW;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAAC;EAAEC;AAA6B,CAAC,EAAE;EACpEN,SAAS,CAAC,MAAM;IACd,IAAI,CAACO,OAAO,EAAE;MACZ;IACF;IACAH,MAAM,CAACI,IAAI,CAAC,oDAAoDF,IAAI,IAAI,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EAENN,SAAS,CAAC,MAAM;IACd,MAAMS,UAAU,GAAGP,oBAAoB,CAACQ,OAAO;IAC/C,QAAQJ,IAAI;MACV,KAAKL,YAAY,CAACU,MAAM;QACtBT,oBAAoB,CAACU,UAAU,CAACT,8BAA8B,CAAC,CAAC,CAAC;QACjE;MACF,KAAKF,YAAY,CAACY,MAAM;QACtBX,oBAAoB,CAACU,UAAU,CAAC,IAAI,CAAC;QACrC;MACF,KAAKX,YAAY,CAACa,KAAK;QACrBZ,oBAAoB,CAACU,UAAU,CAAC,KAAK,CAAC;QACtC;IACJ;IACA,OAAO,MAAM;MACXV,oBAAoB,CAACU,UAAU,CAACH,UAAU,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACH,IAAI,CAAC,CAAC;EAEV,OAAO,IAAI;AACb", "ignoreList": []}