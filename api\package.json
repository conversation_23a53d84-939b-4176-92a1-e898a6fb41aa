{"name": "backend", "version": "1.0.0", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "description": "", "dependencies": {"@neondatabase/serverless": "^1.0.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "bcrypt": "^6.0.0", "cors": "^2.8.5", "cron": "^4.3.1", "dotenv": "^16.5.0", "express": "^4.21.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}