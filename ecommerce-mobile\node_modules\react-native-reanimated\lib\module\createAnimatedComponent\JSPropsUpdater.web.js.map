{"version": 3, "names": ["JSPropsUpdaterWeb", "addOnJSPropsChangeListener", "_animatedComponent", "removeOnJSPropsChangeListener"], "sources": ["JSPropsUpdater.web.ts"], "sourcesContent": ["'use strict';\nimport type {\n  AnimatedComponentProps,\n  IAnimatedComponentInternal,\n  InitialComponentProps,\n} from './commonTypes';\n\nexport default class JSPropsUpdaterWeb {\n  public addOnJSPropsChangeListener(\n    _animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    // noop\n  }\n\n  public removeOnJSPropsChangeListener(\n    _animatedComponent: React.Component<\n      AnimatedComponentProps<InitialComponentProps>\n    > &\n      IAnimatedComponentInternal\n  ) {\n    // noop\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAOZ,eAAe,MAAMA,iBAAiB,CAAC;EAC9BC,0BAA0BA,CAC/BC,kBAG4B,EAC5B;IACA;EAAA;EAGKC,6BAA6BA,CAClCD,kBAG4B,EAC5B;IACA;EAAA;AAEJ", "ignoreList": []}