{"version": 3, "names": ["useAnimatedStyle", "shouldBeUseWeb", "useAnimatedPropsJS", "updater", "deps", "adapters", "useAnimatedPropsNative", "useAnimatedProps"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedProps.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,gBAAgB,QAAQ,uBAAoB;AAErD,SAASC,cAAc,QAAQ,uBAAoB;;AAGnD;;AAYA,SAASC,kBAAkBA,CACzBC,OAAoB,EACpBC,IAA4B,EAC5BC,QAGQ,EACR;EACA,OAAQL,gBAAgB,CACtBG,OAAO,EACPC,IAAI,EACJC,QAAQ,EACR,IACF,CAAC;AACH;AAEA,MAAMC,sBAAsB,GAAGN,gBAAgB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,gBAAkC,GAAGN,cAAc,CAAC,CAAC,GAC7DC,kBAAkB,GACnBI,sBAAsB", "ignoreList": []}