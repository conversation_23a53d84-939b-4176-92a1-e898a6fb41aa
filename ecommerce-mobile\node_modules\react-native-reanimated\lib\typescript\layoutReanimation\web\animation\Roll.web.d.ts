export declare const RollInData: {
    RollInLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
        };
        duration: number;
    };
    RollInRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const RollOutData: {
    RollOutLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
        };
        duration: number;
    };
    RollOutRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    rotate: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const RollIn: {
    RollInLeft: {
        style: string;
        duration: number;
    };
    RollInRight: {
        style: string;
        duration: number;
    };
};
export declare const RollOut: {
    RollOutLeft: {
        style: string;
        duration: number;
    };
    RollOutRight: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Roll.web.d.ts.map