{"version": 3, "names": ["buildWorkletsHash", "worklets", "Object", "values", "reduce", "acc", "worklet", "__workletHash", "toString", "buildDependencies", "dependencies", "handlers", "handlersList", "filter", "handler", "undefined", "map", "workletHash", "closure", "__closure", "push", "areDependenciesEqual", "nextDependencies", "prevDependencies", "is", "x", "y", "Number", "isNaN", "objectIs", "areHookInputsEqual", "nextDeps", "prevDeps", "length", "i", "isAnimated", "prop", "Array", "isArray", "some", "onFrame", "shallowEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "validateAnimatedStyles", "styles", "Error"], "sources": ["utils.ts"], "sourcesContent": ["'use strict';\nimport type { WorkletFunction } from '../commonTypes';\nimport type { DependencyList } from './commonTypes';\n\n// Builds one big hash from multiple worklets' hashes.\nexport function buildWorkletsHash<Args extends unknown[], ReturnValue>(\n  worklets:\n    | Record<string, WorkletFunction<Args, ReturnValue>>\n    | WorkletFunction<Args, ReturnValue>[]\n) {\n  // For arrays `Object.values` returns the array itself.\n  return Object.values(worklets).reduce(\n    (acc, worklet: WorkletFunction<Args, ReturnValue>) =>\n      acc + worklet.__workletHash.toString(),\n    ''\n  );\n}\n\n// Builds dependencies array for useEvent handlers.\nexport function buildDependencies(\n  dependencies: DependencyList,\n  handlers: Record<string, WorkletFunction | undefined>\n) {\n  type Handler = (typeof handlers)[keyof typeof handlers];\n  const handlersList = Object.values(handlers).filter(\n    (handler) => handler !== undefined\n  ) as NonNullable<Handler>[];\n  if (!dependencies) {\n    dependencies = handlersList.map((handler) => {\n      return {\n        workletHash: handler.__workletHash,\n        closure: handler.__closure,\n      };\n    });\n  } else {\n    dependencies.push(buildWorkletsHash(handlersList));\n  }\n\n  return dependencies;\n}\n\n// This is supposed to work as useEffect comparison.\nexport function areDependenciesEqual(\n  nextDependencies: DependencyList,\n  prevDependencies: DependencyList\n) {\n  function is(x: number, y: number) {\n    return (\n      (x === y && (x !== 0 || 1 / x === 1 / y)) ||\n      (Number.isNaN(x) && Number.isNaN(y))\n    );\n  }\n  const objectIs: (nextDeps: unknown, prevDeps: unknown) => boolean =\n    typeof Object.is === 'function' ? Object.is : is;\n\n  function areHookInputsEqual(\n    nextDeps: DependencyList,\n    prevDeps: DependencyList\n  ) {\n    if (!nextDeps || !prevDeps || prevDeps.length !== nextDeps.length) {\n      return false;\n    }\n    for (let i = 0; i < prevDeps.length; ++i) {\n      if (!objectIs(nextDeps[i], prevDeps[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  return areHookInputsEqual(nextDependencies, prevDependencies);\n}\n\nexport function isAnimated(prop: unknown) {\n  'worklet';\n  if (Array.isArray(prop)) {\n    return prop.some(isAnimated);\n  } else if (typeof prop === 'object' && prop !== null) {\n    if ((prop as Record<string, unknown>).onFrame !== undefined) {\n      return true;\n    } else {\n      return Object.values(prop).some(isAnimated);\n    }\n  }\n  return false;\n}\n\n// This function works because `Object.keys`\n// return empty array of primitives and on arrays\n// it returns array of its indices.\nexport function shallowEqual<\n  T extends Record<string | number | symbol, unknown>\n>(a: T, b: T) {\n  'worklet';\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n  for (let i = 0; i < aKeys.length; i++) {\n    if (a[aKeys[i]] !== b[aKeys[i]]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function validateAnimatedStyles(styles: unknown[] | object) {\n  'worklet';\n  if (typeof styles !== 'object') {\n    throw new Error(\n      `[Reanimated] \\`useAnimatedStyle\\` has to return an object, found ${typeof styles} instead.`\n    );\n  } else if (Array.isArray(styles)) {\n    throw new Error(\n      '[Reanimated] `useAnimatedStyle` has to return an object and cannot return static styles combined with dynamic ones. Please do merging where a component receives props.'\n    );\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAIZ;AACA,OAAO,SAASA,iBAAiBA,CAC/BC,QAEwC,EACxC;EACA;EACA,OAAOC,MAAM,CAACC,MAAM,CAACF,QAAQ,CAAC,CAACG,MAAM,CACnC,CAACC,GAAG,EAAEC,OAA2C,KAC/CD,GAAG,GAAGC,OAAO,CAACC,aAAa,CAACC,QAAQ,CAAC,CAAC,EACxC,EACF,CAAC;AACH;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,YAA4B,EAC5BC,QAAqD,EACrD;EAEA,MAAMC,YAAY,GAAGV,MAAM,CAACC,MAAM,CAACQ,QAAQ,CAAC,CAACE,MAAM,CAChDC,OAAO,IAAKA,OAAO,KAAKC,SAC3B,CAA2B;EAC3B,IAAI,CAACL,YAAY,EAAE;IACjBA,YAAY,GAAGE,YAAY,CAACI,GAAG,CAAEF,OAAO,IAAK;MAC3C,OAAO;QACLG,WAAW,EAAEH,OAAO,CAACP,aAAa;QAClCW,OAAO,EAAEJ,OAAO,CAACK;MACnB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IACLT,YAAY,CAACU,IAAI,CAACpB,iBAAiB,CAACY,YAAY,CAAC,CAAC;EACpD;EAEA,OAAOF,YAAY;AACrB;;AAEA;AACA,OAAO,SAASW,oBAAoBA,CAClCC,gBAAgC,EAChCC,gBAAgC,EAChC;EACA,SAASC,EAAEA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAChC,OACGD,CAAC,KAAKC,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IACvCC,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACF,CAAC,CAAE;EAExC;EACA,MAAMG,QAA2D,GAC/D,OAAO3B,MAAM,CAACsB,EAAE,KAAK,UAAU,GAAGtB,MAAM,CAACsB,EAAE,GAAGA,EAAE;EAElD,SAASM,kBAAkBA,CACzBC,QAAwB,EACxBC,QAAwB,EACxB;IACA,IAAI,CAACD,QAAQ,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAKF,QAAQ,CAACE,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACC,MAAM,EAAE,EAAEC,CAAC,EAAE;MACxC,IAAI,CAACL,QAAQ,CAACE,QAAQ,CAACG,CAAC,CAAC,EAAEF,QAAQ,CAACE,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEA,OAAOJ,kBAAkB,CAACR,gBAAgB,EAAEC,gBAAgB,CAAC;AAC/D;AAEA,OAAO,SAASY,UAAUA,CAACC,IAAa,EAAE;EACxC,SAAS;;EACT,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACG,IAAI,CAACJ,UAAU,CAAC;EAC9B,CAAC,MAAM,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IACpD,IAAKA,IAAI,CAA6BI,OAAO,KAAKzB,SAAS,EAAE;MAC3D,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAOb,MAAM,CAACC,MAAM,CAACiC,IAAI,CAAC,CAACG,IAAI,CAACJ,UAAU,CAAC;IAC7C;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA,OAAO,SAASM,YAAYA,CAE1BC,CAAI,EAAEC,CAAI,EAAE;EACZ,SAAS;;EACT,MAAMC,KAAK,GAAG1C,MAAM,CAAC2C,IAAI,CAACH,CAAC,CAAC;EAC5B,MAAMI,KAAK,GAAG5C,MAAM,CAAC2C,IAAI,CAACF,CAAC,CAAC;EAC5B,IAAIC,KAAK,CAACX,MAAM,KAAKa,KAAK,CAACb,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,KAAK,CAACX,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAIQ,CAAC,CAACE,KAAK,CAACV,CAAC,CAAC,CAAC,KAAKS,CAAC,CAACC,KAAK,CAACV,CAAC,CAAC,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,OAAO,SAASa,sBAAsBA,CAACC,MAA0B,EAAE;EACjE,SAAS;;EACT,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,KAAK,CACZ,oEAAmE,OAAOD,MAAO,WACpF,CAAC;EACH,CAAC,MAAM,IAAIX,KAAK,CAACC,OAAO,CAACU,MAAM,CAAC,EAAE;IAChC,MAAM,IAAIC,KAAK,CACb,yKACF,CAAC;EACH;AACF", "ignoreList": []}