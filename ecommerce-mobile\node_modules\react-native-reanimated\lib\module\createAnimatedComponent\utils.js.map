{"version": 3, "names": ["flattenArray", "array", "Array", "isArray", "resultArr", "_flattenArray", "arr", "for<PERSON>ach", "item", "push", "has", "key", "x", "undefined"], "sources": ["utils.ts"], "sourcesContent": ["'use strict';\nimport type { NestedArray } from './commonTypes';\n\nexport function flattenArray<T>(array: NestedArray<T>): T[] {\n  if (!Array.isArray(array)) {\n    return [array];\n  }\n  const resultArr: T[] = [];\n\n  const _flattenArray = (arr: NestedArray<T>[]): void => {\n    arr.forEach((item) => {\n      if (Array.isArray(item)) {\n        _flattenArray(item);\n      } else {\n        resultArr.push(item);\n      }\n    });\n  };\n  _flattenArray(array);\n  return resultArr;\n}\n\nexport const has = <K extends string>(\n  key: K,\n  x: unknown\n): x is { [key in K]: unknown } => {\n  if (typeof x === 'function' || typeof x === 'object') {\n    if (x === null || x === undefined) {\n      return false;\n    } else {\n      return key in x;\n    }\n  }\n  return false;\n};\n"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,YAAYA,CAAIC,KAAqB,EAAO;EAC1D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACzB,OAAO,CAACA,KAAK,CAAC;EAChB;EACA,MAAMG,SAAc,GAAG,EAAE;EAEzB,MAAMC,aAAa,GAAIC,GAAqB,IAAW;IACrDA,GAAG,CAACC,OAAO,CAAEC,IAAI,IAAK;MACpB,IAAIN,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC,EAAE;QACvBH,aAAa,CAACG,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,SAAS,CAACK,IAAI,CAACD,IAAI,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;EACDH,aAAa,CAACJ,KAAK,CAAC;EACpB,OAAOG,SAAS;AAClB;AAEA,OAAO,MAAMM,GAAG,GAAGA,CACjBC,GAAM,EACNC,CAAU,KACuB;EACjC,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACpD,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS,EAAE;MACjC,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OAAOF,GAAG,IAAIC,CAAC;IACjB;EACF;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}