{"version": 3, "names": ["setAndForwardRef", "getForwardedRef", "setLocalRef", "forwardRef", "ref", "forwardedRef", "current"], "sources": ["setAndForwardRef.ts"], "sourcesContent": ["'use strict';\n/**\n * imported from react-native\n */\n\nimport type { MutableRefObject } from 'react';\n\n/* eslint-disable */\n/**\n * This is a helper function for when a component needs to be able to forward a ref\n * to a child component, but still needs to have access to that component as part of\n * its implementation.\n *\n * Its main use case is in wrappers for native components.\n *\n * Usage:\n *\n *   class MyView extends React.Component {\n *     _nativeRef = null;\n *\n *     _setNativeRef = setAndForwardRef({\n *       getForwardedRef: () => this.props.forwardedRef,\n *       setLocalRef: ref => {\n *         this._nativeRef = ref;\n *       },\n *     });\n *\n *     render() {\n *       return <View ref={this._setNativeRef} />;\n *     }\n *   }\n *\n *   const MyViewWithRef = React.forwardRef((props, ref) => (\n *     <MyView {...props} forwardedRef={ref} />\n *   ));\n *\n *   module.exports = MyViewWithRef;\n */\n/* eslint-enable */\n\ntype ForwardedRef<T> = () => MutableRefObject<T> | ((ref: T) => void);\n\nfunction setAndForwardRef<T>({\n  getForwardedRef,\n  setLocalRef,\n}: {\n  getForwardedRef: ForwardedRef<T>;\n  setLocalRef: (ref: T) => void;\n}): (ref: T) => void {\n  return function forwardRef(ref: T) {\n    const forwardedRef = getForwardedRef();\n\n    setLocalRef(ref);\n\n    // Forward to user ref prop (if one has been specified)\n    if (typeof forwardedRef === 'function') {\n      // Handle function-based refs. String-based refs are handled as functions.\n      forwardedRef(ref);\n    } else if (typeof forwardedRef === 'object' && forwardedRef != null) {\n      // Handle createRef-based refs\n      forwardedRef.current = ref;\n    }\n  };\n}\n\nexport default setAndForwardRef;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA,SAASA,gBAAgBA,CAAI;EAC3BC,eAAe;EACfC;AAIF,CAAC,EAAoB;EACnB,OAAO,SAASC,UAAUA,CAACC,GAAM,EAAE;IACjC,MAAMC,YAAY,GAAGJ,eAAe,CAAC,CAAC;IAEtCC,WAAW,CAACE,GAAG,CAAC;;IAEhB;IACA,IAAI,OAAOC,YAAY,KAAK,UAAU,EAAE;MACtC;MACAA,YAAY,CAACD,GAAG,CAAC;IACnB,CAAC,MAAM,IAAI,OAAOC,YAAY,KAAK,QAAQ,IAAIA,YAAY,IAAI,IAAI,EAAE;MACnE;MACAA,YAAY,CAACC,OAAO,GAAGF,GAAG;IAC5B;EACF,CAAC;AACH;AAEA,eAAeJ,gBAAgB", "ignoreList": []}