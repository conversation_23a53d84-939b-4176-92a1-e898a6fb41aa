{"version": 3, "names": ["React", "useEffect", "useRef", "TextInput", "StyleSheet", "View", "useSharedValue", "useAnimatedProps", "useFrameCallback", "createAnimatedComponent", "addWhitelistedNativeProps", "createCircularDoublesBuffer", "size", "next", "buffer", "Float32Array", "count", "push", "value", "oldValue", "oldCount", "Math", "min", "front", "notEmpty", "current", "index", "back", "DEFAULT_BUFFER_SIZE", "text", "AnimatedTextInput", "loopAnimationFrame", "fn", "lastTime", "loop", "requestAnimationFrame", "time", "getFps", "renderTimeInMs", "getTimeDelta", "timestamp", "previousTimestamp", "completeBufferRoutine", "totalRenderTime", "round", "droppedTimestamp", "nextToDrop", "delta", "<PERSON><PERSON><PERSON><PERSON>", "JsPerformance", "jsFps", "circular<PERSON>uffer", "_", "currentFps", "toFixed", "animatedProps", "defaultValue", "createElement", "style", "styles", "container", "editable", "UiPerformance", "uiFps", "PerformanceMonitor", "monitor", "create", "flexDirection", "position", "backgroundColor", "zIndex", "header", "fontSize", "color", "paddingHorizontal", "fontFamily", "alignItems", "justifyContent", "flexWrap"], "sources": ["PerformanceMonitor.tsx"], "sourcesContent": ["'use strict';\n\nimport React, { useEffect, useRef } from 'react';\nimport { TextInput, StyleSheet, View } from 'react-native';\n\nimport type { FrameInfo } from '../frameCallback';\nimport type { SharedValue } from '../commonTypes';\nimport { useSharedValue, useAnimatedProps, useFrameCallback } from '../hook';\nimport { createAnimatedComponent } from '../../createAnimatedComponent';\nimport { addWhitelistedNativeProps } from '../../ConfigHelper';\n\ntype CircularBuffer = ReturnType<typeof createCircularDoublesBuffer>;\nfunction createCircularDoublesBuffer(size: number) {\n  'worklet';\n\n  return {\n    next: 0 as number,\n    buffer: new Float32Array(size),\n    size,\n    count: 0 as number,\n\n    push(value: number): number | null {\n      const oldValue = this.buffer[this.next];\n      const oldCount = this.count;\n      this.buffer[this.next] = value;\n\n      this.next = (this.next + 1) % this.size;\n      this.count = Math.min(this.size, this.count + 1);\n      return oldCount === this.size ? oldValue : null;\n    },\n\n    front(): number | null {\n      const notEmpty = this.count > 0;\n      if (notEmpty) {\n        const current = this.next - 1;\n        const index = current < 0 ? this.size - 1 : current;\n        return this.buffer[index];\n      }\n      return null;\n    },\n\n    back(): number | null {\n      const notEmpty = this.count > 0;\n      return notEmpty ? this.buffer[this.next] : null;\n    },\n  };\n}\n\nconst DEFAULT_BUFFER_SIZE = 60;\naddWhitelistedNativeProps({ text: true });\nconst AnimatedTextInput = createAnimatedComponent(TextInput);\n\nfunction loopAnimationFrame(fn: (lastTime: number, time: number) => void) {\n  let lastTime = 0;\n\n  function loop() {\n    requestAnimationFrame((time) => {\n      if (lastTime > 0) {\n        fn(lastTime, time);\n      }\n      lastTime = time;\n      requestAnimationFrame(loop);\n    });\n  }\n\n  loop();\n}\n\nfunction getFps(renderTimeInMs: number): number {\n  'worklet';\n  return 1000 / renderTimeInMs;\n}\n\nfunction getTimeDelta(\n  timestamp: number,\n  previousTimestamp: number | null\n): number {\n  'worklet';\n  return previousTimestamp !== null ? timestamp - previousTimestamp : 0;\n}\n\nfunction completeBufferRoutine(\n  buffer: CircularBuffer,\n  timestamp: number,\n  previousTimestamp: number,\n  totalRenderTime: SharedValue<number>\n): number {\n  'worklet';\n  timestamp = Math.round(timestamp);\n  previousTimestamp = Math.round(previousTimestamp) ?? timestamp;\n\n  const droppedTimestamp = buffer.push(timestamp);\n  const nextToDrop = buffer.back()!;\n\n  const delta = getTimeDelta(timestamp, previousTimestamp);\n  const droppedDelta = getTimeDelta(nextToDrop, droppedTimestamp);\n\n  totalRenderTime.value += delta - droppedDelta;\n\n  return getFps(totalRenderTime.value / buffer.count);\n}\n\nfunction JsPerformance() {\n  const jsFps = useSharedValue<string | null>(null);\n  const totalRenderTime = useSharedValue(0);\n  const circularBuffer = useRef<CircularBuffer>(\n    createCircularDoublesBuffer(DEFAULT_BUFFER_SIZE)\n  );\n\n  useEffect(() => {\n    loopAnimationFrame((_, timestamp) => {\n      timestamp = Math.round(timestamp);\n      const previousTimestamp = circularBuffer.current.front() ?? timestamp;\n\n      const currentFps = completeBufferRoutine(\n        circularBuffer.current,\n        timestamp,\n        previousTimestamp,\n        totalRenderTime\n      );\n\n      // JS fps have to be measured every 2nd frame,\n      // thus 2x multiplication has to occur here\n      jsFps.value = (currentFps * 2).toFixed(0);\n    });\n  }, []);\n\n  const animatedProps = useAnimatedProps(() => {\n    const text = 'JS: ' + jsFps.value ?? 'N/A';\n    return { text, defaultValue: text };\n  });\n\n  return (\n    <View style={styles.container}>\n      <AnimatedTextInput\n        style={styles.text}\n        animatedProps={animatedProps}\n        editable={false}\n      />\n    </View>\n  );\n}\n\nfunction UiPerformance() {\n  const uiFps = useSharedValue<string | null>(null);\n  const totalRenderTime = useSharedValue(0);\n  const circularBuffer = useSharedValue<CircularBuffer | null>(null);\n\n  useFrameCallback(({ timestamp }: FrameInfo) => {\n    if (circularBuffer.value === null) {\n      circularBuffer.value = createCircularDoublesBuffer(DEFAULT_BUFFER_SIZE);\n    }\n\n    timestamp = Math.round(timestamp);\n    const previousTimestamp = circularBuffer.value.front() ?? timestamp;\n\n    const currentFps = completeBufferRoutine(\n      circularBuffer.value,\n      timestamp,\n      previousTimestamp,\n      totalRenderTime\n    );\n\n    uiFps.value = currentFps.toFixed(0);\n  });\n\n  const animatedProps = useAnimatedProps(() => {\n    const text = 'UI: ' + uiFps.value ?? 'N/A';\n    return { text, defaultValue: text };\n  });\n\n  return (\n    <View style={styles.container}>\n      <AnimatedTextInput\n        style={styles.text}\n        animatedProps={animatedProps}\n        editable={false}\n      />\n    </View>\n  );\n}\n\nexport function PerformanceMonitor() {\n  return (\n    <View style={styles.monitor}>\n      <JsPerformance />\n      <UiPerformance />\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  monitor: {\n    flexDirection: 'row',\n    position: 'absolute',\n    backgroundColor: '#0006',\n    zIndex: 1000,\n  },\n  header: {\n    fontSize: 14,\n    color: '#ffff',\n    paddingHorizontal: 5,\n  },\n  text: {\n    fontSize: 13,\n    color: '#ffff',\n    fontFamily: 'monospace',\n    paddingHorizontal: 3,\n  },\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n  },\n});\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,SAAS,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAI1D,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAC5E,SAASC,uBAAuB,QAAQ,+BAA+B;AACvE,SAASC,yBAAyB,QAAQ,oBAAoB;AAG9D,SAASC,2BAA2BA,CAACC,IAAY,EAAE;EACjD,SAAS;;EAET,OAAO;IACLC,IAAI,EAAE,CAAW;IACjBC,MAAM,EAAE,IAAIC,YAAY,CAACH,IAAI,CAAC;IAC9BA,IAAI;IACJI,KAAK,EAAE,CAAW;IAElBC,IAAIA,CAACC,KAAa,EAAiB;MACjC,MAAMC,QAAQ,GAAG,IAAI,CAACL,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC;MACvC,MAAMO,QAAQ,GAAG,IAAI,CAACJ,KAAK;MAC3B,IAAI,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAGK,KAAK;MAE9B,IAAI,CAACL,IAAI,GAAG,CAAC,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAACD,IAAI;MACvC,IAAI,CAACI,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,IAAI,CAACV,IAAI,EAAE,IAAI,CAACI,KAAK,GAAG,CAAC,CAAC;MAChD,OAAOI,QAAQ,KAAK,IAAI,CAACR,IAAI,GAAGO,QAAQ,GAAG,IAAI;IACjD,CAAC;IAEDI,KAAKA,CAAA,EAAkB;MACrB,MAAMC,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC;MAC/B,IAAIQ,QAAQ,EAAE;QACZ,MAAMC,OAAO,GAAG,IAAI,CAACZ,IAAI,GAAG,CAAC;QAC7B,MAAMa,KAAK,GAAGD,OAAO,GAAG,CAAC,GAAG,IAAI,CAACb,IAAI,GAAG,CAAC,GAAGa,OAAO;QACnD,OAAO,IAAI,CAACX,MAAM,CAACY,KAAK,CAAC;MAC3B;MACA,OAAO,IAAI;IACb,CAAC;IAEDC,IAAIA,CAAA,EAAkB;MACpB,MAAMH,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC;MAC/B,OAAOQ,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAG,IAAI;IACjD;EACF,CAAC;AACH;AAEA,MAAMe,mBAAmB,GAAG,EAAE;AAC9BlB,yBAAyB,CAAC;EAAEmB,IAAI,EAAE;AAAK,CAAC,CAAC;AACzC,MAAMC,iBAAiB,GAAGrB,uBAAuB,CAACN,SAAS,CAAC;AAE5D,SAAS4B,kBAAkBA,CAACC,EAA4C,EAAE;EACxE,IAAIC,QAAQ,GAAG,CAAC;EAEhB,SAASC,IAAIA,CAAA,EAAG;IACdC,qBAAqB,CAAEC,IAAI,IAAK;MAC9B,IAAIH,QAAQ,GAAG,CAAC,EAAE;QAChBD,EAAE,CAACC,QAAQ,EAAEG,IAAI,CAAC;MACpB;MACAH,QAAQ,GAAGG,IAAI;MACfD,qBAAqB,CAACD,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAA,IAAI,CAAC,CAAC;AACR;AAEA,SAASG,MAAMA,CAACC,cAAsB,EAAU;EAC9C,SAAS;;EACT,OAAO,IAAI,GAAGA,cAAc;AAC9B;AAEA,SAASC,YAAYA,CACnBC,SAAiB,EACjBC,iBAAgC,EACxB;EACR,SAAS;;EACT,OAAOA,iBAAiB,KAAK,IAAI,GAAGD,SAAS,GAAGC,iBAAiB,GAAG,CAAC;AACvE;AAEA,SAASC,qBAAqBA,CAC5B5B,MAAsB,EACtB0B,SAAiB,EACjBC,iBAAyB,EACzBE,eAAoC,EAC5B;EACR,SAAS;;EACTH,SAAS,GAAGnB,IAAI,CAACuB,KAAK,CAACJ,SAAS,CAAC;EACjCC,iBAAiB,GAAGpB,IAAI,CAACuB,KAAK,CAACH,iBAAiB,CAAC,IAAID,SAAS;EAE9D,MAAMK,gBAAgB,GAAG/B,MAAM,CAACG,IAAI,CAACuB,SAAS,CAAC;EAC/C,MAAMM,UAAU,GAAGhC,MAAM,CAACa,IAAI,CAAC,CAAE;EAEjC,MAAMoB,KAAK,GAAGR,YAAY,CAACC,SAAS,EAAEC,iBAAiB,CAAC;EACxD,MAAMO,YAAY,GAAGT,YAAY,CAACO,UAAU,EAAED,gBAAgB,CAAC;EAE/DF,eAAe,CAACzB,KAAK,IAAI6B,KAAK,GAAGC,YAAY;EAE7C,OAAOX,MAAM,CAACM,eAAe,CAACzB,KAAK,GAAGJ,MAAM,CAACE,KAAK,CAAC;AACrD;AAEA,SAASiC,aAAaA,CAAA,EAAG;EACvB,MAAMC,KAAK,GAAG5C,cAAc,CAAgB,IAAI,CAAC;EACjD,MAAMqC,eAAe,GAAGrC,cAAc,CAAC,CAAC,CAAC;EACzC,MAAM6C,cAAc,GAAGjD,MAAM,CAC3BS,2BAA2B,CAACiB,mBAAmB,CACjD,CAAC;EAED3B,SAAS,CAAC,MAAM;IACd8B,kBAAkB,CAAC,CAACqB,CAAC,EAAEZ,SAAS,KAAK;MACnCA,SAAS,GAAGnB,IAAI,CAACuB,KAAK,CAACJ,SAAS,CAAC;MACjC,MAAMC,iBAAiB,GAAGU,cAAc,CAAC1B,OAAO,CAACF,KAAK,CAAC,CAAC,IAAIiB,SAAS;MAErE,MAAMa,UAAU,GAAGX,qBAAqB,CACtCS,cAAc,CAAC1B,OAAO,EACtBe,SAAS,EACTC,iBAAiB,EACjBE,eACF,CAAC;;MAED;MACA;MACAO,KAAK,CAAChC,KAAK,GAAG,CAACmC,UAAU,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAGhD,gBAAgB,CAAC,MAAM;IAC3C,MAAMsB,IAAI,GAAG,MAAM,GAAGqB,KAAK,CAAChC,KAAK,IAAI,KAAK;IAC1C,OAAO;MAAEW,IAAI;MAAE2B,YAAY,EAAE3B;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,oBACE7B,KAAA,CAAAyD,aAAA,CAACpD,IAAI;IAACqD,KAAK,EAAEC,MAAM,CAACC;EAAU,gBAC5B5D,KAAA,CAAAyD,aAAA,CAAC3B,iBAAiB;IAChB4B,KAAK,EAAEC,MAAM,CAAC9B,IAAK;IACnB0B,aAAa,EAAEA,aAAc;IAC7BM,QAAQ,EAAE;EAAM,CACjB,CACG,CAAC;AAEX;AAEA,SAASC,aAAaA,CAAA,EAAG;EACvB,MAAMC,KAAK,GAAGzD,cAAc,CAAgB,IAAI,CAAC;EACjD,MAAMqC,eAAe,GAAGrC,cAAc,CAAC,CAAC,CAAC;EACzC,MAAM6C,cAAc,GAAG7C,cAAc,CAAwB,IAAI,CAAC;EAElEE,gBAAgB,CAAC,CAAC;IAAEgC;EAAqB,CAAC,KAAK;IAC7C,IAAIW,cAAc,CAACjC,KAAK,KAAK,IAAI,EAAE;MACjCiC,cAAc,CAACjC,KAAK,GAAGP,2BAA2B,CAACiB,mBAAmB,CAAC;IACzE;IAEAY,SAAS,GAAGnB,IAAI,CAACuB,KAAK,CAACJ,SAAS,CAAC;IACjC,MAAMC,iBAAiB,GAAGU,cAAc,CAACjC,KAAK,CAACK,KAAK,CAAC,CAAC,IAAIiB,SAAS;IAEnE,MAAMa,UAAU,GAAGX,qBAAqB,CACtCS,cAAc,CAACjC,KAAK,EACpBsB,SAAS,EACTC,iBAAiB,EACjBE,eACF,CAAC;IAEDoB,KAAK,CAAC7C,KAAK,GAAGmC,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGhD,gBAAgB,CAAC,MAAM;IAC3C,MAAMsB,IAAI,GAAG,MAAM,GAAGkC,KAAK,CAAC7C,KAAK,IAAI,KAAK;IAC1C,OAAO;MAAEW,IAAI;MAAE2B,YAAY,EAAE3B;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,oBACE7B,KAAA,CAAAyD,aAAA,CAACpD,IAAI;IAACqD,KAAK,EAAEC,MAAM,CAACC;EAAU,gBAC5B5D,KAAA,CAAAyD,aAAA,CAAC3B,iBAAiB;IAChB4B,KAAK,EAAEC,MAAM,CAAC9B,IAAK;IACnB0B,aAAa,EAAEA,aAAc;IAC7BM,QAAQ,EAAE;EAAM,CACjB,CACG,CAAC;AAEX;AAEA,OAAO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,oBACEhE,KAAA,CAAAyD,aAAA,CAACpD,IAAI;IAACqD,KAAK,EAAEC,MAAM,CAACM;EAAQ,gBAC1BjE,KAAA,CAAAyD,aAAA,CAACR,aAAa,MAAE,CAAC,eACjBjD,KAAA,CAAAyD,aAAA,CAACK,aAAa,MAAE,CACZ,CAAC;AAEX;AAEA,MAAMH,MAAM,GAAGvD,UAAU,CAAC8D,MAAM,CAAC;EAC/BD,OAAO,EAAE;IACPE,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,OAAO;IACxBC,MAAM,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdC,iBAAiB,EAAE;EACrB,CAAC;EACD7C,IAAI,EAAE;IACJ2C,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdE,UAAU,EAAE,WAAW;IACvBD,iBAAiB,EAAE;EACrB,CAAC;EACDd,SAAS,EAAE;IACTgB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,aAAa,EAAE,KAAK;IACpBW,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}