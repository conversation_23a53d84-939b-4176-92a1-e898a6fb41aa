{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "logger", "withSequence", "_reduceMotionOrFirstAnimation", "_animations", "reduceMotion", "unshift", "length", "warn", "onStart", "animation", "value", "current", "onFrame", "animationIndex", "animations", "map", "a", "result", "finished", "findNextNonReducedMotionAnimationIndex", "index", "callback", "for<PERSON>ach", "sequence", "now", "currentAnim", "nextAnim", "previousAnimation", "anim", "undefined", "currentAnimation", "isHigherOrder"], "sourceRoot": "../../../src", "sources": ["animation/sequence.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,WAAQ;AASrE,SAASC,MAAM,QAAQ,oBAAW;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA,OAAO,SAASC,YAAYA,CAC1BC,6BAA6E,EAC7E,GAAGC,WAA6C,EAClB;EAC9B,SAAS;;EACT,IAAIC,YAAsC;;EAE1C;EACA;EACA,IAAIF,6BAA6B,EAAE;IACjC,IAAI,OAAOA,6BAA6B,KAAK,QAAQ,EAAE;MACrDE,YAAY,GAAGF,6BAA6C;IAC9D,CAAC,MAAM;MACLC,WAAW,CAACE,OAAO,CACjBH,6BACF,CAAC;IACH;EACF;EAEA,IAAIC,WAAW,CAACG,MAAM,KAAK,CAAC,EAAE;IAC5BN,MAAM,CAACO,IAAI,CAAC,4CAA4C,CAAC;IAEzD,OAAOT,eAAe,CAAoB,CAAC,EAAE,MAAM;MACjD,SAAS;;MACT,OAAO;QACLU,OAAO,EAAEA,CAACC,SAAS,EAAEC,KAAK,KAAMD,SAAS,CAACE,OAAO,GAAGD,KAAM;QAC1DE,OAAO,EAAEA,CAAA,KAAM,IAAI;QACnBD,OAAO,EAAE,CAAC;QACVE,cAAc,EAAE,CAAC;QACjBT,YAAY,EAAEL,2BAA2B,CAACK,YAAY;MACxD,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,OAAON,eAAe,CACpBK,WAAW,CAAC,CAAC,CAAC,EACd,MAAM;IACJ,SAAS;;IAET,MAAMW,UAAU,GAAGX,WAAW,CAACY,GAAG,CAAEC,CAAC,IAAK;MACxC,MAAMC,MAAM,GAAG,OAAOD,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,CAAC,GAAGA,CAAC;MAChDC,MAAM,CAACC,QAAQ,GAAG,KAAK;MACvB,OAAOD,MAAM;IACf,CAAC,CAAC;IAEF,SAASE,sCAAsCA,CAACC,KAAa,EAAE;MAC7D;MACA;MACA,OACEA,KAAK,GAAGN,UAAU,CAACR,MAAM,GAAG,CAAC,IAC7BQ,UAAU,CAACM,KAAK,CAAC,CAAChB,YAAY,EAC9B;QACAgB,KAAK,EAAE;MACT;MAEA,OAAOA,KAAK;IACd;IAEA,MAAMC,QAAQ,GAAIH,QAAiB,IAAW;MAC5C,IAAIA,QAAQ,EAAE;QACZ;QACA;QACA;MACF;MACA;MACAJ,UAAU,CAACQ,OAAO,CAAEb,SAAS,IAAK;QAChC,IAAI,OAAOA,SAAS,CAACY,QAAQ,KAAK,UAAU,IAAI,CAACZ,SAAS,CAACS,QAAQ,EAAE;UACnET,SAAS,CAACY,QAAQ,CAACH,QAAQ,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC;IAED,SAASK,QAAQA,CAACd,SAA4B,EAAEe,GAAc,EAAW;MACvE,MAAMC,WAAW,GAAGX,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC;MACxD,MAAMK,QAAQ,GAAGO,WAAW,CAACb,OAAO,CAACa,WAAW,EAAED,GAAG,CAAC;MACtDf,SAAS,CAACE,OAAO,GAAGc,WAAW,CAACd,OAAO;MACvC,IAAIO,QAAQ,EAAE;QACZ;QACA,IAAIO,WAAW,CAACJ,QAAQ,EAAE;UACxBI,WAAW,CAACJ,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;QAC3C;QACAI,WAAW,CAACP,QAAQ,GAAG,IAAI;QAC3BT,SAAS,CAACI,cAAc,GAAGM,sCAAsC,CAC/DV,SAAS,CAACI,cAAc,GAAG,CAC7B,CAAC;QACD,IAAIJ,SAAS,CAACI,cAAc,GAAGC,UAAU,CAACR,MAAM,EAAE;UAChD,MAAMoB,QAAQ,GAAGZ,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC;UACrDa,QAAQ,CAAClB,OAAO,CAACkB,QAAQ,EAAED,WAAW,CAACd,OAAO,EAAEa,GAAG,EAAEC,WAAW,CAAC;UACjE,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd;IAEA,SAASjB,OAAOA,CACdC,SAA4B,EAC5BC,KAAsB,EACtBc,GAAc,EACdG,iBAAoC,EAC9B;MACN;MACA;MACAb,UAAU,CAACQ,OAAO,CAAEM,IAAI,IAAK;QAC3B,IAAIA,IAAI,CAACxB,YAAY,KAAKyB,SAAS,EAAE;UACnCD,IAAI,CAACxB,YAAY,GAAGK,SAAS,CAACL,YAAY;QAC5C;MACF,CAAC,CAAC;MACFK,SAAS,CAACI,cAAc,GAAGM,sCAAsC,CAAC,CAAC,CAAC;MAEpE,IAAIQ,iBAAiB,KAAKE,SAAS,EAAE;QACnCF,iBAAiB,GAAGb,UAAU,CAC5BA,UAAU,CAACR,MAAM,GAAG,CAAC,CACD;MACxB;MAEA,MAAMwB,gBAAgB,GAAGhB,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC;MAC7DiB,gBAAgB,CAACtB,OAAO,CACtBsB,gBAAgB,EAChBpB,KAAK,EACLc,GAAG,EACHG,iBACF,CAAC;IACH;IAEA,OAAO;MACLI,aAAa,EAAE,IAAI;MACnBnB,OAAO,EAAEW,QAAQ;MACjBf,OAAO;MACPK,cAAc,EAAE,CAAC;MACjBF,OAAO,EAAEG,UAAU,CAAC,CAAC,CAAC,CAACH,OAAO;MAC9BU,QAAQ;MACRjB,YAAY,EAAEL,2BAA2B,CAACK,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH", "ignoreList": []}