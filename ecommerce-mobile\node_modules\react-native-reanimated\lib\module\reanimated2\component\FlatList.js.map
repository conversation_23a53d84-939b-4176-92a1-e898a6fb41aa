{"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "React", "forwardRef", "useRef", "FlatList", "AnimatedView", "createAnimatedComponent", "LayoutAnimationConfig", "AnimatedFlatList", "createCellRendererComponent", "itemLayoutAnimationRef", "CellRendererComponent", "props", "createElement", "layout", "current", "onLayout", "style", "children", "FlatListForwardRefRender", "ref", "itemLayoutAnimation", "skipEnteringExitingAnimations", "restProps", "scrollEventThrottle", "useMemo", "animatedFlatList", "undefined", "skipEntering", "skipExiting", "ReanimatedFlatList"], "sources": ["FlatList.tsx"], "sourcesContent": ["'use strict';\nimport React, { forwardRef, useRef } from 'react';\nimport type {\n  FlatListProps,\n  LayoutChangeEvent,\n  StyleProp,\n  ViewStyle,\n} from 'react-native';\nimport { FlatList } from 'react-native';\nimport { AnimatedView } from './View';\nimport { createAnimatedComponent } from '../../createAnimatedComponent';\nimport type { ILayoutAnimationBuilder } from '../layoutReanimation/animationBuilder/commonTypes';\nimport { LayoutAnimationConfig } from './LayoutAnimationConfig';\nimport type { AnimatedProps, AnimatedStyle } from '../helperTypes';\n\nconst AnimatedFlatList = createAnimatedComponent(FlatList);\n\ninterface CellRendererComponentProps {\n  onLayout?: ((event: LayoutChangeEvent) => void) | undefined;\n  children: React.ReactNode;\n  style?: StyleProp<AnimatedStyle<ViewStyle>>;\n}\n\nconst createCellRendererComponent = (\n  itemLayoutAnimationRef?: React.MutableRefObject<\n    ILayoutAnimationBuilder | undefined\n  >\n) => {\n  const CellRendererComponent = (props: CellRendererComponentProps) => {\n    return (\n      <AnimatedView\n        // TODO TYPESCRIPT This is temporary cast is to get rid of .d.ts file.\n        layout={itemLayoutAnimationRef?.current as any}\n        onLayout={props.onLayout}\n        style={props.style}>\n        {props.children}\n      </AnimatedView>\n    );\n  };\n\n  return CellRendererComponent;\n};\n\ninterface ReanimatedFlatListPropsWithLayout<T>\n  extends AnimatedProps<FlatListProps<T>> {\n  /**\n   * Lets you pass layout animation directly to the FlatList item.\n   */\n  itemLayoutAnimation?: ILayoutAnimationBuilder;\n  /**\n   * Lets you skip entering and exiting animations of FlatList items when on FlatList mount or unmount.\n   */\n  skipEnteringExitingAnimations?: boolean;\n}\n\nexport type FlatListPropsWithLayout<T> = ReanimatedFlatListPropsWithLayout<T>;\n\n// Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n// but not things like NativeMethods, etc. we need to add them manually by extending the type.\ninterface AnimatedFlatListComplement<T> extends FlatList<T> {\n  getNode(): FlatList<T>;\n}\n\n// We need explicit any here, because this is the exact same type that is used in React Native types.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst FlatListForwardRefRender = function <Item = any>(\n  props: ReanimatedFlatListPropsWithLayout<Item>,\n  ref: React.ForwardedRef<FlatList>\n) {\n  const { itemLayoutAnimation, skipEnteringExitingAnimations, ...restProps } =\n    props;\n\n  // Set default scrollEventThrottle, because user expects\n  // to have continuous scroll events and\n  // react-native defaults it to 50 for FlatLists.\n  // We set it to 1, so we have peace until\n  // there are 960 fps screens.\n  if (!('scrollEventThrottle' in restProps)) {\n    restProps.scrollEventThrottle = 1;\n  }\n\n  const itemLayoutAnimationRef = useRef(itemLayoutAnimation);\n  itemLayoutAnimationRef.current = itemLayoutAnimation;\n\n  const CellRendererComponent = React.useMemo(\n    () => createCellRendererComponent(itemLayoutAnimationRef),\n    [itemLayoutAnimationRef]\n  );\n\n  const animatedFlatList = (\n    // @ts-expect-error In its current type state, createAnimatedComponent cannot create generic components.\n    <AnimatedFlatList\n      ref={ref}\n      {...restProps}\n      CellRendererComponent={CellRendererComponent}\n    />\n  );\n\n  if (skipEnteringExitingAnimations === undefined) {\n    return animatedFlatList;\n  }\n\n  return (\n    <LayoutAnimationConfig skipEntering skipExiting>\n      {animatedFlatList}\n    </LayoutAnimationConfig>\n  );\n};\n\nexport const ReanimatedFlatList = forwardRef(FlatListForwardRefRender) as <\n  // We need explicit any here, because this is the exact same type that is used in React Native types.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ItemT = any\n>(\n  props: ReanimatedFlatListPropsWithLayout<ItemT> & {\n    ref?: React.ForwardedRef<FlatList>;\n  }\n) => React.ReactElement;\n\nexport type ReanimatedFlatList<T> = typeof AnimatedFlatList &\n  AnimatedFlatListComplement<T>;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AACb,OAAOQ,KAAK,IAAIC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAOjD,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,YAAY,QAAQ,QAAQ;AACrC,SAASC,uBAAuB,QAAQ,+BAA+B;AAEvE,SAASC,qBAAqB,QAAQ,yBAAyB;AAG/D,MAAMC,gBAAgB,GAAGF,uBAAuB,CAACF,QAAQ,CAAC;AAQ1D,MAAMK,2BAA2B,GAC/BC,sBAEC,IACE;EACH,MAAMC,qBAAqB,GAAIC,KAAiC,IAAK;IACnE,oBACEX,KAAA,CAAAY,aAAA,CAACR;IACC;IAAA;MACAS,MAAM,EAAEJ,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEK,OAAe;MAC/CC,QAAQ,EAAEJ,KAAK,CAACI,QAAS;MACzBC,KAAK,EAAEL,KAAK,CAACK;IAAM,GAClBL,KAAK,CAACM,QACK,CAAC;EAEnB,CAAC;EAED,OAAOP,qBAAqB;AAC9B,CAAC;;AAgBD;AACA;;AAKA;AACA;AACA,MAAMQ,wBAAwB,GAAG,SAAAA,CAC/BP,KAA8C,EAC9CQ,GAAiC,EACjC;EACA,MAAM;IAAEC,mBAAmB;IAAEC,6BAA6B;IAAE,GAAGC;EAAU,CAAC,GACxEX,KAAK;;EAEP;EACA;EACA;EACA;EACA;EACA,IAAI,EAAE,qBAAqB,IAAIW,SAAS,CAAC,EAAE;IACzCA,SAAS,CAACC,mBAAmB,GAAG,CAAC;EACnC;EAEA,MAAMd,sBAAsB,GAAGP,MAAM,CAACkB,mBAAmB,CAAC;EAC1DX,sBAAsB,CAACK,OAAO,GAAGM,mBAAmB;EAEpD,MAAMV,qBAAqB,GAAGV,KAAK,CAACwB,OAAO,CACzC,MAAMhB,2BAA2B,CAACC,sBAAsB,CAAC,EACzD,CAACA,sBAAsB,CACzB,CAAC;EAED,MAAMgB,gBAAgB;EAAA;EACpB;EACAzB,KAAA,CAAAY,aAAA,CAACL,gBAAgB,EAAArB,QAAA;IACfiC,GAAG,EAAEA;EAAI,GACLG,SAAS;IACbZ,qBAAqB,EAAEA;EAAsB,EAC9C,CACF;EAED,IAAIW,6BAA6B,KAAKK,SAAS,EAAE;IAC/C,OAAOD,gBAAgB;EACzB;EAEA,oBACEzB,KAAA,CAAAY,aAAA,CAACN,qBAAqB;IAACqB,YAAY;IAACC,WAAW;EAAA,GAC5CH,gBACoB,CAAC;AAE5B,CAAC;AAED,OAAO,MAAMI,kBAAkB,gBAAG5B,UAAU,CAACiB,wBAAwB,CAQ9C", "ignoreList": []}