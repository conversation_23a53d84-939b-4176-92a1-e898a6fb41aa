{"version": 3, "names": ["useHandler", "useEvent", "EVENT_TYPE", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END", "useAnimatedGestureHandler", "handlers", "dependencies", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb", "handler", "e", "event", "nativeEvent", "state", "onStart", "onActive", "oldState", "onEnd", "onFail", "onCancel", "onFinish"], "sources": ["useAnimatedGestureHandler.ts"], "sourcesContent": ["'use strict';\nimport type {\n  Dependency<PERSON>ist,\n  NativeEventWrapper,\n  ReanimatedEvent,\n} from './commonTypes';\nimport { useHandler } from './useHandler';\nimport { useEvent } from './useEvent';\n\nconst EVENT_TYPE = {\n  UNDETERMINED: 0,\n  FAILED: 1,\n  BEGAN: 2,\n  CANCELLED: 3,\n  ACTIVE: 4,\n  END: 5,\n} as const;\n\ntype StateType = (typeof EVENT_TYPE)[keyof typeof EVENT_TYPE];\n\n// This type comes from React Native Gesture Handler\n// import type { PanGestureHandlerGestureEvent as DefaultEvent } from 'react-native-gesture-handler';\ntype DefaultEvent = {\n  nativeEvent: {\n    readonly handlerTag: number;\n    readonly numberOfPointers: number;\n    readonly state: (typeof EVENT_TYPE)[keyof typeof EVENT_TYPE];\n    readonly x: number;\n    readonly y: number;\n    readonly absoluteX: number;\n    readonly absoluteY: number;\n    readonly translationX: number;\n    readonly translationY: number;\n    readonly velocityX: number;\n    readonly velocityY: number;\n  };\n};\n\ninterface PropsUsedInUseAnimatedGestureHandler {\n  handlerTag?: number;\n  numberOfPointers?: number;\n  state?: StateType;\n  oldState?: StateType;\n}\n\nexport type GestureHandlerEvent<Event extends object> =\n  | ReanimatedEvent<Event>\n  | Event;\n\ntype GestureHandler<\n  Event extends NativeEventWrapper<PropsUsedInUseAnimatedGestureHandler>,\n  Context extends Record<string, unknown>\n> = (\n  eventPayload: ReanimatedEvent<Event>,\n  context: Context,\n  isCanceledOrFailed?: boolean\n) => void;\n\nexport interface GestureHandlers<\n  Event extends NativeEventWrapper<PropsUsedInUseAnimatedGestureHandler>,\n  Context extends Record<string, unknown>\n> {\n  [key: string]: GestureHandler<Event, Context> | undefined;\n  onStart?: GestureHandler<Event, Context>;\n  onActive?: GestureHandler<Event, Context>;\n  onEnd?: GestureHandler<Event, Context>;\n  onFail?: GestureHandler<Event, Context>;\n  onCancel?: GestureHandler<Event, Context>;\n  onFinish?: GestureHandler<Event, Context>;\n}\n\n/**\n * @deprecated useAnimatedGestureHandler is an old API which is no longer supported.\n *\n * Please check https://docs.swmansion.com/react-native-gesture-handler/docs/guides/upgrading-to-2/\n * for information about how to migrate to `react-native-gesture-handler` v2\n */\nexport function useAnimatedGestureHandler<\n  Event extends NativeEventWrapper<PropsUsedInUseAnimatedGestureHandler> = DefaultEvent,\n  Context extends Record<string, unknown> = Record<string, unknown>\n>(handlers: GestureHandlers<Event, Context>, dependencies?: DependencyList) {\n  type WebOrNativeEvent = Event | ReanimatedEvent<Event>;\n\n  const { context, doDependenciesDiffer, useWeb } = useHandler<Event, Context>(\n    handlers,\n    dependencies\n  );\n  const handler = (e: WebOrNativeEvent) => {\n    'worklet';\n    const event = useWeb\n      ? // On Web we get events straight from React Native and they don't have\n        // the `eventName` field there. To simplify the types here we just\n        // cast it as the field was available.\n        ((e as Event).nativeEvent as ReanimatedEvent<Event>)\n      : (e as ReanimatedEvent<Event>);\n\n    if (event.state === EVENT_TYPE.BEGAN && handlers.onStart) {\n      handlers.onStart(event, context);\n    }\n    if (event.state === EVENT_TYPE.ACTIVE && handlers.onActive) {\n      handlers.onActive(event, context);\n    }\n    if (\n      event.oldState === EVENT_TYPE.ACTIVE &&\n      event.state === EVENT_TYPE.END &&\n      handlers.onEnd\n    ) {\n      handlers.onEnd(event, context);\n    }\n    if (\n      event.oldState === EVENT_TYPE.BEGAN &&\n      event.state === EVENT_TYPE.FAILED &&\n      handlers.onFail\n    ) {\n      handlers.onFail(event, context);\n    }\n    if (\n      event.oldState === EVENT_TYPE.ACTIVE &&\n      event.state === EVENT_TYPE.CANCELLED &&\n      handlers.onCancel\n    ) {\n      handlers.onCancel(event, context);\n    }\n    if (\n      (event.oldState === EVENT_TYPE.BEGAN ||\n        event.oldState === EVENT_TYPE.ACTIVE) &&\n      event.state !== EVENT_TYPE.BEGAN &&\n      event.state !== EVENT_TYPE.ACTIVE &&\n      handlers.onFinish\n    ) {\n      handlers.onFinish(\n        event,\n        context,\n        event.state === EVENT_TYPE.CANCELLED ||\n          event.state === EVENT_TYPE.FAILED\n      );\n    }\n  };\n\n  if (useWeb) {\n    return handler;\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useEvent<Event>(\n    handler,\n    ['onGestureHandlerStateChange', 'onGestureHandlerEvent'],\n    doDependenciesDiffer\n    // This is not correct but we want to make GH think it receives a function.\n  ) as unknown as (e: Event) => void;\n}\n"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,QAAQ,QAAQ,YAAY;AAErC,MAAMC,UAAU,GAAG;EACjBC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAU;;AAIV;AACA;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAGvCC,QAAyC,EAAEC,YAA6B,EAAE;EAG1E,MAAM;IAAEC,OAAO;IAAEC,oBAAoB;IAAEC;EAAO,CAAC,GAAGd,UAAU,CAC1DU,QAAQ,EACRC,YACF,CAAC;EACD,MAAMI,OAAO,GAAIC,CAAmB,IAAK;IACvC,SAAS;;IACT,MAAMC,KAAK,GAAGH,MAAM;IAChB;IACA;IACA;IACEE,CAAC,CAAWE,WAAW,GACxBF,CAA4B;IAEjC,IAAIC,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACG,KAAK,IAAIK,QAAQ,CAACU,OAAO,EAAE;MACxDV,QAAQ,CAACU,OAAO,CAACH,KAAK,EAAEL,OAAO,CAAC;IAClC;IACA,IAAIK,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACK,MAAM,IAAIG,QAAQ,CAACW,QAAQ,EAAE;MAC1DX,QAAQ,CAACW,QAAQ,CAACJ,KAAK,EAAEL,OAAO,CAAC;IACnC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACK,MAAM,IACpCU,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACM,GAAG,IAC9BE,QAAQ,CAACa,KAAK,EACd;MACAb,QAAQ,CAACa,KAAK,CAACN,KAAK,EAAEL,OAAO,CAAC;IAChC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACG,KAAK,IACnCY,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACE,MAAM,IACjCM,QAAQ,CAACc,MAAM,EACf;MACAd,QAAQ,CAACc,MAAM,CAACP,KAAK,EAAEL,OAAO,CAAC;IACjC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACK,MAAM,IACpCU,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACI,SAAS,IACpCI,QAAQ,CAACe,QAAQ,EACjB;MACAf,QAAQ,CAACe,QAAQ,CAACR,KAAK,EAAEL,OAAO,CAAC;IACnC;IACA,IACE,CAACK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACG,KAAK,IAClCY,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACK,MAAM,KACtCU,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACG,KAAK,IAChCY,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACK,MAAM,IACjCG,QAAQ,CAACgB,QAAQ,EACjB;MACAhB,QAAQ,CAACgB,QAAQ,CACfT,KAAK,EACLL,OAAO,EACPK,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACI,SAAS,IAClCW,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACE,MAC/B,CAAC;IACH;EACF,CAAC;EAED,IAAIU,MAAM,EAAE;IACV,OAAOC,OAAO;EAChB;;EAEA;EACA,OAAOd,QAAQ,CACbc,OAAO,EACP,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,EACxDF;EACA;EACF,CAAC;AACH", "ignoreList": []}