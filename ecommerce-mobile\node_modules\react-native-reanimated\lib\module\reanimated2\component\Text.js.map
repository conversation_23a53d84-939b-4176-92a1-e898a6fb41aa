{"version": 3, "names": ["Text", "createAnimatedComponent", "AnimatedText"], "sources": ["Text.ts"], "sourcesContent": ["'use strict';\nimport { Text } from 'react-native';\nimport { createAnimatedComponent } from '../../createAnimatedComponent';\n\n// Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n// but not things like NativeMethods, etc. we need to add them manually by extending the type.\ninterface AnimatedTextComplement extends Text {\n  getNode(): Text;\n}\n\nexport const AnimatedText = createAnimatedComponent(Text);\n\nexport type AnimatedText = typeof AnimatedText & AnimatedTextComplement;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,uBAAuB,QAAQ,+BAA+B;;AAEvE;AACA;;AAKA,OAAO,MAAMC,YAAY,GAAGD,uBAAuB,CAACD,IAAI,CAAC", "ignoreList": []}