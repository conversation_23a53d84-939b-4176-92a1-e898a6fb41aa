{"version": 3, "file": "NativeReanimated.d.ts", "sourceRoot": "", "sources": ["../../../src/NativeReanimated/NativeReanimated.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,iBAAiB,EACjB,OAAO,EACP,aAAa,EACb,YAAY,EACb,MAAM,gBAAgB,CAAC;AAGxB,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAGlD,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,mDAAmD,CAAC;AAKlG,MAAM,WAAW,sBAAsB;IACrC,kBAAkB,CAAC,CAAC,EAClB,KAAK,EAAE,CAAC,EACR,mBAAmB,EAAE,OAAO,EAC5B,iBAAiB,CAAC,EAAE,MAAM,GACzB,YAAY,CAAC,CAAC,CAAC,CAAC;IACnB,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAClD,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5D,oBAAoB,CAClB,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,YAAY,CAAC,MAAM,IAAI,CAAC,GACpC,cAAc,CAAC;IAClB,iBAAiB,CAAC,CAAC,EACjB,cAAc,EAAE,cAAc,EAC9B,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,GACvB,IAAI,CAAC;IACR,oBAAoB,CAAC,CAAC,EACpB,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,EAC7B,SAAS,EAAE,MAAM,EACjB,eAAe,EAAE,MAAM,GACtB,MAAM,CAAC;IACV,sBAAsB,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC;IACzC,WAAW,CAAC,CAAC,EACX,0BAA0B,EAAE,MAAM,GAAG,iBAAiB,EACtD,QAAQ,EAAE,MAAM,EAChB,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,IAAI,GAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;IACd,sBAAsB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;IAC5C,cAAc,CACZ,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,EAChB,iBAAiB,EAAE,MAAM,EACzB,OAAO,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE,OAAO,GAAG,aAAa,KAAK,IAAI,CAAC,GAC7D,MAAM,CAAC;IACV,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACzC,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAC/D,0BAA0B,CACxB,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,EAC7B,sBAAsB,EAAE,OAAO,EAC/B,0BAA0B,EAAE,OAAO,GAClC,MAAM,CAAC;IACV,6BAA6B,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IACxD,6BAA6B,CAC3B,qBAAqB,EAAE,wBAAwB,EAAE,GAChD,IAAI,CAAC;IACR,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,GAAG,IAAI,CAAC;CAC9E;AAcD,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,iBAAiB,CAAyB;;IAwBlD,kBAAkB,CAAC,CAAC,EAClB,KAAK,EAAE,CAAC,EACR,mBAAmB,EAAE,OAAO,EAC5B,iBAAiB,CAAC,EAAE,MAAM;IAS5B,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;IAI1C,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IAI3D,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,MAAM,IAAI,CAAC;IAIxE,iBAAiB,CAAC,CAAC,EACjB,cAAc,EAAE,cAAc,EAC9B,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;IAQnC,cAAc,CACZ,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,EAChB,iBAAiB,EAAE,MAAM,EACzB,OAAO,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE,OAAO,GAAG,aAAa,KAAK,IAAI,CAAC;IAUhE,gBAAgB,CAAC,QAAQ,EAAE,MAAM;IAIjC,oBAAoB,CAAC,CAAC,EACpB,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,EAC7B,SAAS,EAAE,MAAM,EACjB,eAAe,EAAE,MAAM;IASzB,sBAAsB,CAAC,EAAE,EAAE,MAAM;IAIjC,WAAW,CAAC,CAAC,EACX,OAAO,EAAE,MAAM,EACf,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,EAAE,qBAAqB;IAC7D,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,IAAI;IAiBhC,6BAA6B,CAC3B,qBAAqB,EAAE,wBAAwB,EAAE;IAKnD,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO;IAOrE,sBAAsB,CAAC,IAAI,EAAE,OAAO;IAIpC,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;IAIvD,0BAA0B,CACxB,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,EAC7B,sBAAsB,EAAE,OAAO,EAC/B,0BAA0B,EAAE,OAAO;IASrC,6BAA6B,CAAC,UAAU,EAAE,MAAM;CAGjD"}