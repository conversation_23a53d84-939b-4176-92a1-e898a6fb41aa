{"version": 3, "names": ["isF<PERSON><PERSON>", "shouldBeUseWeb", "configureLayoutAnimationBatch", "makeShareableCloneRecursive", "createUpdateManager", "animations", "deferredAnimations", "update", "batchItem", "isUnmounting", "push", "length", "flush", "setImmediate", "concat", "updateLayoutAnimations", "updateLayoutAnimationsManager", "viewTag", "type", "config", "sharedTransitionTag", "undefined"], "sourceRoot": "../../src", "sources": ["UpdateLayoutAnimations.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,EAAEC,cAAc,QAAQ,sBAAmB;AAC5D,SACEC,6BAA6B,EAC7BC,2BAA2B,QACtB,WAAQ;AAWf,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,MAAMC,UAAsC,GAAG,EAAE;EACjD;EACA;EACA;EACA,MAAMC,kBAA8C,GAAG,EAAE;EAEzD,OAAO;IACLC,MAAMA,CAACC,SAAmC,EAAEC,YAAsB,EAAE;MAClE,IAAIA,YAAY,EAAE;QAChBH,kBAAkB,CAACI,IAAI,CAACF,SAAS,CAAC;MACpC,CAAC,MAAM;QACLH,UAAU,CAACK,IAAI,CAACF,SAAS,CAAC;MAC5B;MACA,IAAIH,UAAU,CAACM,MAAM,GAAGL,kBAAkB,CAACK,MAAM,KAAK,CAAC,EAAE;QACvDX,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACY,KAAK,CAAC,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACD,KAAK,CAAC;MACtD;IACF,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBV,6BAA6B,CAACG,UAAU,CAACS,MAAM,CAACR,kBAAkB,CAAC,CAAC;MACpED,UAAU,CAACM,MAAM,GAAG,CAAC;MACrBL,kBAAkB,CAACK,MAAM,GAAG,CAAC;IAC/B;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,sBAUF;AAET,IAAId,cAAc,CAAC,CAAC,EAAE;EACpBc,sBAAsB,GAAGA,CAAA,KAAM;IAC7B;EAAA,CACD;AACH,CAAC,MAAM;EACL,MAAMC,6BAA6B,GAAGZ,mBAAmB,CAAC,CAAC;EAC3DW,sBAAsB,GAAGA,CACvBE,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,mBAAmB,EACnBX,YAAY,KAEZO,6BAA6B,CAACT,MAAM,CAClC;IACEU,OAAO;IACPC,IAAI;IACJC,MAAM,EAAEA,MAAM,GAAGhB,2BAA2B,CAACgB,MAAM,CAAC,GAAGE,SAAS;IAChED;EACF,CAAC,EACDX,YACF,CAAC;AACL", "ignoreList": []}