{"version": 3, "names": ["shouldBeUseWeb", "SHOULD_BE_USE_WEB", "shareableMappingFlag", "Symbol", "cache", "WeakMap", "shareableMappingCache", "set", "get", "shareable", "shareableRef", "bind"], "sources": ["shareableMappingCache.ts"], "sourcesContent": ["'use strict';\nimport { shouldBeUseWeb } from './PlatformChecker';\nimport type { ShareableRef } from './commonTypes';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\n/**\n * This symbol is used to represent a mapping from the value to itself.\n *\n * It's used to prevent converting a shareable that's already converted -\n * for example a Shared Value that's in worklet's closure.\n **/\nexport const shareableMappingFlag = Symbol('shareable flag');\n\n/*\nDuring a fast refresh, <PERSON>act holds the same instance of a Mutable\n(that's guaranteed by `useRef`) but `shareableCache` gets regenerated and thus\nbecoming empty. This happens when editing the file that contains the definition of this cache.\n\nBecause of it, `makeShareableCloneRecursive` can't find given mapping\nin `shareableCache` for the Mutable and tries to clone it as if it was a regular JS object.\nDuring cloning we use `Object.entries` to iterate over the keys which throws an error on accessing `_value`.\nFor convenience we moved this cache to a separate file so it doesn't scare us with red squiggles.\n*/\n\nconst cache = SHOULD_BE_USE_WEB\n  ? null\n  : new WeakMap<object, ShareableRef | symbol>();\n\nexport const shareableMappingCache = SHOULD_BE_USE_WEB\n  ? {\n      set() {\n        // NOOP\n      },\n      get() {\n        return null;\n      },\n    }\n  : {\n      set(shareable: object, shareableRef?: ShareableRef): void {\n        cache!.set(shareable, shareableRef || shareableMappingFlag);\n      },\n      get: cache!.get.bind(cache),\n    };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,cAAc,QAAQ,mBAAmB;AAGlD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAGC,MAAM,CAAC,gBAAgB,CAAC;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAK,GAAGH,iBAAiB,GAC3B,IAAI,GACJ,IAAII,OAAO,CAAgC,CAAC;AAEhD,OAAO,MAAMC,qBAAqB,GAAGL,iBAAiB,GAClD;EACEM,GAAGA,CAAA,EAAG;IACJ;EAAA,CACD;EACDC,GAAGA,CAAA,EAAG;IACJ,OAAO,IAAI;EACb;AACF,CAAC,GACD;EACED,GAAGA,CAACE,SAAiB,EAAEC,YAA2B,EAAQ;IACxDN,KAAK,CAAEG,GAAG,CAACE,SAAS,EAAEC,YAAY,IAAIR,oBAAoB,CAAC;EAC7D,CAAC;EACDM,GAAG,EAAEJ,KAAK,CAAEI,GAAG,CAACG,IAAI,CAACP,KAAK;AAC5B,CAAC", "ignoreList": []}