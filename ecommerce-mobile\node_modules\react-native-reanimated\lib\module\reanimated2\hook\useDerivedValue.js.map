{"version": 3, "names": ["useEffect", "useRef", "initialUpdaterRun", "makeMutable", "startMapper", "stopMapper", "shouldBeUseWeb", "useDerivedValue", "updater", "dependencies", "initRef", "inputs", "Object", "values", "__closure", "_dependencies", "length", "undefined", "__workletHash", "push", "current", "sharedValue", "fun", "value", "mapperId"], "sources": ["useDerivedValue.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useRef } from 'react';\nimport { initialUpdaterRun } from '../animation';\nimport type { SharedValue, WorkletFunction } from '../commonTypes';\nimport { makeMutable, startMapper, stopMapper } from '../core';\nimport type { DependencyList } from './commonTypes';\nimport { shouldBeUseWeb } from '../PlatformChecker';\n\nexport type DerivedValue<Value> = Readonly<SharedValue<Value>>;\n\n/**\n * Lets you create new shared values based on existing ones while keeping them reactive.\n *\n * @param updater - A function called whenever at least one of the shared values or state used in the function body changes.\n * @param dependencies - An optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.\n * @returns A new readonly shared value based on a value returned from the updater function\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useDerivedValue\n */\n// @ts-expect-error This overload is required by our API.\nexport function useDerivedValue<Value>(\n  updater: () => Value,\n  dependencies?: DependencyList\n): DerivedValue<Value>;\n\nexport function useDerivedValue<Value>(\n  updater: WorkletFunction<[], Value>,\n  dependencies?: DependencyList\n): DerivedValue<Value> {\n  const initRef = useRef<SharedValue<Value> | null>(null);\n  let inputs = Object.values(updater.__closure ?? {});\n  if (shouldBeUseWeb()) {\n    if (!inputs.length && dependencies?.length) {\n      // let web work without a Babel/SWC plugin\n      inputs = dependencies;\n    }\n  }\n\n  // build dependencies\n  if (dependencies === undefined) {\n    dependencies = [...inputs, updater.__workletHash];\n  } else {\n    dependencies.push(updater.__workletHash);\n  }\n\n  if (initRef.current === null) {\n    initRef.current = makeMutable(initialUpdaterRun(updater));\n  }\n\n  const sharedValue: SharedValue<Value> = initRef.current;\n\n  useEffect(() => {\n    const fun = () => {\n      'worklet';\n      sharedValue.value = updater();\n    };\n    const mapperId = startMapper(fun, inputs, [sharedValue]);\n    return () => {\n      stopMapper(mapperId);\n    };\n  }, dependencies);\n\n  useEffect(() => {\n    return () => {\n      initRef.current = null;\n    };\n  }, []);\n\n  return sharedValue;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,iBAAiB,QAAQ,cAAc;AAEhD,SAASC,WAAW,EAAEC,WAAW,EAAEC,UAAU,QAAQ,SAAS;AAE9D,SAASC,cAAc,QAAQ,oBAAoB;;AAInD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASC,eAAeA,CAC7BC,OAAmC,EACnCC,YAA6B,EACR;EACrB,MAAMC,OAAO,GAAGT,MAAM,CAA4B,IAAI,CAAC;EACvD,IAAIU,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACM,SAAS,IAAI,CAAC,CAAC,CAAC;EACnD,IAAIR,cAAc,CAAC,CAAC,EAAE;IAAA,IAAAS,aAAA;IACpB,IAAI,CAACJ,MAAM,CAACK,MAAM,KAAAD,aAAA,GAAIN,YAAY,cAAAM,aAAA,eAAZA,aAAA,CAAcC,MAAM,EAAE;MAC1C;MACAL,MAAM,GAAGF,YAAY;IACvB;EACF;;EAEA;EACA,IAAIA,YAAY,KAAKQ,SAAS,EAAE;IAC9BR,YAAY,GAAG,CAAC,GAAGE,MAAM,EAAEH,OAAO,CAACU,aAAa,CAAC;EACnD,CAAC,MAAM;IACLT,YAAY,CAACU,IAAI,CAACX,OAAO,CAACU,aAAa,CAAC;EAC1C;EAEA,IAAIR,OAAO,CAACU,OAAO,KAAK,IAAI,EAAE;IAC5BV,OAAO,CAACU,OAAO,GAAGjB,WAAW,CAACD,iBAAiB,CAACM,OAAO,CAAC,CAAC;EAC3D;EAEA,MAAMa,WAA+B,GAAGX,OAAO,CAACU,OAAO;EAEvDpB,SAAS,CAAC,MAAM;IACd,MAAMsB,GAAG,GAAGA,CAAA,KAAM;MAChB,SAAS;;MACTD,WAAW,CAACE,KAAK,GAAGf,OAAO,CAAC,CAAC;IAC/B,CAAC;IACD,MAAMgB,QAAQ,GAAGpB,WAAW,CAACkB,GAAG,EAAEX,MAAM,EAAE,CAACU,WAAW,CAAC,CAAC;IACxD,OAAO,MAAM;MACXhB,UAAU,CAACmB,QAAQ,CAAC;IACtB,CAAC;EACH,CAAC,EAAEf,YAAY,CAAC;EAEhBT,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXU,OAAO,CAACU,OAAO,GAAG,IAAI;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOC,WAAW;AACpB", "ignoreList": []}