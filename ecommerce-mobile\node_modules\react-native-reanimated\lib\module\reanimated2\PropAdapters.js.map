{"version": 3, "names": ["addWhitelistedNativeProps", "createAnimatedPropAdapter", "adapter", "nativeProps", "nativePropsToAdd", "for<PERSON>ach", "prop"], "sources": ["PropAdapters.ts"], "sourcesContent": ["'use strict';\nimport { addWhitelistedNativeProps } from '../ConfigHelper';\nimport type {\n  AnimatedPropsAdapterFunction,\n  AnimatedPropsAdapterWorklet,\n} from './commonTypes';\n\n// @ts-expect-error This overload is required by our API.\nexport function createAnimatedPropAdapter(\n  adapter: AnimatedPropsAdapterFunction,\n  nativeProps?: string[]\n): AnimatedPropsAdapterFunction;\n\nexport function createAnimatedPropAdapter(\n  adapter: AnimatedPropsAdapterWorklet,\n  nativeProps?: string[]\n): AnimatedPropsAdapterWorklet {\n  const nativePropsToAdd: { [key: string]: boolean } = {};\n  nativeProps?.forEach((prop) => {\n    nativePropsToAdd[prop] = true;\n  });\n  addWhitelistedNativeProps(nativePropsToAdd);\n  return adapter;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,yBAAyB,QAAQ,iBAAiB;;AAM3D;;AAMA,OAAO,SAASC,yBAAyBA,CACvCC,OAAoC,EACpCC,WAAsB,EACO;EAC7B,MAAMC,gBAA4C,GAAG,CAAC,CAAC;EACvDD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,OAAO,CAAEC,IAAI,IAAK;IAC7BF,gBAAgB,CAACE,IAAI,CAAC,GAAG,IAAI;EAC/B,CAAC,CAAC;EACFN,yBAAyB,CAACI,gBAAgB,CAAC;EAC3C,OAAOF,OAAO;AAChB", "ignoreList": []}