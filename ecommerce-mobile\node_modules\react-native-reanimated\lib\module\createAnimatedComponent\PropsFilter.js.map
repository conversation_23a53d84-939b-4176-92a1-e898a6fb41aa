{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "shallowEqual", "isSharedValue", "isChromeDebugger", "WorkletEventHandler", "initialUpdaterRun", "hasInlineStyles", "getInlineStyle", "flattenArray", "has", "StyleSheet", "dummyListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "filterNonAnimatedProps", "component", "inputProps", "props", "_maybePrepareForNewInitials", "styleProp", "style", "styles", "_requiresNewInitials", "_initialStyle", "processedStyle", "map", "viewDescriptors", "_style$viewsRef", "viewsRef", "add", "initial", "updater", "flatten", "animatedProp", "animatedProps", "undefined", "keys", "for<PERSON>ach", "initialValueKey", "_animatedProp$initial", "_animatedProp$viewsRe", "workletEventHandler", "eventNames", "length", "eventName", "listeners", "_previousProps"], "sources": ["PropsFilter.tsx"], "sourcesContent": ["'use strict';\n\nimport { shallowEqual } from '../reanimated2/hook/utils';\nimport type { StyleProps } from '../reanimated2/commonTypes';\nimport { isSharedValue } from '../reanimated2/isSharedValue';\nimport { isChromeDebugger } from '../reanimated2/PlatformChecker';\nimport { WorkletEventHandler } from '../reanimated2/WorkletEventHandler';\nimport { initialUpdaterRun } from '../reanimated2/animation';\nimport { hasInlineStyles, getInlineStyle } from './InlinePropManager';\nimport type {\n  AnimatedComponentProps,\n  AnimatedProps,\n  InitialComponentProps,\n  IAnimatedComponentInternal,\n  IPropsFilter,\n} from './commonTypes';\nimport { flattenArray, has } from './utils';\nimport { StyleSheet } from 'react-native';\n\nfunction dummyListener() {\n  // empty listener we use to assign to listener properties for which animated\n  // event is used.\n}\n\nexport class PropsFilter implements IPropsFilter {\n  private _initialStyle = {};\n  private _previousProps: React.Component['props'] | null = null;\n  private _requiresNewInitials = true;\n\n  public filterNonAnimatedProps(\n    component: React.Component<unknown, unknown> & IAnimatedComponentInternal\n  ): Record<string, unknown> {\n    const inputProps =\n      component.props as AnimatedComponentProps<InitialComponentProps>;\n\n    this._maybePrepareForNewInitials(inputProps);\n\n    const props: Record<string, unknown> = {};\n    for (const key in inputProps) {\n      const value = inputProps[key];\n      if (key === 'style') {\n        const styleProp = inputProps.style;\n        const styles = flattenArray<StyleProps>(styleProp ?? []);\n        if (this._requiresNewInitials) {\n          this._initialStyle = {};\n        }\n        const processedStyle: StyleProps = styles.map((style) => {\n          if (style && style.viewDescriptors) {\n            // this is how we recognize styles returned by useAnimatedStyle\n            // TODO - refactor, since `viewsRef` is only present on Web\n            style.viewsRef?.add(component);\n            if (this._requiresNewInitials) {\n              this._initialStyle = {\n                ...style.initial.value,\n                ...this._initialStyle,\n                ...initialUpdaterRun<StyleProps>(style.initial.updater),\n              };\n            }\n            return this._initialStyle;\n          } else if (hasInlineStyles(style)) {\n            return getInlineStyle(style, this._requiresNewInitials);\n          } else {\n            return style;\n          }\n        });\n        props[key] = StyleSheet.flatten(processedStyle);\n      } else if (key === 'animatedProps') {\n        const animatedProp = inputProps.animatedProps as Partial<\n          AnimatedComponentProps<AnimatedProps>\n        >;\n        if (animatedProp.initial !== undefined) {\n          Object.keys(animatedProp.initial.value).forEach((initialValueKey) => {\n            props[initialValueKey] =\n              animatedProp.initial?.value[initialValueKey];\n            // TODO - refacotr, since `viewsRef` is only present on Web\n            animatedProp.viewsRef?.add(component);\n          });\n        }\n      } else if (\n        has('workletEventHandler', value) &&\n        value.workletEventHandler instanceof WorkletEventHandler\n      ) {\n        if (value.workletEventHandler.eventNames.length > 0) {\n          value.workletEventHandler.eventNames.forEach((eventName) => {\n            props[eventName] = has('listeners', value.workletEventHandler)\n              ? (\n                  value.workletEventHandler.listeners as Record<string, unknown>\n                )[eventName]\n              : dummyListener;\n          });\n        } else {\n          props[key] = dummyListener;\n        }\n      } else if (isSharedValue(value)) {\n        if (this._requiresNewInitials) {\n          props[key] = value.value;\n        }\n      } else if (key !== 'onGestureHandlerStateChange' || !isChromeDebugger()) {\n        props[key] = value;\n      }\n    }\n    this._requiresNewInitials = false;\n    return props;\n  }\n\n  private _maybePrepareForNewInitials(\n    inputProps: AnimatedComponentProps<InitialComponentProps>\n  ) {\n    if (this._previousProps && inputProps.style) {\n      this._requiresNewInitials = !shallowEqual(\n        this._previousProps,\n        inputProps\n      );\n    }\n    this._previousProps = inputProps;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAEb,SAASW,YAAY,QAAQ,2BAA2B;AAExD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AAQrE,SAASC,YAAY,EAAEC,GAAG,QAAQ,SAAS;AAC3C,SAASC,UAAU,QAAQ,cAAc;AAEzC,SAASC,aAAaA,CAAA,EAAG;EACvB;EACA;AAAA;AAGF,OAAO,MAAMC,WAAW,CAAyB;EAAAC,YAAA;IAAAjC,eAAA,wBACvB,CAAC,CAAC;IAAAA,eAAA,yBACgC,IAAI;IAAAA,eAAA,+BAC/B,IAAI;EAAA;EAE5BkC,sBAAsBA,CAC3BC,SAAyE,EAChD;IACzB,MAAMC,UAAU,GACdD,SAAS,CAACE,KAAsD;IAElE,IAAI,CAACC,2BAA2B,CAACF,UAAU,CAAC;IAE5C,MAAMC,KAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMnC,GAAG,IAAIkC,UAAU,EAAE;MAC5B,MAAMjC,KAAK,GAAGiC,UAAU,CAAClC,GAAG,CAAC;MAC7B,IAAIA,GAAG,KAAK,OAAO,EAAE;QACnB,MAAMqC,SAAS,GAAGH,UAAU,CAACI,KAAK;QAClC,MAAMC,MAAM,GAAGb,YAAY,CAAaW,SAAS,IAAI,EAAE,CAAC;QACxD,IAAI,IAAI,CAACG,oBAAoB,EAAE;UAC7B,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;QACzB;QACA,MAAMC,cAA0B,GAAGH,MAAM,CAACI,GAAG,CAAEL,KAAK,IAAK;UACvD,IAAIA,KAAK,IAAIA,KAAK,CAACM,eAAe,EAAE;YAAA,IAAAC,eAAA;YAClC;YACA;YACA,CAAAA,eAAA,GAAAP,KAAK,CAACQ,QAAQ,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,GAAG,CAACd,SAAS,CAAC;YAC9B,IAAI,IAAI,CAACO,oBAAoB,EAAE;cAC7B,IAAI,CAACC,aAAa,GAAG;gBACnB,GAAGH,KAAK,CAACU,OAAO,CAAC/C,KAAK;gBACtB,GAAG,IAAI,CAACwC,aAAa;gBACrB,GAAGlB,iBAAiB,CAAae,KAAK,CAACU,OAAO,CAACC,OAAO;cACxD,CAAC;YACH;YACA,OAAO,IAAI,CAACR,aAAa;UAC3B,CAAC,MAAM,IAAIjB,eAAe,CAACc,KAAK,CAAC,EAAE;YACjC,OAAOb,cAAc,CAACa,KAAK,EAAE,IAAI,CAACE,oBAAoB,CAAC;UACzD,CAAC,MAAM;YACL,OAAOF,KAAK;UACd;QACF,CAAC,CAAC;QACFH,KAAK,CAACnC,GAAG,CAAC,GAAG4B,UAAU,CAACsB,OAAO,CAACR,cAAc,CAAC;MACjD,CAAC,MAAM,IAAI1C,GAAG,KAAK,eAAe,EAAE;QAClC,MAAMmD,YAAY,GAAGjB,UAAU,CAACkB,aAE/B;QACD,IAAID,YAAY,CAACH,OAAO,KAAKK,SAAS,EAAE;UACtClD,MAAM,CAACmD,IAAI,CAACH,YAAY,CAACH,OAAO,CAAC/C,KAAK,CAAC,CAACsD,OAAO,CAAEC,eAAe,IAAK;YAAA,IAAAC,qBAAA,EAAAC,qBAAA;YACnEvB,KAAK,CAACqB,eAAe,CAAC,IAAAC,qBAAA,GACpBN,YAAY,CAACH,OAAO,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBxD,KAAK,CAACuD,eAAe,CAAC;YAC9C;YACA,CAAAE,qBAAA,GAAAP,YAAY,CAACL,QAAQ,cAAAY,qBAAA,eAArBA,qBAAA,CAAuBX,GAAG,CAACd,SAAS,CAAC;UACvC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IACLN,GAAG,CAAC,qBAAqB,EAAE1B,KAAK,CAAC,IACjCA,KAAK,CAAC0D,mBAAmB,YAAYrC,mBAAmB,EACxD;QACA,IAAIrB,KAAK,CAAC0D,mBAAmB,CAACC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;UACnD5D,KAAK,CAAC0D,mBAAmB,CAACC,UAAU,CAACL,OAAO,CAAEO,SAAS,IAAK;YAC1D3B,KAAK,CAAC2B,SAAS,CAAC,GAAGnC,GAAG,CAAC,WAAW,EAAE1B,KAAK,CAAC0D,mBAAmB,CAAC,GAExD1D,KAAK,CAAC0D,mBAAmB,CAACI,SAAS,CACnCD,SAAS,CAAC,GACZjC,aAAa;UACnB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLM,KAAK,CAACnC,GAAG,CAAC,GAAG6B,aAAa;QAC5B;MACF,CAAC,MAAM,IAAIT,aAAa,CAACnB,KAAK,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACuC,oBAAoB,EAAE;UAC7BL,KAAK,CAACnC,GAAG,CAAC,GAAGC,KAAK,CAACA,KAAK;QAC1B;MACF,CAAC,MAAM,IAAID,GAAG,KAAK,6BAA6B,IAAI,CAACqB,gBAAgB,CAAC,CAAC,EAAE;QACvEc,KAAK,CAACnC,GAAG,CAAC,GAAGC,KAAK;MACpB;IACF;IACA,IAAI,CAACuC,oBAAoB,GAAG,KAAK;IACjC,OAAOL,KAAK;EACd;EAEQC,2BAA2BA,CACjCF,UAAyD,EACzD;IACA,IAAI,IAAI,CAAC8B,cAAc,IAAI9B,UAAU,CAACI,KAAK,EAAE;MAC3C,IAAI,CAACE,oBAAoB,GAAG,CAACrB,YAAY,CACvC,IAAI,CAAC6C,cAAc,EACnB9B,UACF,CAAC;IACH;IACA,IAAI,CAAC8B,cAAc,GAAG9B,UAAU;EAClC;AACF", "ignoreList": []}