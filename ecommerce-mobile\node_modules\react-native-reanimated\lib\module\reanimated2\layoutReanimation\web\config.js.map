{"version": 3, "names": ["BounceIn", "BounceInData", "BounceOut", "BounceOutData", "FadeIn", "FadeInData", "FadeOut", "FadeOutData", "FlipIn", "FlipInData", "FlipOut", "FlipOutData", "LightSpeedIn", "LightSpeedInData", "LightSpeedOut", "LightSpeedOutData", "Pinwheel", "PinwheelData", "RollIn", "RollInData", "RollOut", "RollOutData", "RotateIn", "RotateInData", "RotateOut", "RotateOutData", "SlideIn", "SlideInData", "SlideOut", "SlideOutData", "StretchIn", "StretchInData", "StretchOut", "StretchOutData", "ZoomIn", "ZoomInData", "ZoomOut", "ZoomOutData", "TransitionType", "AnimationsData", "Animations", "WebEasings", "linear", "ease", "quad", "cubic", "sin", "circle", "exp"], "sources": ["config.ts"], "sourcesContent": ["'use strict';\nimport type { ReduceMotion } from '../../commonTypes';\nimport type { LayoutAnimationType } from '../animationBuilder/commonTypes';\nimport {\n  BounceIn,\n  BounceInData,\n  BounceOut,\n  BounceOutData,\n} from './animation/Bounce.web';\nimport { FadeIn, FadeInData, FadeOut, FadeOutData } from './animation/Fade.web';\nimport { FlipIn, FlipInData, FlipOut, FlipOutData } from './animation/Flip.web';\nimport {\n  LightSpeedIn,\n  LightSpeedInData,\n  LightSpeedOut,\n  LightSpeedOutData,\n} from './animation/Lightspeed.web';\nimport { Pinwheel, PinwheelData } from './animation/Pinwheel.web';\nimport { RollIn, RollInData, RollOut, RollOutData } from './animation/Roll.web';\nimport {\n  RotateIn,\n  RotateInData,\n  RotateOut,\n  RotateOutData,\n} from './animation/Rotate.web';\nimport {\n  SlideIn,\n  SlideInData,\n  SlideOut,\n  SlideOutData,\n} from './animation/Slide.web';\nimport {\n  StretchIn,\n  StretchInData,\n  StretchOut,\n  StretchOutData,\n} from './animation/Stretch.web';\nimport { ZoomIn, ZoomInData, ZoomOut, ZoomOutData } from './animation/Zoom.web';\n\nimport type { AnimationData } from './animationParser';\n\nexport type AnimationCallback = ((finished: boolean) => void) | null;\n\nexport interface AnimationConfig {\n  animationName: string;\n  animationType: LayoutAnimationType;\n  duration: number;\n  delay: number;\n  easing: string;\n  callback: AnimationCallback;\n  reversed: boolean;\n}\n\nexport interface CustomConfig {\n  easingV?: () => number;\n  durationV?: number;\n  delayV?: number;\n  randomizeDelay?: boolean;\n  reduceMotionV?: ReduceMotion;\n  callbackV?: AnimationCallback;\n  reversed?: boolean;\n}\n\nexport enum TransitionType {\n  LINEAR,\n  SEQUENCED,\n  FADING,\n}\n\nexport const AnimationsData: Record<string, AnimationData> = {\n  ...FadeInData,\n  ...FadeOutData,\n  ...BounceInData,\n  ...BounceOutData,\n  ...FlipInData,\n  ...FlipOutData,\n  ...StretchInData,\n  ...StretchOutData,\n  ...ZoomInData,\n  ...ZoomOutData,\n  ...SlideInData,\n  ...SlideOutData,\n  ...LightSpeedInData,\n  ...LightSpeedOutData,\n  ...PinwheelData,\n  ...RotateInData,\n  ...RotateOutData,\n  ...RollInData,\n  ...RollOutData,\n};\n\nexport const Animations = {\n  ...FadeIn,\n  ...FadeOut,\n  ...BounceIn,\n  ...BounceOut,\n  ...FlipIn,\n  ...FlipOut,\n  ...StretchIn,\n  ...StretchOut,\n  ...ZoomIn,\n  ...ZoomOut,\n  ...SlideIn,\n  ...SlideOut,\n  ...LightSpeedIn,\n  ...LightSpeedOut,\n  ...Pinwheel,\n  ...RotateIn,\n  ...RotateOut,\n  ...RollIn,\n  ...RollOut,\n};\n\n// Those are the easings that can be implemented using Bezier curves.\n// Others should be done as CSS animations\nexport const WebEasings = {\n  linear: [0, 0, 1, 1],\n  ease: [0.42, 0, 1, 1],\n  quad: [0.11, 0, 0.5, 0],\n  cubic: [0.32, 0, 0.67, 0],\n  sin: [0.12, 0, 0.39, 0],\n  circle: [0.55, 0, 1, 0.45],\n  exp: [0.7, 0, 0.84, 0],\n};\n\nexport type AnimationNames = keyof typeof Animations;\nexport type LayoutTransitionsNames = keyof typeof AnimationsData;\nexport type WebEasingsNames = keyof typeof WebEasings;\n"], "mappings": "AAAA,YAAY;;AAGZ,SACEA,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,aAAa,QACR,wBAAwB;AAC/B,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,WAAW,QAAQ,sBAAsB;AAC/E,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,WAAW,QAAQ,sBAAsB;AAC/E,SACEC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,iBAAiB,QACZ,4BAA4B;AACnC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,0BAA0B;AACjE,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,WAAW,QAAQ,sBAAsB;AAC/E,SACEC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,aAAa,QACR,wBAAwB;AAC/B,SACEC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,YAAY,QACP,uBAAuB;AAC9B,SACEC,SAAS,EACTC,aAAa,EACbC,UAAU,EACVC,cAAc,QACT,yBAAyB;AAChC,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,WAAW,QAAQ,sBAAsB;AA0B/E,WAAYC,cAAc,0BAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA;AAM1B,OAAO,MAAMC,cAA6C,GAAG;EAC3D,GAAGlC,UAAU;EACb,GAAGE,WAAW;EACd,GAAGN,YAAY;EACf,GAAGE,aAAa;EAChB,GAAGM,UAAU;EACb,GAAGE,WAAW;EACd,GAAGoB,aAAa;EAChB,GAAGE,cAAc;EACjB,GAAGE,UAAU;EACb,GAAGE,WAAW;EACd,GAAGV,WAAW;EACd,GAAGE,YAAY;EACf,GAAGhB,gBAAgB;EACnB,GAAGE,iBAAiB;EACpB,GAAGE,YAAY;EACf,GAAGM,YAAY;EACf,GAAGE,aAAa;EAChB,GAAGN,UAAU;EACb,GAAGE;AACL,CAAC;AAED,OAAO,MAAMmB,UAAU,GAAG;EACxB,GAAGpC,MAAM;EACT,GAAGE,OAAO;EACV,GAAGN,QAAQ;EACX,GAAGE,SAAS;EACZ,GAAGM,MAAM;EACT,GAAGE,OAAO;EACV,GAAGoB,SAAS;EACZ,GAAGE,UAAU;EACb,GAAGE,MAAM;EACT,GAAGE,OAAO;EACV,GAAGV,OAAO;EACV,GAAGE,QAAQ;EACX,GAAGhB,YAAY;EACf,GAAGE,aAAa;EAChB,GAAGE,QAAQ;EACX,GAAGM,QAAQ;EACX,GAAGE,SAAS;EACZ,GAAGN,MAAM;EACT,GAAGE;AACL,CAAC;;AAED;AACA;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrBC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EACvBC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACzBC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACvBC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC1BC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AACvB,CAAC", "ignoreList": []}