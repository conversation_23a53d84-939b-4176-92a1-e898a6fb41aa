/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import type { ElementRef } from 'react';
import type { LayoutEvent } from '../../types';
import useLayoutEffect from '../useLayoutEffect';
import UIManager from '../../exports/UIManager';
import canUseDOM from '../canUseDom';
const DOM_LAYOUT_HANDLER_NAME = '__reactLayoutHandler';
let didWarn = !canUseDOM;
let resizeObserver = null;
declare function getResizeObserver(): ?ResizeObserver;
declare export default function useElementLayout(ref: ElementRef<any>, onLayout?: ?(e: LayoutEvent) => void): any;