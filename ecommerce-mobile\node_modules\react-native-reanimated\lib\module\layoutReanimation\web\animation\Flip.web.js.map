{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_FLIP_TIME", "FlipInData", "FlipInYRight", "name", "style", "transform", "perspective", "rotateY", "translateX", "duration", "FlipInYLeft", "FlipInXUp", "rotateX", "translateY", "FlipInXDown", "FlipInEasyX", "FlipInEasyY", "FlipOutData", "FlipOutYRight", "FlipOutYLeft", "FlipOutXUp", "FlipOutXDown", "FlipOutEasyX", "FlipOutEasyY", "FlipIn", "FlipOut"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/animation/Flip.web.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,uBAAoB;AAEtE,MAAMC,iBAAiB,GAAG,GAAG;AAE7B,OAAO,MAAMC,UAAU,GAAG;EACxBC,YAAY,EAAE;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDU,WAAW,EAAE;IACXP,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDW,SAAS,EAAE;IACTR,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAET;EACZ,CAAC;EAEDc,WAAW,EAAE;IACXX,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAET;EACZ,CAAC;EAEDe,WAAW,EAAE;IACXZ,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC9D,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAO,CAAC;MAAE;IAChE,CAAC;IACDH,QAAQ,EAAET;EACZ,CAAC;EAEDgB,WAAW,EAAE;IACXb,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC9D,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAAE;IAChE,CAAC;IACDE,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMiB,WAAW,GAAG;EACzBC,aAAa,EAAE;IACbf,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDmB,YAAY,EAAE;IACZhB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDoB,UAAU,EAAE;IACVjB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAET;EACZ,CAAC;EAEDqB,YAAY,EAAE;IACZlB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAET;EACZ,CAAC;EAEDsB,YAAY,EAAE;IACZnB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAO,CAAC;MAAE,CAAC;MAC7D,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAQ,CAAC;MAAE;IACjE,CAAC;IACDH,QAAQ,EAAET;EACZ,CAAC;EAEDuB,YAAY,EAAE;IACZpB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAAE,CAAC;MAC7D,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAQ,CAAC;MAAE;IACjE,CAAC;IACDE,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMwB,MAAM,GAAG;EACpBtB,YAAY,EAAE;IACZE,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACC,YAAY,CAAC;IACjEO,QAAQ,EAAER,UAAU,CAACC,YAAY,CAACO;EACpC,CAAC;EACDC,WAAW,EAAE;IACXN,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACS,WAAW,CAAC;IAChED,QAAQ,EAAER,UAAU,CAACS,WAAW,CAACD;EACnC,CAAC;EACDE,SAAS,EAAE;IACTP,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACU,SAAS,CAAC;IAC9DF,QAAQ,EAAER,UAAU,CAACU,SAAS,CAACF;EACjC,CAAC;EACDK,WAAW,EAAE;IACXV,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACa,WAAW,CAAC;IAChEL,QAAQ,EAAER,UAAU,CAACa,WAAW,CAACL;EACnC,CAAC;EACDM,WAAW,EAAE;IACXX,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACc,WAAW,CAAC;IAChEN,QAAQ,EAAER,UAAU,CAACc,WAAW,CAACN;EACnC,CAAC;EACDO,WAAW,EAAE;IACXZ,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACe,WAAW,CAAC;IAChEP,QAAQ,EAAER,UAAU,CAACe,WAAW,CAACP;EACnC;AACF,CAAC;AAED,OAAO,MAAMgB,OAAO,GAAG;EACrBP,aAAa,EAAE;IACbd,KAAK,EAAEL,iCAAiC,CAACkB,WAAW,CAACC,aAAa,CAAC;IACnET,QAAQ,EAAEQ,WAAW,CAACC,aAAa,CAACT;EACtC,CAAC;EACDU,YAAY,EAAE;IACZf,KAAK,EAAEL,iCAAiC,CAACkB,WAAW,CAACE,YAAY,CAAC;IAClEV,QAAQ,EAAEQ,WAAW,CAACE,YAAY,CAACV;EACrC,CAAC;EACDW,UAAU,EAAE;IACVhB,KAAK,EAAEL,iCAAiC,CAACkB,WAAW,CAACG,UAAU,CAAC;IAChEX,QAAQ,EAAEQ,WAAW,CAACG,UAAU,CAACX;EACnC,CAAC;EACDY,YAAY,EAAE;IACZjB,KAAK,EAAEL,iCAAiC,CAACkB,WAAW,CAACI,YAAY,CAAC;IAClEZ,QAAQ,EAAEQ,WAAW,CAACI,YAAY,CAACZ;EACrC,CAAC;EACDa,YAAY,EAAE;IACZlB,KAAK,EAAEL,iCAAiC,CAACkB,WAAW,CAACK,YAAY,CAAC;IAClEb,QAAQ,EAAEQ,WAAW,CAACK,YAAY,CAACb;EACrC,CAAC;EACDc,YAAY,EAAE;IACZnB,KAAK,EAAEL,iCAAiC,CAACkB,WAAW,CAACM,YAAY,CAAC;IAClEd,QAAQ,EAAEQ,WAAW,CAACM,YAAY,CAACd;EACrC;AACF,CAAC", "ignoreList": []}