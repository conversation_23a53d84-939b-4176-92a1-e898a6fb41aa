/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 */

type Groups = {
  [key: number]: {
    start: ?number,
    rules: Array<string>,
  }
};
type Selectors = {
  [key: string]: boolean
};
export type OrderedCSSStyleSheet = {|
  getTextContent: () => string,
  insert: (cssText: string, groupValue: number) => void,
|};
const slice = Array.prototype.slice;

/**
 * Order-based insertion of CSS.
 *
 * Each rule is associated with a numerically defined group.
 * Groups are ordered within the style sheet according to their number, with the
 * lowest first.
 *
 * Groups are implemented using marker rules. The selector of the first rule of
 * each group is used only to encode the group number for hydration. An
 * alternative implementation could rely on CSSMediaRule, allowing groups to be
 * treated as a sub-sheet, but the Edge implementation of CSSMediaRule is
 * broken.
 * https://developer.mozilla.org/en-US/docs/Web/API/CSSMediaRule
 * https://gist.github.com/necolas/aa0c37846ad6bd3b05b727b959e82674
 */
declare export default function createOrderedCSSStyleSheet(sheet: ?CSSStyleSheet): OrderedCSSStyleSheet;
/**
 * Helper functions
 */
declare function encodeGroupRule(group: any): any;
const groupPattern = /["']/g;
declare function decodeGroupRule(cssRule: any): any;
declare function getOrderedGroups(obj: {
  [key: number]: any
}): any;
const selectorPattern = /\s*([,])\s*/g;
declare function getSelectorText(cssText: any): any;
declare function insertRuleAt(root: any, cssText: string, position: number): boolean;