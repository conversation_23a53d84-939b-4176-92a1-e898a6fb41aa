{"version": 3, "names": ["dispatchCommand", "measure", "scrollTo", "setGestureState", "setNativeProps", "getRelativeCoords"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nexport { dispatchCommand } from './dispatchCommand';\nexport { measure } from './measure';\nexport { scrollTo } from './scrollTo';\nexport { setGestureState } from './setGestureState';\nexport { setNativeProps } from './setNativeProps';\nexport { getRelativeCoords } from './getRelativeCoords';\nexport type { ComponentCoords } from './getRelativeCoords';\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB", "ignoreList": []}