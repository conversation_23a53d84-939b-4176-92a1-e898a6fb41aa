{"version": 3, "names": ["AnimationsData", "TransitionType", "convertAnimationObjectToKeyframes", "LinearTransition", "SequencedTransition", "FadingTransition", "JumpingTransition", "insertWebAnimation", "CurvedTransition", "EntryExitTransition", "addPxToTransform", "transform", "newTransform", "map", "transformProp", "newTransformProp", "key", "value", "Object", "entries", "includes", "createCustomKeyFrameAnimation", "keyframeDefinitions", "values", "animationData", "name", "style", "duration", "generateNextCustomKeyframeName", "parsedKeyframe", "createAnimationWithInitialValues", "animationName", "initialValues", "animationStyle", "structuredClone", "firstAnimationStep", "rest", "transformWithPx", "transformStyle", "Map", "rule", "property", "set", "Array", "from", "keyframeName", "animationObject", "keyframe", "customKeyframeCounter", "TransitionGenerator", "transitionType", "transitionData", "transitionKeyframeName", "dummyTransitionKeyframeName", "transitionObject", "LINEAR", "SEQUENCED", "FADING", "JUMPING", "CURVED", "firstKeyframeObj", "secondKeyframeObj", "dummy<PERSON><PERSON><PERSON>", "ENTRY_EXIT", "transitionKeyframe"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/createAnimation.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,EAAEC,cAAc,QAAQ,aAAU;AAEzD,SAASC,iCAAiC,QAAQ,sBAAmB;AAOrE,SAASC,gBAAgB,QAAQ,4BAAyB;AAC1D,SAASC,mBAAmB,QAAQ,+BAA4B;AAChE,SAASC,gBAAgB,QAAQ,4BAAyB;AAC1D,SAASC,iBAAiB,QAAQ,6BAA0B;AAC5D,SAASC,kBAAkB,QAAQ,eAAY;AAC/C,SAASC,gBAAgB,QAAQ,4BAAyB;AAC1D,SAASC,mBAAmB,QAAQ,+BAA4B;AAIhE;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,SAAwB,EAAE;EAGlD;EACA;EACA,MAAMC,YAAY,GAAGD,SAAS,CAACE,GAAG,CAAEC,aAA8B,IAAK;IACrE,MAAMC,gBAAkD,GAAG,CAAC,CAAC;IAC7D,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,aAAa,CAAC,EAAE;MACxD,IACE,CAACE,GAAG,CAACI,QAAQ,CAAC,WAAW,CAAC,IAAIJ,GAAG,CAACI,QAAQ,CAAC,aAAa,CAAC,KACzD,OAAOH,KAAK,KAAK,QAAQ,EACzB;QACA;QACA;QACAF,gBAAgB,CAACC,GAAG,CAAC,GAAG,GAAGC,KAAK,IAAI;MACtC,CAAC,MAAM;QACL;QACAF,gBAAgB,CAACC,GAAG,CAAC,GAAGC,KAAK;MAC/B;IACF;IACA,OAAOF,gBAAgB;EACzB,CAAC,CAAC;EAEF,OAAOH,YAAY;AACrB;AAEA,OAAO,SAASS,6BAA6BA,CAC3CC,mBAAwC,EACxC;EACA,KAAK,MAAML,KAAK,IAAIC,MAAM,CAACK,MAAM,CAACD,mBAAmB,CAAC,EAAE;IACtD,IAAIL,KAAK,CAACN,SAAS,EAAE;MACnBM,KAAK,CAACN,SAAS,GAAGD,gBAAgB,CAACO,KAAK,CAACN,SAA0B,CAAC;IACtE;EACF;EAEA,MAAMa,aAA4B,GAAG;IACnCC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAEJ,mBAAmB;IAC1BK,QAAQ,EAAE,CAAC;EACb,CAAC;EAEDH,aAAa,CAACC,IAAI,GAAGG,8BAA8B,CAAC,CAAC;EAErD,MAAMC,cAAc,GAAG3B,iCAAiC,CAACsB,aAAa,CAAC;EAEvEjB,kBAAkB,CAACiB,aAAa,CAACC,IAAI,EAAEI,cAAc,CAAC;EAEtD,OAAOL,aAAa,CAACC,IAAI;AAC3B;AAEA,OAAO,SAASK,gCAAgCA,CAC9CC,aAAqB,EACrBC,aAAsC,EACtC;EACA,MAAMC,cAAc,GAAGC,eAAe,CAAClC,cAAc,CAAC+B,aAAa,CAAC,CAACL,KAAK,CAAC;EAC3E,MAAMS,kBAAkB,GAAGF,cAAc,CAAC,GAAG,CAAC;EAE9C,MAAM;IAAEtB,SAAS;IAAE,GAAGyB;EAAK,CAAC,GAAGJ,aAAa;EAC5C,MAAMK,eAAe,GAAG3B,gBAAgB,CAACC,SAA0B,CAAC;EAEpE,IAAIA,SAAS,EAAE;IACb;IACA,IAAI,CAACwB,kBAAkB,CAACxB,SAAS,EAAE;MACjCwB,kBAAkB,CAACxB,SAAS,GAAG0B,eAAe;IAChD,CAAC,MAAM;MACL;MACA;MACA,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAc,CAAC;;MAE7C;MACA,KAAK,MAAMC,IAAI,IAAIL,kBAAkB,CAACxB,SAAS,EAAE;QAC/C;QACA,KAAK,MAAM,CAAC8B,QAAQ,EAAExB,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACqB,IAAI,CAAC,EAAE;UACpDF,cAAc,CAACI,GAAG,CAACD,QAAQ,EAAExB,KAAK,CAAC;QACrC;MACF;;MAEA;MACA,KAAK,MAAMuB,IAAI,IAAIH,eAAe,EAAE;QAClC,KAAK,MAAM,CAACI,QAAQ,EAAExB,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACqB,IAAI,CAAC,EAAE;UACpDF,cAAc,CAACI,GAAG,CAACD,QAAQ,EAAExB,KAAK,CAAC;QACrC;MACF;;MAEA;MACAkB,kBAAkB,CAACxB,SAAS,GAAGgC,KAAK,CAACC,IAAI,CACvCN,cAAc,EACd,CAAC,CAACG,QAAQ,EAAExB,KAAK,CAAC,MAAM;QACtB,CAACwB,QAAQ,GAAGxB;MACd,CAAC,CACH,CAAC;IACH;EACF;EAEAgB,cAAc,CAAC,GAAG,CAAC,GAAG;IACpB,GAAGA,cAAc,CAAC,GAAG,CAAC;IACtB,GAAGG;EACL,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGjB,8BAA8B,CAAC,CAAC;EAErD,MAAMkB,eAA8B,GAAG;IACrCrB,IAAI,EAAEoB,YAAY;IAClBnB,KAAK,EAAEO,cAAc;IACrBN,QAAQ,EAAE3B,cAAc,CAAC+B,aAAa,CAAC,CAACJ;EAC1C,CAAC;EAED,MAAMoB,QAAQ,GAAG7C,iCAAiC,CAAC4C,eAAe,CAAC;EAEnEvC,kBAAkB,CAACsC,YAAY,EAAEE,QAAQ,CAAC;EAE1C,OAAOF,YAAY;AACrB;AAEA,IAAIG,qBAAqB,GAAG,CAAC;AAE7B,SAASpB,8BAA8BA,CAAA,EAAG;EACxC,OAAO,MAAMoB,qBAAqB,EAAE,EAAE;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CACjCC,cAA8B,EAC9BC,cAA8B,EAC9B;EACA,MAAMC,sBAAsB,GAAGxB,8BAA8B,CAAC,CAAC;EAC/D,IAAIyB,2BAA2B;EAE/B,IAAIC,gBAAgB;EAEpB,QAAQJ,cAAc;IACpB,KAAKjD,cAAc,CAACsD,MAAM;MACxBD,gBAAgB,GAAGnD,gBAAgB,CACjCiD,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKlD,cAAc,CAACuD,SAAS;MAC3BF,gBAAgB,GAAGlD,mBAAmB,CACpCgD,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKlD,cAAc,CAACwD,MAAM;MACxBH,gBAAgB,GAAGjD,gBAAgB,CACjC+C,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKlD,cAAc,CAACyD,OAAO;MACzBJ,gBAAgB,GAAGhD,iBAAiB,CAClC8C,sBAAsB,EACtBD,cACF,CAAC;MACD;;IAEF;IACA,KAAKlD,cAAc,CAAC0D,MAAM;MAAE;QAC1BN,2BAA2B,GAAGzB,8BAA8B,CAAC,CAAC;QAE9D,MAAM;UAAEgC,gBAAgB;UAAEC;QAAkB,CAAC,GAAGrD,gBAAgB,CAC9D4C,sBAAsB,EACtBC,2BAA2B,EAC3BF,cACF,CAAC;QAEDG,gBAAgB,GAAGM,gBAAgB;QAEnC,MAAME,aAAa,GACjB5D,iCAAiC,CAAC2D,iBAAiB,CAAC;QAEtDtD,kBAAkB,CAAC8C,2BAA2B,EAAES,aAAa,CAAC;QAE9D;MACF;IACA,KAAK7D,cAAc,CAAC8D,UAAU;MAC5BT,gBAAgB,GAAG7C,mBAAmB,CACpC2C,sBAAsB,EACtBD,cACF,CAAC;MACD;EACJ;EAEA,MAAMa,kBAAkB,GACtB9D,iCAAiC,CAACoD,gBAAgB,CAAC;EAErD/C,kBAAkB,CAAC6C,sBAAsB,EAAEY,kBAAkB,CAAC;EAE9D,OAAO;IAAEZ,sBAAsB;IAAEC;EAA4B,CAAC;AAChE", "ignoreList": []}