{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_SafeAreaContext", "require", "keys", "for<PERSON>ach", "key", "enumerable", "get", "_SafeAreaView", "_InitialWindow", "_SafeArea"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAEb,IAAAC,gBAAA,GAAAC,OAAA;AAAAL,MAAA,CAAAM,IAAA,CAAAF,gBAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAN,OAAA,IAAAA,OAAA,CAAAM,GAAA,MAAAJ,gBAAA,CAAAI,GAAA;EAAAR,MAAA,CAAAC,cAAA,CAAAC,OAAA,EAAAM,GAAA;IAAAC,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAN,gBAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AACA,IAAAG,aAAA,GAAAN,OAAA;AAAAL,MAAA,CAAAM,IAAA,CAAAK,aAAA,EAAAJ,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAN,OAAA,IAAAA,OAAA,CAAAM,GAAA,MAAAG,aAAA,CAAAH,GAAA;EAAAR,MAAA,CAAAC,cAAA,CAAAC,OAAA,EAAAM,GAAA;IAAAC,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAC,aAAA,CAAAH,GAAA;IAAA;EAAA;AAAA;AACA,IAAAI,cAAA,GAAAP,OAAA;AAAAL,MAAA,CAAAM,IAAA,CAAAM,cAAA,EAAAL,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAN,OAAA,IAAAA,OAAA,CAAAM,GAAA,MAAAI,cAAA,CAAAJ,GAAA;EAAAR,MAAA,CAAAC,cAAA,CAAAC,OAAA,EAAAM,GAAA;IAAAC,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAE,cAAA,CAAAJ,GAAA;IAAA;EAAA;AAAA;AACA,IAAAK,SAAA,GAAAR,OAAA;AAAAL,MAAA,CAAAM,IAAA,CAAAO,SAAA,EAAAN,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAN,OAAA,IAAAA,OAAA,CAAAM,GAAA,MAAAK,SAAA,CAAAL,GAAA;EAAAR,MAAA,CAAAC,cAAA,CAAAC,OAAA,EAAAM,GAAA;IAAAC,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAG,SAAA,CAAAL,GAAA;IAAA;EAAA;AAAA", "ignoreList": []}