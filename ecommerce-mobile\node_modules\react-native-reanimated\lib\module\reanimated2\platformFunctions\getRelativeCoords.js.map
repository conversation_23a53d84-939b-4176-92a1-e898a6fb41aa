{"version": 3, "names": ["measure", "getRelativeCoords", "animatedRef", "absoluteX", "absoluteY", "parentCoords", "x", "pageX", "y", "pageY"], "sources": ["getRelativeCoords.ts"], "sourcesContent": ["'use strict';\nimport type { Component } from 'react';\nimport { measure } from './measure';\nimport type { AnimatedRef } from '../hook/commonTypes';\n\n/**\n * An object which contains relative coordinates.\n */\nexport interface ComponentCoords {\n  x: number;\n  y: number;\n}\n\n/**\n * Lets you determines the location on the screen, relative to the given view.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns) connected to the component you'd want to get the coordinates from.\n * @param absoluteX - A number which is an absolute x coordinate.\n * @param absoluteY - A number which is an absolute y coordinate.\n * @returns An object which contains relative coordinates - {@link ComponentCoords}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/getRelativeCoords\n */\nexport function getRelativeCoords(\n  animatedRef: AnimatedRef<Component>,\n  absoluteX: number,\n  absoluteY: number\n): ComponentCoords | null {\n  'worklet';\n  const parentCoords = measure(animatedRef);\n  if (parentCoords === null) {\n    return null;\n  }\n  return {\n    x: absoluteX - parentCoords.pageX,\n    y: absoluteY - parentCoords.pageY,\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,WAAW;;AAGnC;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,WAAmC,EACnCC,SAAiB,EACjBC,SAAiB,EACO;EACxB,SAAS;;EACT,MAAMC,YAAY,GAAGL,OAAO,CAACE,WAAW,CAAC;EACzC,IAAIG,YAAY,KAAK,IAAI,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAO;IACLC,CAAC,EAAEH,SAAS,GAAGE,YAAY,CAACE,KAAK;IACjCC,CAAC,EAAEJ,SAAS,GAAGC,YAAY,CAACI;EAC9B,CAAC;AACH", "ignoreList": []}