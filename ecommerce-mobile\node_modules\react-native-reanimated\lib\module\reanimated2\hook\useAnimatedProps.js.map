{"version": 3, "names": ["useAnimatedStyle", "shouldBeUseWeb", "useAnimatedPropsJS", "updater", "deps", "adapters", "useAnimatedPropsNative", "useAnimatedProps"], "sources": ["useAnimatedProps.ts"], "sourcesContent": ["'use strict';\nimport { useAnimatedStyle } from './useAnimatedStyle';\nimport type { DependencyList, UseAnimatedStyleInternal } from './commonTypes';\nimport { shouldBeUseWeb } from '../PlatformChecker';\nimport type { AnimatedPropsAdapterFunction } from '../commonTypes';\n\n// TODO: we should make sure that when useAP is used we are not assigning styles\n\ntype UseAnimatedProps = <Props extends object>(\n  updater: () => Partial<Props>,\n  dependencies?: DependencyList | null,\n  adapters?:\n    | AnimatedPropsAdapterFunction\n    | AnimatedPropsAdapterFunction[]\n    | null,\n  isAnimatedProps?: boolean\n) => Partial<Props>;\n\nfunction useAnimatedPropsJS<Props extends object>(\n  updater: () => Props,\n  deps?: DependencyList | null,\n  adapters?:\n    | AnimatedPropsAdapterFunction\n    | AnimatedPropsAdapterFunction[]\n    | null\n) {\n  return (useAnimatedStyle as UseAnimatedStyleInternal<Props>)(\n    updater,\n    deps,\n    adapters,\n    true\n  );\n}\n\nconst useAnimatedPropsNative = useAnimatedStyle;\n\n/**\n * Lets you create an animated props object which can be animated using shared values.\n *\n * @param updater - A function returning an object with properties you want to animate.\n * @param dependencies - An optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.\n * @param adapters - An optional function or array of functions allowing to adopt prop naming between JS and the native side.\n * @returns An animated props object which has to be passed to `animatedProps` property of an Animated component that you want to animate.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedProps\n */\nexport const useAnimatedProps: UseAnimatedProps = shouldBeUseWeb()\n  ? (useAnimatedPropsJS as UseAnimatedProps)\n  : useAnimatedPropsNative;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,cAAc,QAAQ,oBAAoB;;AAGnD;;AAYA,SAASC,kBAAkBA,CACzBC,OAAoB,EACpBC,IAA4B,EAC5BC,QAGQ,EACR;EACA,OAAQL,gBAAgB,CACtBG,OAAO,EACPC,IAAI,EACJC,QAAQ,EACR,IACF,CAAC;AACH;AAEA,MAAMC,sBAAsB,GAAGN,gBAAgB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,gBAAkC,GAAGN,cAAc,CAAC,CAAC,GAC7DC,kBAAkB,GACnBI,sBAAsB", "ignoreList": []}