{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "runOnUI", "prepareUIRegistry", "FrameCallbackRegistryJS", "constructor", "registerFrameCallback", "callback", "callbackId", "nextCallbackId", "global", "_frameCallbackRegistry", "unregisterFrameCallback", "manageStateFrameCallback", "state"], "sources": ["FrameCallbackRegistryJS.ts"], "sourcesContent": ["'use strict';\nimport { runOnUI } from '../core';\nimport type { FrameInfo } from './FrameCallbackRegistryUI';\nimport { prepareUIRegistry } from './FrameCallbackRegistryUI';\n\nexport default class FrameCallbackRegistryJS {\n  private nextCallbackId = 0;\n\n  constructor() {\n    prepareUIRegistry();\n  }\n\n  registerFrameCallback(callback: (frameInfo: FrameInfo) => void): number {\n    if (!callback) {\n      return -1;\n    }\n\n    const callbackId = this.nextCallbackId;\n    this.nextCallbackId++;\n\n    runOnUI(() => {\n      global._frameCallbackRegistry.registerFrameCallback(callback, callbackId);\n    })();\n\n    return callbackId;\n  }\n\n  unregisterFrameCallback(callbackId: number): void {\n    runOnUI(() => {\n      global._frameCallbackRegistry.unregisterFrameCallback(callbackId);\n    })();\n  }\n\n  manageStateFrameCallback(callbackId: number, state: boolean): void {\n    runOnUI(() => {\n      global._frameCallbackRegistry.manageStateFrameCallback(callbackId, state);\n    })();\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,OAAO,QAAQ,SAAS;AAEjC,SAASC,iBAAiB,QAAQ,2BAA2B;AAE7D,eAAe,MAAMC,uBAAuB,CAAC;EAG3CC,WAAWA,CAAA,EAAG;IAAAxB,eAAA,yBAFW,CAAC;IAGxBsB,iBAAiB,CAAC,CAAC;EACrB;EAEAG,qBAAqBA,CAACC,QAAwC,EAAU;IACtE,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,CAAC,CAAC;IACX;IAEA,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc;IACtC,IAAI,CAACA,cAAc,EAAE;IAErBP,OAAO,CAAC,MAAM;MACZQ,MAAM,CAACC,sBAAsB,CAACL,qBAAqB,CAACC,QAAQ,EAAEC,UAAU,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC;IAEJ,OAAOA,UAAU;EACnB;EAEAI,uBAAuBA,CAACJ,UAAkB,EAAQ;IAChDN,OAAO,CAAC,MAAM;MACZQ,MAAM,CAACC,sBAAsB,CAACC,uBAAuB,CAACJ,UAAU,CAAC;IACnE,CAAC,CAAC,CAAC,CAAC;EACN;EAEAK,wBAAwBA,CAACL,UAAkB,EAAEM,KAAc,EAAQ;IACjEZ,OAAO,CAAC,MAAM;MACZQ,MAAM,CAACC,sBAAsB,CAACE,wBAAwB,CAACL,UAAU,EAAEM,KAAK,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC;EACN;AACF", "ignoreList": []}