/**
 * @license lucide-react-native v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

'use strict';

var createLucideIcon = require('../createLucideIcon.js');

const SquareArrowUpRight = createLucideIcon("SquareArrowUpRight", [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "M8 8h8v8", key: "b65dnt" }],
  ["path", { d: "m8 16 8-8", key: "13b9ih" }]
]);

module.exports = SquareArrowUpRight;
//# sourceMappingURL=square-arrow-up-right.js.map
