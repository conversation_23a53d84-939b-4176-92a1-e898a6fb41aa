{"version": 3, "names": ["React", "getCurrentReactOwner", "ReactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactCurrentOwner", "current", "isReactRendering", "isFirstReactRender", "current<PERSON>wner", "alternate"], "sourceRoot": "../../src", "sources": ["reactUtils.ts"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,MAAMC,oBAAoB;EACxB;EACAF,KAAK,CAACG,kDAAkD;EACxD;EACAH,KAAK,CAACI,+DAA+D;EACvE,OAAOF,oBAAoB,EAAEG,iBAAiB,EAAEC,OAAO;AACzD;AAEA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,OAAO,CAAC,CAACN,oBAAoB,CAAC,CAAC;AACjC;AAEA,OAAO,SAASO,kBAAkBA,CAAA,EAAG;EACnC,MAAMC,YAAY,GAAGR,oBAAoB,CAAC,CAAC;EAC3C;EACA;EACA,OAAOQ,YAAY,IAAI,CAACA,YAAY,EAAEC,SAAS;AACjD", "ignoreList": []}